/**
 * Caching Utility for Billing Operations
 * Implements in-memory caching to reduce API calls and improve performance
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheEntry<any>>();
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * Set a cache entry with TTL
   */
  set<T>(key: string, data: T, ttlMs: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
    
    console.log(`📦 Cache SET: ${key} (TTL: ${ttlMs}ms)`);
  }

  /**
   * Get a cache entry if it exists and hasn't expired
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      console.log(`📦 Cache MISS: ${key}`);
      return null;
    }

    const now = Date.now();
    const age = now - entry.timestamp;

    if (age > entry.ttl) {
      console.log(`📦 Cache EXPIRED: ${key} (age: ${age}ms, ttl: ${entry.ttl}ms)`);
      this.cache.delete(key);
      return null;
    }

    console.log(`📦 Cache HIT: ${key} (age: ${age}ms)`);
    return entry.data as T;
  }

  /**
   * Delete a specific cache entry
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      console.log(`📦 Cache DELETE: ${key}`);
    }
    return deleted;
  }

  /**
   * Clear all cache entries for a shop
   */
  clearShop(shop: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.cache.keys()) {
      if (key.includes(shop)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    console.log(`📦 Cache CLEAR SHOP: ${shop} (${keysToDelete.length} entries)`);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`📦 Cache CLEAR ALL: ${size} entries`);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      const age = now - entry.timestamp;
      if (age > entry.ttl) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      console.log(`📦 Cache CLEANUP: Removed ${keysToDelete.length} expired entries`);
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }

  /**
   * Destroy the cache and cleanup interval
   */
  destroy(): void {
    clearInterval(this.cleanupInterval);
    this.clear();
  }
}

// Global cache instance
const billingCache = new MemoryCache();

/**
 * Cache keys for different types of billing data
 */
export const CacheKeys = {
  BILLING_STATUS: (shop: string) => `billing:status:${shop}`,
  SUBSCRIPTION_DATA: (shop: string) => `billing:subscription:${shop}`,
  PURCHASE_DATA: (shop: string) => `billing:purchases:${shop}`,
  BILLING_PLANS: () => `billing:plans`,
  USAGE_STATS: (shop: string, days: number) => `billing:usage:${shop}:${days}`,
  SHOPIFY_SUBSCRIPTION: (shop: string) => `shopify:subscription:${shop}`,
  SHOPIFY_PURCHASES: (shop: string) => `shopify:purchases:${shop}`,
  SUBSCRIPTION_UPDATE_FLAG: (shop: string) => `billing:update_flag:${shop}`
};

/**
 * Cache TTL values in milliseconds
 */
export const CacheTTL = {
  BILLING_STATUS: 2 * 60 * 1000,      // 2 minutes
  SUBSCRIPTION_DATA: 5 * 60 * 1000,   // 5 minutes
  PURCHASE_DATA: 3 * 60 * 1000,       // 3 minutes
  BILLING_PLANS: 30 * 60 * 1000,      // 30 minutes
  USAGE_STATS: 10 * 60 * 1000,        // 10 minutes
  SHOPIFY_DATA: 1 * 60 * 1000         // 1 minute
};

/**
 * Cached wrapper for billing status checks
 */
export async function getCachedBillingStatus<T>(
  shop: string,
  fetchFunction: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.BILLING_STATUS(shop);
  
  // Try to get from cache first
  const cached = billingCache.get<T>(cacheKey);
  if (cached !== null) {
    return cached;
  }

  // Fetch fresh data
  console.log(`🔄 Fetching fresh billing status for shop: ${shop}`);
  const data = await fetchFunction();
  
  // Cache the result
  billingCache.set(cacheKey, data, CacheTTL.BILLING_STATUS);
  
  return data;
}

/**
 * Cached wrapper for subscription data
 */
export async function getCachedSubscriptionData<T>(
  shop: string,
  fetchFunction: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.SUBSCRIPTION_DATA(shop);
  
  const cached = billingCache.get<T>(cacheKey);
  if (cached !== null) {
    return cached;
  }

  console.log(`🔄 Fetching fresh subscription data for shop: ${shop}`);
  const data = await fetchFunction();
  
  billingCache.set(cacheKey, data, CacheTTL.SUBSCRIPTION_DATA);
  
  return data;
}

/**
 * Cached wrapper for purchase data
 */
export async function getCachedPurchaseData<T>(
  shop: string,
  fetchFunction: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.PURCHASE_DATA(shop);
  
  const cached = billingCache.get<T>(cacheKey);
  if (cached !== null) {
    return cached;
  }

  console.log(`🔄 Fetching fresh purchase data for shop: ${shop}`);
  const data = await fetchFunction();
  
  billingCache.set(cacheKey, data, CacheTTL.PURCHASE_DATA);
  
  return data;
}

/**
 * Cached wrapper for billing plans
 */
export async function getCachedBillingPlans<T>(
  fetchFunction: () => Promise<T>
): Promise<T> {
  const cacheKey = CacheKeys.BILLING_PLANS();
  
  const cached = billingCache.get<T>(cacheKey);
  if (cached !== null) {
    return cached;
  }

  console.log(`🔄 Fetching fresh billing plans`);
  const data = await fetchFunction();
  
  billingCache.set(cacheKey, data, CacheTTL.BILLING_PLANS);
  
  return data;
}

/**
 * Invalidate cache entries when billing data changes
 */
export function invalidateBillingCache(shop: string, type?: 'subscription' | 'purchase' | 'all'): void {
  console.log(`🗑️ Invalidating billing cache for shop: ${shop}, type: ${type || 'all'}`);
  
  switch (type) {
    case 'subscription':
      billingCache.delete(CacheKeys.BILLING_STATUS(shop));
      billingCache.delete(CacheKeys.SUBSCRIPTION_DATA(shop));
      billingCache.delete(CacheKeys.SHOPIFY_SUBSCRIPTION(shop));
      break;
      
    case 'purchase':
      billingCache.delete(CacheKeys.BILLING_STATUS(shop));
      billingCache.delete(CacheKeys.PURCHASE_DATA(shop));
      billingCache.delete(CacheKeys.SHOPIFY_PURCHASES(shop));
      break;
      
    default:
      billingCache.clearShop(shop);
      break;
  }
}

/**
 * Preload cache with commonly accessed data
 */
export async function preloadBillingCache(shop: string, billingService: any): Promise<void> {
  try {
    console.log(`🚀 Preloading billing cache for shop: ${shop}`);
    
    // Preload billing status
    const billingStatusPromise = getCachedBillingStatus(shop, () => 
      billingService.hasActiveBilling()
    );
    
    // Preload subscription data
    const subscriptionPromise = getCachedSubscriptionData(shop, () => 
      billingService.getCurrentSubscription()
    );
    
    // Preload purchase data
    const purchasePromise = getCachedPurchaseData(shop, () => 
      billingService.getOneTimePurchases()
    );
    
    // Wait for all preloads to complete
    await Promise.allSettled([
      billingStatusPromise,
      subscriptionPromise,
      purchasePromise
    ]);
    
    console.log(`✅ Billing cache preloaded for shop: ${shop}`);
  } catch (error) {
    console.error(`❌ Failed to preload billing cache for shop ${shop}:`, error);
  }
}

/**
 * Get cache statistics
 */
export function getCacheStats(): { size: number; keys: string[] } {
  return billingCache.getStats();
}

/**
 * Clear all cache (for testing/debugging)
 */
export function clearAllCache(): void {
  billingCache.clear();
}

/**
 * Debounced cache invalidation to prevent excessive cache clearing
 */
const debouncedInvalidation = new Map<string, NodeJS.Timeout>();

export function debouncedInvalidateBillingCache(
  shop: string,
  type?: 'subscription' | 'purchase' | 'all',
  delayMs: number = 1000
): void {
  const key = `${shop}:${type || 'all'}`;

  // Clear existing timeout
  const existingTimeout = debouncedInvalidation.get(key);
  if (existingTimeout) {
    clearTimeout(existingTimeout);
  }

  // Set new timeout
  const timeout = setTimeout(() => {
    invalidateBillingCache(shop, type);
    debouncedInvalidation.delete(key);
  }, delayMs);

  debouncedInvalidation.set(key, timeout);
}

/**
 * Set a flag indicating subscription status has been updated recently
 * This helps other pages detect they need to refresh their billing data
 */
export function setSubscriptionUpdateFlag(shop: string): void {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  const timestamp = Date.now();

  // Set flag with 5 minute expiry - long enough for pages to detect the change
  billingCache.set(cacheKey, timestamp, 5 * 60 * 1000);
  console.log(`🚩 Subscription update flag set for shop: ${shop} at ${new Date(timestamp).toISOString()}`);
}

/**
 * Check if subscription status has been updated recently
 * Returns the timestamp of the last update, or null if no recent update
 */
export function getSubscriptionUpdateFlag(shop: string): number | null {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  const timestamp = billingCache.get<number>(cacheKey);

  if (timestamp !== null) {
    console.log(`🚩 Subscription update flag found for shop: ${shop} at ${new Date(timestamp).toISOString()}`);
  }

  return timestamp;
}

/**
 * Clear the subscription update flag
 */
export function clearSubscriptionUpdateFlag(shop: string): void {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  billingCache.delete(cacheKey);
  console.log(`🚩 Subscription update flag cleared for shop: ${shop}`);
}

export default billingCache;
