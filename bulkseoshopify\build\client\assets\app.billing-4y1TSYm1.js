import{j as e}from"./jsx-runtime-0DLF9kdB.js";import{r as h}from"./index-BJYSoprK.js";import{B as N}from"./button-RlsfHhb_.js";import{P as U}from"./PricingSelection-BaVbg180.js";import{a as B,u as T}from"./components-C2v4lmCU.js";import{T as R}from"./TitleBar-DFMSJ8Yc.js";import"./badge-CSt7rrAu.js";import"./index-DI88vBYx.js";import"./useAppBridge-Bj34gXAL.js";import"./proxy-CdGEizrq.js";import"./index-Ci0627_k.js";function M({balance:s,history:t,hasSubscription:c}){const x=i=>new Date(i).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),u=i=>{switch(i){case"PURCHASE":return"💳";case"USAGE":return"🔧";case"REFUND":return"↩️";case"BONUS":return"🎁";default:return"📝"}},m=i=>{switch(i){case"PURCHASE":return"text-green-400";case"USAGE":return"text-blue-400";case"REFUND":return"text-yellow-400";case"BONUS":return"text-purple-400";default:return"text-gray-400"}};return c?e.jsx("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🚀"}),e.jsx("h2",{className:"text-3xl font-bold mb-2",children:"Unlimited Access"}),e.jsx("p",{className:"text-white/70 text-lg",children:"You have an active subscription with unlimited product optimizations"}),e.jsx("div",{className:"mt-6 inline-flex items-center bg-green-500/20 border border-green-500/40 rounded-2xl px-6 py-3",children:e.jsx("div",{className:"text-green-300 font-bold",children:"✅ Active Subscription"})})]})}):e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Optimization Credits"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2 text-blue-400",children:s.totalCredits}),e.jsx("div",{className:"text-white/70",children:"Total Purchased"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2 text-orange-400",children:s.usedCredits}),e.jsx("div",{className:"text-white/70",children:"Used"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2 text-green-400",children:s.remainingCredits}),e.jsx("div",{className:"text-white/70",children:"Remaining"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm text-white/70 mb-2",children:[e.jsx("span",{children:"Credits Used"}),e.jsxs("span",{children:[s.usedCredits," / ",s.totalCredits]})]}),e.jsx("div",{className:"w-full bg-white/10 rounded-full h-3",children:e.jsx("div",{className:"bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300",style:{width:s.totalCredits>0?`${Math.min(100,s.usedCredits/s.totalCredits*100)}%`:"0%"}})})]}),e.jsx("div",{className:"bg-white/5 rounded-2xl p-4",children:e.jsxs("div",{className:"text-sm text-white/70",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{children:"💡 Each credit = 1 product optimization"}),e.jsx("span",{children:"💰 $0.10 per credit"})]}),e.jsxs("div",{className:"text-xs text-white/50",children:["Last updated: ",x(s.lastUpdated)]})]})})]}),t.length>0&&e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Credit History"}),e.jsx("div",{className:"space-y-4 max-h-96 overflow-y-auto",children:t.map((i,o)=>e.jsxs("div",{className:"flex items-center justify-between py-3 border-b border-white/10 last:border-b-0",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"text-2xl",children:u(i.type)}),e.jsxs("div",{children:[e.jsx("div",{className:"font-semibold",children:i.description}),e.jsxs("div",{className:"text-sm text-white/60",children:[x(i.date),i.referenceId&&e.jsxs("span",{className:"ml-2 text-white/40",children:["#",i.referenceId.substring(0,8)]})]})]})]}),e.jsxs("div",{className:`font-bold text-lg ${m(i.type)}`,children:[i.amount>0?"+":"",i.amount]})]},o))})]}),s.totalCredits===0&&e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8 text-center",children:[e.jsx("div",{className:"text-6xl mb-4",children:"💳"}),e.jsx("h3",{className:"text-2xl font-bold mb-2",children:"No Credits Yet"}),e.jsx("p",{className:"text-white/70 mb-6",children:"Purchase optimization credits to start improving your products"}),e.jsx("div",{className:"text-sm text-white/60",children:"💡 Tip: Each credit optimizes one product for just $0.10"})]})]})}function O({subscription:s,plan:t,monthlyUsage:c,recentPurchases:x=[],plans:u=[],csrfToken:m,creditBalance:i,creditHistory:o=[]}){const d=B(),[w,f]=h.useState(!1),[j,y]=h.useState(!1),[E,D]=h.useState(!1);h.useEffect(()=>{const r=()=>{console.log("👆 User interaction detected"),D(!0)};return document.addEventListener("click",r),document.addEventListener("keydown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("click",r),document.removeEventListener("keydown",r),document.removeEventListener("touchstart",r)}},[]),h.useEffect(()=>{console.log("🔍 ModernBillingDashboard Debug:",{subscription:s,plan:{id:t==null?void 0:t.id,name:t==null?void 0:t.name,price:t==null?void 0:t.price,type:t==null?void 0:t.type},hasActiveSubscription:g(),statusDisplay:S()})},[s,t]),h.useEffect(()=>{if(d.data&&typeof d.data=="object"){const r=d.data;r.error?alert(`Error: ${r.error}`):r.success&&(r.redirectTo?window.location.href=r.redirectTo:window.location.reload())}y(d.state==="loading"||d.state==="submitting")},[d.data,d.state]);const A=()=>{if(console.log("🚨 handleCancelSubscription called - this should only happen when user clicks Cancel button"),console.log("🔍 Call stack:",new Error().stack),console.log("🔍 User interacted:",E),!E){console.log("🛡️ BLOCKING automatic cancellation - user has not interacted with page yet");return}if(!s||d.state==="submitting"){console.log("❌ Cannot cancel - no subscription or already submitting");return}const r=`Are you sure you want to cancel your ${(t==null?void 0:t.name)||"subscription"}?`;if(console.log("🤔 Showing confirmation dialog:",r),confirm(r)){console.log("✅ User confirmed cancellation");const l=new FormData;l.append("action","cancel_subscription"),l.append("subscriptionId",s.id),m&&l.append("csrfToken",m),d.submit(l,{method:"POST"})}else console.log("❌ User cancelled the cancellation")},S=()=>{if(!s)return{text:"No Active Plan",color:"text-gray-400"};switch(s.status){case"ACTIVE":return g()?{text:"Active Subscription",color:"text-green-400"}:{text:"Active",color:"text-green-400"};case"PENDING":return{text:"Pending",color:"text-yellow-400"};case"CANCELLED":return{text:"Cancelled",color:"text-red-400"};case"EXPIRED":return{text:"Expired",color:"text-red-400"};default:return{text:s.status,color:"text-gray-400"}}},b=r=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(r),P=()=>{var r,l;if(s&&s.status==="ACTIVE"){if(t!=null&&t.name)return t.name;const n=(r=s.lineItems)==null?void 0:r[0];if((l=n==null?void 0:n.plan)!=null&&l.pricingDetails){const a=n.plan.pricingDetails.interval;if(a==="ANNUAL")return"Annual Plan";if(a==="EVERY_30_DAYS")return"Monthly Plan"}return"Active Subscription"}return(t==null?void 0:t.name)||"No Plan"},L=()=>{var r,l,n,a;if(s&&s.status==="ACTIVE"){if(t!=null&&t.price)return b(t.price);const v=(r=s.lineItems)==null?void 0:r[0];if((a=(n=(l=v==null?void 0:v.plan)==null?void 0:l.pricingDetails)==null?void 0:n.price)!=null&&a.amount){const I=parseFloat(v.plan.pricingDetails.price.amount);return b(I)}return"Active"}return t!=null&&t.price?b(t.price):"$0"},k=()=>{var r,l;if(s&&s.status==="ACTIVE"){if(t!=null&&t.type)return t.type==="pay_per_use"?"Per Use":t.type==="annual"?"Annual":"Monthly";const n=(r=s.lineItems)==null?void 0:r[0];if((l=n==null?void 0:n.plan)!=null&&l.pricingDetails){const a=n.plan.pricingDetails.interval;if(a==="ANNUAL")return"Annual";if(a==="EVERY_30_DAYS")return"Monthly"}return"Subscription"}return(t==null?void 0:t.type)==="pay_per_use"?"Per Use":"Monthly"},g=()=>{var l,n;if(!s||s.status!=="ACTIVE")return!1;if(t)return t.type!=="pay_per_use";const r=(l=s.lineItems)==null?void 0:l[0];if((n=r==null?void 0:r.plan)!=null&&n.pricingDetails){const a=r.plan.pricingDetails.interval;return a==="ANNUAL"||a==="EVERY_30_DAYS"}return!1},C=r=>new Date(r).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});if(w)return e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsx("div",{className:"mb-8",children:e.jsx(N,{onClick:()=>f(!1),className:"mb-4 bg-white/10 border border-white/20 text-white hover:bg-white/20",children:"← Back to Billing Dashboard"})}),e.jsx(U,{plans:u,selectedPlan:t==null?void 0:t.id,csrfToken:m,hasActiveSubscription:g()})]});const p=S();return console.log("🔍 ModernBillingDashboard Debug:",{subscription:s?{id:s.id,status:s.status,lineItems:s.lineItems}:null,plan:t?{id:t.id,name:t.name,price:t.price,type:t.type}:null,hasActiveSubscription:g(),statusDisplay:p}),e.jsxs("div",{className:"max-w-6xl mx-auto space-y-8",children:[e.jsxs("div",{className:"bg-yellow-500/20 border border-yellow-500/40 rounded-2xl p-4 text-center",children:[e.jsx("div",{className:"text-yellow-300 font-bold text-lg",children:"🧪 TEST MODE ENABLED"}),e.jsx("div",{className:"text-yellow-200 text-sm mt-1",children:"All payments are in test mode - no real charges will be made"})]}),e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-3xl font-bold mb-2",children:"Current Plan"}),e.jsx("p",{className:"text-white/70",children:"Manage your subscription and billing preferences"})]}),e.jsx("div",{className:`text-lg font-semibold ${p.color}`,children:p.text})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2",children:P()}),e.jsx("div",{className:"text-white/70",children:"Current Plan"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2",children:L()}),e.jsx("div",{className:"text-white/70",children:k()})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-4xl font-bold mb-2",children:(c==null?void 0:c.productsOptimized)||0}),e.jsx("div",{className:"text-white/70",children:"Products Optimized"})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 mt-8",children:[e.jsx(N,{onClick:()=>f(!0),className:"bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl",disabled:j,children:t?"Change Plan":"Choose Plan"}),(t==null?void 0:t.id)==="pay_per_use"&&e.jsx(N,{onClick:()=>{y(!0),typeof window<"u"&&(window.location.href=window.location.pathname+"?refresh=true")},className:"bg-transparent border-2 border-blue-400/60 text-blue-400 hover:bg-blue-400 hover:text-black font-bold py-3 px-8 rounded-2xl",disabled:j,children:j?"Refreshing...":"Refresh Subscription"}),s&&s.status==="ACTIVE"&&e.jsx(N,{onClick:A,className:"bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl",disabled:j,children:"Cancel Subscription"})]})]}),c&&e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Usage This Month"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-3xl font-bold mb-2",children:b(c.totalSpent)}),e.jsx("div",{className:"text-white/70",children:"Total Spent"})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-3xl font-bold mb-2",children:c.lastOptimization?C(c.lastOptimization):"Never"}),e.jsx("div",{className:"text-white/70",children:"Last Optimization"})]})]})]}),i&&e.jsx(M,{balance:i,history:o,hasSubscription:g()}),x.length>0&&e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-6",children:"Recent Purchases"}),e.jsx("div",{className:"space-y-4",children:x.slice(0,5).map(r=>e.jsxs("div",{className:"flex justify-between items-center py-4 border-b border-white/10 last:border-b-0",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"font-semibold",children:[r.productCount," Products Optimized"]}),e.jsx("div",{className:"text-white/70 text-sm",children:C(r.date)})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"font-bold",children:b(r.amount)}),e.jsx("div",{className:`text-sm ${r.status==="ACTIVE"?"text-green-400":"text-gray-400"}`,children:r.status})]})]},r.id))})]})]})}function q(){const{subscription:s,plan:t,monthlyUsage:c,recentPurchases:x,plans:u,creditBalance:m,creditHistory:i,success:o,error:d,csrfToken:w}=T();return h.useEffect(()=>{console.log("🏦 BillingPage mounted with success parameter:",o),console.log("🔍 URL:",window.location.href),console.log("🔍 Subscription:",s),console.log("🔍 Plan:",t);const f=window.getEventListeners?window.getEventListeners(document):"getEventListeners not available";console.log("🔍 Document event listeners:",f)},[o,s,t]),e.jsxs(e.Fragment,{children:[e.jsx(R,{title:"AI BULK SEO Billing"}),o&&e.jsx("div",{className:"mx-6 mb-6",children:e.jsxs("div",{className:"bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm shadow-2xl",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:"Payment Successful!"}),e.jsx("p",{className:"text-white/70 text-sm",children:"Your payment has been processed and credits have been added to your account."})]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-white/70",children:[e.jsx("div",{className:"w-1.5 h-1.5 bg-white rounded-full animate-pulse"}),e.jsx("span",{children:"Ready to start optimizing your products"})]})]})}),d&&e.jsx("div",{className:"mx-6 mb-6",children:e.jsxs("div",{className:"bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center",children:e.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:"Payment Error"}),e.jsx("p",{className:"text-white/70 text-sm",children:"There was an issue processing your payment. Please try again or contact support."})]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-white/70",children:[e.jsx("div",{className:"w-1.5 h-1.5 bg-white rounded-full"}),e.jsx("span",{children:"Please check your payment details and try again"})]})]})}),e.jsxs("div",{className:"min-h-screen bg-black text-white",children:[e.jsx("div",{className:"bg-black py-20 px-6",children:e.jsx("div",{className:"max-w-6xl mx-auto text-center",children:e.jsxs("div",{className:"flex flex-col items-center mb-12",children:[e.jsx("img",{src:"/logo.png",alt:"AI BULK SEO Logo",className:"w-16 h-16 mb-6 rounded-2xl shadow-2xl",style:{filter:"brightness(1.1) contrast(1.1)"}}),e.jsx("h1",{style:{fontSize:"clamp(3rem, 8vw, 6rem)",fontWeight:900,lineHeight:.9,letterSpacing:"-0.05em",marginBottom:"1rem",color:"white"},children:"BILLING"}),e.jsx("p",{style:{fontSize:"clamp(1.25rem, 3vw, 1.75rem)",fontWeight:300,color:"#cbd5e1",maxWidth:"40rem",margin:"0 auto"},children:"Manage your AI BULK SEO subscription and usage"})]})})}),e.jsx("div",{className:"px-6 pb-20",children:e.jsx(O,{subscription:s,plan:t,monthlyUsage:c,recentPurchases:x,plans:u,creditBalance:m,creditHistory:i,csrfToken:w})})]})]})}export{q as default};
