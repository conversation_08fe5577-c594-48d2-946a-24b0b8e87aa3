import{r as m,R as n}from"./index-BJYSoprK.js";import{u as p}from"./use-is-after-initial-mount-MUb1fdtO.js";import{g as l}from"./Button-3T1ZHLBP.js";import{a as b}from"./Page-Dli0YmGf.js";const v=m.createContext(!1);var h={ShadowBevel:"Polaris-ShadowBevel"};function f(e){const{as:r="div",bevel:o=!0,borderRadius:a,boxShadow:s,children:d,zIndex:i="0"}=e,u=r;return n.createElement(u,{className:h.ShadowBevel,style:{"--pc-shadow-bevel-z-index":i,...l("shadow-bevel","content",c(o,t=>t?'""':"none")),...l("shadow-bevel","box-shadow",c(o,t=>t?`var(--p-shadow-${s})`:"none")),...l("shadow-bevel","border-radius",c(o,t=>t?`var(--p-border-radius-${a})`:"var(--p-border-radius-0)"))}},d)}function c(e,r){return typeof e=="boolean"?r(e):Object.fromEntries(Object.entries(e).map(([o,a])=>[o,r(a)]))}const g=({children:e,background:r="bg-surface",padding:o={xs:"400"},roundedAbove:a="sm"})=>{const s=p(),d="300",i=!!s[`${a}Up`];return n.createElement(v.Provider,{value:!0},n.createElement(f,{boxShadow:"100",borderRadius:i?d:"0",zIndex:"32"},n.createElement(b,{background:r,padding:o,overflowX:"clip",overflowY:"clip",minHeight:"100%"},e)))};export{g as C};
