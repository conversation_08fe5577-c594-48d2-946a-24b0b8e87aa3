import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full px-4 py-2 text-xs font-bold w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-2 [&>svg]:pointer-events-none transition-all duration-300 transform hover:scale-110 shadow-lg uppercase tracking-wider",
  {
    variants: {
      variant: {
        default:
          "bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",
        secondary:
          "bg-gray-100 text-black hover:bg-gray-200 shadow-gray-200 hover:shadow-gray-300 border border-gray-300",
        destructive:
          "bg-white text-black border-2 border-black hover:bg-black hover:text-white shadow-black/20 hover:shadow-black/40",
        outline:
          "border-2 border-black bg-white text-black hover:bg-black hover:text-white shadow-black/10 hover:shadow-black/30",
        success:
          "bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",
        warning:
          "bg-white text-black border border-gray-400 hover:bg-gray-100 shadow-gray-200 hover:shadow-gray-300",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<"span"> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "span"

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }
