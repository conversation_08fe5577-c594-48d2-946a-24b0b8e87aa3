/**
 * Unified Authentication Service
 * Handles all authentication, session management, and security concerns
 */

import { redirect } from "@remix-run/node";
import { authenticate as shopifyAuthenticate } from "../shopify.server";
import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";
import { securityService } from "./security.server";

export interface AuthenticatedSession {
  admin: any;
  session: {
    id: string;
    shop: string;
    accessToken: string;
    expires?: Date;
    userId?: string;
    email?: string;
    isOnline: boolean;
    scope?: string;
  };
}

export interface SessionValidationResult {
  isValid: boolean;
  session?: AuthenticatedSession['session'];
  error?: string;
  shouldRedirect?: boolean;
  redirectUrl?: string;
}

/**
 * Enhanced authentication service with comprehensive session management
 */
export class AuthService {
  private static instance: AuthService;

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Authenticate admin request with enhanced validation
   */
  async authenticateAdmin(request: Request, context?: ErrorContext): Promise<AuthenticatedSession> {
    try {
      // Apply rate limiting for authentication attempts
      const clientIP = request.headers.get('x-forwarded-for') || 'unknown';
      await applyRateLimit(RATE_LIMITERS.AUTH_LOGIN(clientIP), context);

      // Use Shopify's authentication
      const { admin, session } = await shopifyAuthenticate.admin(request);

      // Enhanced session validation
      const validationResult = await this.validateSession(session, context);
      
      if (!validationResult.isValid) {
        if (validationResult.shouldRedirect) {
          throw redirect(validationResult.redirectUrl || '/auth/login');
        }
        throw createError('AUTH_INVALID_SESSION', context);
      }

      // Update last access time
      await this.updateSessionAccess(session.shop);

      console.log(`✅ Authentication successful for shop: ${session.shop}`);
      
      return { admin, session: validationResult.session! };

    } catch (error) {
      console.error('❌ Authentication failed:', error);
      
      // Log authentication failure
      await logError(
        error instanceof Error ? error : new Error('Authentication failed'),
        { ...context, action: 'authenticate_admin' }
      );

      // If it's already a redirect, re-throw it
      if (error instanceof Response && error.status >= 300 && error.status < 400) {
        throw error;
      }

      // Otherwise redirect to login
      throw redirect('/auth/login');
    }
  }

  /**
   * Validate session with comprehensive checks
   */
  async validateSession(session: any, context?: ErrorContext): Promise<SessionValidationResult> {
    try {
      // Basic session structure validation
      if (!session || !session.shop || !session.accessToken) {
        return {
          isValid: false,
          error: 'Invalid session structure',
          shouldRedirect: true,
          redirectUrl: '/auth/login'
        };
      }

      // Check session expiration (with 5 minute buffer to prevent premature expiration)
      if (session.expires && new Date(session.expires.getTime() + 5 * 60 * 1000) < new Date()) {
        console.log(`⚠️ Session expired for shop: ${session.shop}`);

        // Clean up expired session
        await this.cleanupExpiredSession(session.shop);

        return {
          isValid: false,
          error: 'Session expired',
          shouldRedirect: true,
          redirectUrl: '/auth/login'
        };
      }

      // Validate session in database
      const dbSession = await db.session.findUnique({
        where: { shop: session.shop }
      });

      if (!dbSession) {
        return {
          isValid: false,
          error: 'Session not found in database',
          shouldRedirect: true,
          redirectUrl: '/auth/login'
        };
      }

      // Update session access time and token if needed
      if (dbSession.accessToken !== session.accessToken) {
        console.log(`🔄 Updating access token for shop: ${session.shop}`);
        await db.session.update({
          where: { shop: session.shop },
          data: {
            accessToken: session.accessToken,
            updatedAt: new Date()
          }
        });
      }

      // Validate required scopes
      const requiredScopes = ['read_products', 'write_products'];
      const sessionScopes = session.scope?.split(',') || [];
      const missingScopes = requiredScopes.filter(scope => !sessionScopes.includes(scope));
      
      if (missingScopes.length > 0) {
        console.warn(`⚠️ Missing required scopes for shop ${session.shop}: ${missingScopes.join(', ')}`);
        return {
          isValid: false,
          error: `Missing required permissions: ${missingScopes.join(', ')}`,
          shouldRedirect: true,
          redirectUrl: '/auth/login'
        };
      }

      return {
        isValid: true,
        session: {
          id: dbSession.id,
          shop: session.shop,
          accessToken: session.accessToken,
          expires: session.expires ? new Date(session.expires) : undefined,
          userId: dbSession.userId?.toString(),
          email: dbSession.email || undefined,
          isOnline: dbSession.isOnline,
          scope: session.scope
        }
      };

    } catch (error) {
      console.error('❌ Session validation error:', error);
      await logError(
        error instanceof Error ? error : new Error('Session validation failed'),
        { ...context, action: 'validate_session' }
      );

      return {
        isValid: false,
        error: 'Session validation failed',
        shouldRedirect: true,
        redirectUrl: '/auth/login'
      };
    }
  }

  /**
   * Update session last access time
   */
  private async updateSessionAccess(shop: string): Promise<void> {
    try {
      await db.session.update({
        where: { shop },
        data: { updatedAt: new Date() }
      });
    } catch (error) {
      console.error('❌ Failed to update session access time:', error);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Clean up expired session
   */
  private async cleanupExpiredSession(shop: string): Promise<void> {
    try {
      await db.session.deleteMany({
        where: {
          shop,
          expires: {
            lt: new Date()
          }
        }
      });
      console.log(`🧹 Cleaned up expired sessions for shop: ${shop}`);
    } catch (error) {
      console.error('❌ Failed to cleanup expired session:', error);
    }
  }

  /**
   * Clean up all expired sessions (maintenance task)
   */
  async cleanupAllExpiredSessions(): Promise<void> {
    try {
      const result = await db.session.deleteMany({
        where: {
          expires: {
            lt: new Date()
          }
        }
      });
      
      if (result.count > 0) {
        console.log(`🧹 Cleaned up ${result.count} expired sessions`);
      }
    } catch (error) {
      console.error('❌ Failed to cleanup expired sessions:', error);
    }
  }

  /**
   * Logout user and clean up session
   */
  async logout(shop: string): Promise<void> {
    try {
      await db.session.deleteMany({
        where: { shop }
      });
      console.log(`👋 User logged out for shop: ${shop}`);
    } catch (error) {
      console.error('❌ Failed to logout user:', error);
      throw createError('INTERNAL_SERVER_ERROR', { shop, action: 'logout' });
    }
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();

// Schedule cleanup of expired sessions every hour
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    authService.cleanupAllExpiredSessions();
  }, 60 * 60 * 1000); // 1 hour
}
