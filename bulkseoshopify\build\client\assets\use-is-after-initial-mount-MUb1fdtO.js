import{g as we,r as U}from"./index-BJYSoprK.js";const or={props:{"data-polaris-scrollable":!0},selector:"[data-polaris-scrollable]"},ar={props:{"data-polaris-overlay":!0}},tr={props:{"data-polaris-layer":!0},selector:"[data-polaris-layer]"},ir={props:{"data-polaris-unstyled":!0}},nr={selector:"[data-polaris-top-bar]"},lr={selector:"[data-portal-id]"};var cr=["xs","sm","md","lg","xl"],ke={"breakpoints-xs":{value:"0px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-sm":{value:"490px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-md":{value:"768px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-lg":{value:"1040px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."},"breakpoints-xl":{value:"1440px",description:"Commonly used for sizing containers (e.g. max-width). See below for media query usage."}};function ze(r,t){var i=r==null?null:typeof Symbol<"u"&&r[Symbol.iterator]||r["@@iterator"];if(i!=null){var g,s,d,k,w=[],T=!0,I=!1;try{if(d=(i=i.call(r)).next,t!==0)for(;!(T=(g=d.call(i)).done)&&(w.push(g.value),w.length!==t);T=!0);}catch(R){I=!0,s=R}finally{try{if(!T&&i.return!=null&&(k=i.return(),Object(k)!==k))return}finally{if(I)throw s}}return w}}function Se(r,t){return t||(t=r.slice(0)),r.raw=t,r}function O(r,t){return Te(r)||ze(r,t)||Ie(r,t)||Ue()}function Te(r){if(Array.isArray(r))return r}function Ie(r,t){if(r){if(typeof r=="string")return q(r,t);var i=Object.prototype.toString.call(r).slice(8,-1);if(i==="Object"&&r.constructor&&(i=r.constructor.name),i==="Map"||i==="Set")return Array.from(r);if(i==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return q(r,t)}}function q(r,t){(t==null||t>r.length)&&(t=r.length);for(var i=0,g=new Array(t);i<t;i++)g[i]=r[i];return g}function Ue(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var V,j=16,E="px",M="em",B="rem",re=new RegExp(String.raw(V||(V=Se(["-?d+(?:.d+|d*)"],["-?\\d+(?:\\.\\d+|\\d*)"])))),Fe=new RegExp(E+"|"+M+"|"+B);function G(r){r===void 0&&(r="");var t=r.match(new RegExp(re.source+"("+Fe.source+")"));return t&&t[1]}function Me(r){r===void 0&&(r="");var t=G(r);if(!t||t===E)return r;if(t===M||t===B)return""+parseFloat(r)*j+E}function oe(r,t){r===void 0&&(r=""),t===void 0&&(t=j);var i=G(r);if(!i||i===M)return r;if(i===E)return""+parseFloat(r)/t+M;if(i===B)return""+parseFloat(r)*j/t+M}function Be(r){r===void 0&&(r="");var t=G(r);if(!t||t===B)return r;if(t===M)return""+parseFloat(r)+B;if(t===E)return""+parseFloat(r)/j+B}function Ee(r){return r.replace(new RegExp(re.source+"("+E+")","g"),function(t){var i;return(i=Be(t))!=null?i:t})}function ae(r){return Object.fromEntries(Object.entries(r).map(function(t){var i=O(t,2),g=i[0],s=i[1];return[g,Object.assign(Object.assign({},s),{},{value:Ee(s.value)})]}))}function te(r){return"--p-"+r}function a(r){return"var("+te(r)+")"}function Re(r){return Object.values(r).flatMap(function(t){return Object.keys(t)})}function Ne(r){var t=Object.entries(r),i=t.length-1;return Object.fromEntries(t.map(function(g,s){var d=g,k=O(d,2),w=k[0],T=k[1],I=Ae(T),R=Q(T),C=s===i?I:I+" and "+Q(t[s+1][1]);return[w,{up:I,down:R,only:C}]}))}function Ae(r){return"(min-width: "+oe(r)+")"}function Q(r){var t,i=parseFloat((t=Me(r))!=null?t:"")-.04;return"(max-width: "+oe(i+"px")+")"}var ie=["border","breakpoints","font","height","shadow","space","text","width"];function Ce(r){return Object.fromEntries(Object.entries(r).map(function(t){var i=O(t,2),g=i[0],s=i[1];return[g,ie.includes(g)?ae(s):s]}))}var _,X;function He(){if(X)return _;X=1;var r=function(l){return t(l)&&!i(l)};function t(n){return!!n&&typeof n=="object"}function i(n){var l=Object.prototype.toString.call(n);return l==="[object RegExp]"||l==="[object Date]"||d(n)}var g=typeof Symbol=="function"&&Symbol.for,s=g?Symbol.for("react.element"):60103;function d(n){return n.$$typeof===s}function k(n){return Array.isArray(n)?[]:{}}function w(n,l){return l.clone!==!1&&l.isMergeableObject(n)?F(k(n),n,l):n}function T(n,l,c){return n.concat(l).map(function(z){return w(z,c)})}function I(n,l){if(!l.customMerge)return F;var c=l.customMerge(n);return typeof c=="function"?c:F}function R(n){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(n).filter(function(l){return Object.propertyIsEnumerable.call(n,l)}):[]}function C(n){return Object.keys(n).concat(R(n))}function $(n,l){try{return l in n}catch{return!1}}function ve(n,l){return $(n,l)&&!(Object.hasOwnProperty.call(n,l)&&Object.propertyIsEnumerable.call(n,l))}function pe(n,l,c){var z={};return c.isMergeableObject(n)&&C(n).forEach(function(f){z[f]=w(n[f],c)}),C(l).forEach(function(f){ve(n,f)||($(n,f)&&c.isMergeableObject(l[f])?z[f]=I(f,c)(n[f],l[f],c):z[f]=w(l[f],c))}),z}function F(n,l,c){c=c||{},c.arrayMerge=c.arrayMerge||T,c.isMergeableObject=c.isMergeableObject||r,c.cloneUnlessOtherwiseSpecified=w;var z=Array.isArray(l),f=Array.isArray(n),ye=z===f;return ye?z?c.arrayMerge(n,l,c):pe(n,l,c):w(l,c)}F.all=function(l,c){if(!Array.isArray(l))throw new Error("first argument should be an array");return l.reduce(function(z,f){return F(z,f,c)},{})};var xe=F;return _=xe,_}var je=He();const Oe=we(je);var o={0:"0px","0165":"0.66px","025":"1px","050":"2px",100:"4px",150:"6px",200:"8px",275:"11px",300:"12px",325:"13px",350:"14px",400:"16px",450:"18px",500:"20px",550:"22px",600:"24px",700:"28px",750:"30px",800:"32px",900:"36px",1e3:"40px",1200:"48px",1600:"64px",2e3:"80px",2400:"96px",2800:"112px",3200:"128px"},Le={"border-radius-0":{value:o[0]},"border-radius-050":{value:o["050"]},"border-radius-100":{value:o[100]},"border-radius-150":{value:o[150]},"border-radius-200":{value:o[200]},"border-radius-300":{value:o[300]},"border-radius-400":{value:o[400]},"border-radius-500":{value:o[500]},"border-radius-750":{value:o[750]},"border-radius-full":{value:"9999px"},"border-width-0":{value:o[0]},"border-width-0165":{value:o["0165"]},"border-width-025":{value:o["025"]},"border-width-050":{value:o["050"]},"border-width-100":{value:o[100]}},e={1:"rgba(255, 255, 255, 1)",2:"rgba(253, 253, 253, 1)",3:"rgba(250, 250, 250, 1)",4:"rgba(247, 247, 247, 1)",5:"rgba(243, 243, 243, 1)",6:"rgba(241, 241, 241, 1)",7:"rgba(235, 235, 235, 1)",8:"rgba(227, 227, 227, 1)",9:"rgba(212, 212, 212, 1)",10:"rgba(204, 204, 204, 1)",11:"rgba(181, 181, 181, 1)",12:"rgba(138, 138, 138, 1)",13:"rgba(97, 97, 97, 1)",14:"rgba(74, 74, 74, 1)",15:"rgba(48, 48, 48, 1)",16:"rgba(26, 26, 26, 1)"},u={3:"rgba(234, 244, 255, 1)",4:"rgba(224, 240, 255, 1)",5:"rgba(213, 235, 255, 1)",6:"rgba(202, 230, 255, 1)",8:"rgba(168, 216, 255, 1)",9:"rgba(145, 208, 255, 1)",10:"rgba(81, 192, 255, 1)",11:"rgba(0, 148, 213, 1)",12:"rgba(0, 124, 180, 1)",15:"rgba(0, 58, 90, 1)",16:"rgba(0, 33, 51, 1)"},b={1:"rgba(252, 253, 255, 1)",3:"rgba(240, 242, 255, 1)",4:"rgba(234, 237, 255, 1)",5:"rgba(226, 231, 255, 1)",7:"rgba(213, 220, 255, 1)",8:"rgba(197, 208, 255, 1)",13:"rgba(0, 91, 211, 1)",14:"rgba(0, 66, 153, 1)",15:"rgba(0, 46, 106, 1)"},h={1:"rgba(248, 255, 251, 1)",3:"rgba(205, 254, 225, 1)",4:"rgba(180, 254, 210, 1)",5:"rgba(146, 254, 194, 1)",7:"rgba(56, 250, 163, 1)",12:"rgba(41, 132, 90, 1)",13:"rgba(19, 111, 69, 1)",14:"rgba(12, 81, 50, 1)",15:"rgba(8, 61, 37, 1)",16:"rgba(9, 42, 27, 1)"},W={9:"rgba(37, 232, 43, 1)",15:"rgba(3, 61, 5, 1)"},K={3:"rgba(253, 239, 253, 1)",12:"rgba(197, 48, 197, 1)"},x={3:"rgba(255, 241, 227, 1)",4:"rgba(255, 235, 213, 1)",5:"rgba(255, 228, 198, 1)",7:"rgba(255, 214, 164, 1)",8:"rgba(255, 200, 121, 1)",9:"rgba(255, 184, 0, 1)",10:"rgba(229, 165, 0, 1)",11:"rgba(178, 132, 0, 1)",12:"rgba(149, 111, 0, 1)",14:"rgba(94, 66, 0, 1)",15:"rgba(65, 45, 0, 1)",16:"rgba(37, 26, 0, 1)"},v={1:"rgba(253, 253, 255, 1)",2:"rgba(248, 247, 255, 1)",3:"rgba(243, 241, 255, 1)",5:"rgba(233, 229, 255, 1)",6:"rgba(228, 222, 255, 1)",7:"rgba(223, 217, 255, 1)",11:"rgba(148, 116, 255, 1)",12:"rgba(128, 81, 255, 1)",13:"rgba(113, 38, 255, 1)",14:"rgba(87, 0, 209, 1)"},p={1:"rgba(255, 251, 251, 1)",4:"rgba(254, 233, 232, 1)",5:"rgba(254, 226, 225, 1)",6:"rgba(254, 218, 217, 1)",7:"rgba(254, 211, 209, 1)",8:"rgba(254, 195, 193, 1)",11:"rgba(239, 77, 47, 1)",12:"rgba(229, 28, 0, 1)",13:"rgba(181, 38, 11, 1)",14:"rgba(142, 31, 11, 1)",15:"rgba(95, 21, 7, 1)",16:"rgba(47, 10, 4, 1)"},Z={2:"rgba(255, 246, 248, 1)",11:"rgba(253, 75, 146, 1)"},J={9:"rgba(44, 224, 212, 1)",15:"rgba(3, 60, 57, 1)"},y={2:"rgba(255, 248, 219, 1)",3:"rgba(255, 244, 191, 1)",4:"rgba(255, 239, 157, 1)",5:"rgba(255, 235, 120, 1)",6:"rgba(255, 230, 0, 1)",8:"rgba(234, 211, 0, 1)",9:"rgba(225, 203, 0, 1)",11:"rgba(153, 138, 0, 1)",12:"rgba(130, 117, 0, 1)",14:"rgba(79, 71, 0, 1)",15:"rgba(51, 46, 0, 1)",16:"rgba(31, 28, 0, 1)"},m={1:"rgba(0, 0, 0, 0)",3:"rgba(0, 0, 0, 0.02)",5:"rgba(0, 0, 0, 0.05)",6:"rgba(0, 0, 0, 0.06)",7:"rgba(0, 0, 0, 0.08)",8:"rgba(0, 0, 0, 0.11)",9:"rgba(0, 0, 0, 0.17)",10:"rgba(0, 0, 0, 0.20)",14:"rgba(0, 0, 0, 0.71)",15:"rgba(0, 0, 0, 0.81)"},S={4:"rgba(255, 255, 255, 0.03)",8:"rgba(255, 255, 255, 0.11)",9:"rgba(255, 255, 255, 0.17)",10:"rgba(255, 255, 255, 0.20)",11:"rgba(255, 255, 255, 0.28)"},_e={"color-scheme":{value:"light"},"color-bg":{value:e[6],description:"The default background color of the admin."},"color-bg-inverse":{value:e[16],description:"Use for high contrast page or component backgrounds."},"color-bg-surface":{value:e[1],description:"The background color for elements with the highest level of prominence, like a card."},"color-bg-surface-hover":{value:e[4],description:"The hover state color for elements with the highest level of prominence."},"color-bg-surface-active":{value:e[5],description:"The active state (on press) color for elements with the highest level of prominence."},"color-bg-surface-selected":{value:e[6],description:"The selected state color for elements with the highest level of prominence."},"color-bg-surface-disabled":{value:m[5],description:"The disabled state color for elements."},"color-bg-surface-secondary":{value:e[4],description:"The background color for elements with a secondary level of prominence."},"color-bg-surface-secondary-hover":{value:e[6],description:"The hover state color for elements with a secondary level of prominence."},"color-bg-surface-secondary-active":{value:e[7],description:"The active state (on press) color for elements with a secondary level of prominence."},"color-bg-surface-secondary-selected":{value:e[7],description:"The selected state color for elements with a secondary level of prominence."},"color-bg-surface-tertiary":{value:e[5],description:"The background color for elements with a third level of prominence."},"color-bg-surface-tertiary-hover":{value:e[7],description:"The hover state color for elements with a third level of prominence."},"color-bg-surface-tertiary-active":{value:e[8],description:"The active state (on press) color for elements with a third level of prominence."},"color-bg-surface-brand":{value:e[8],description:"Use to apply the key color to elements."},"color-bg-surface-brand-hover":{value:e[7],description:"The hover state color for key elements."},"color-bg-surface-brand-active":{value:e[6],description:"The active state (on press) color for key elements."},"color-bg-surface-brand-selected":{value:e[6],description:"The selected state color for key elements."},"color-bg-surface-info":{value:u[3],description:"Use for backgrounds communicating important information, like banners."},"color-bg-surface-info-hover":{value:u[4],description:"The hover state color for communicating important information."},"color-bg-surface-info-active":{value:u[6],description:"The active state (on press) color for communicating important information."},"color-bg-surface-success":{value:h[3],description:"Use for backgrounds communicating success, like banners."},"color-bg-surface-success-hover":{value:h[4],description:"The hover state color for communicating success."},"color-bg-surface-success-active":{value:h[5],description:"The active state (on press) color for communicating success."},"color-bg-surface-caution":{value:y[2],description:"Use for backgrounds communicating caution, like banners."},"color-bg-surface-caution-hover":{value:y[3],description:"The hover state for communicating caution."},"color-bg-surface-caution-active":{value:y[4],description:"The active state (on press) color for communicating caution."},"color-bg-surface-warning":{value:x[3],description:"Use for backgrounds communicating warning, like banners."},"color-bg-surface-warning-hover":{value:x[4],description:"The hover state color for communicating warning."},"color-bg-surface-warning-active":{value:x[5],description:"The active state (on press) color for communicating warning."},"color-bg-surface-critical":{value:p[4],description:"Use for backgrounds communicating critical information, like banners or input errors."},"color-bg-surface-critical-hover":{value:p[5],description:"The hover state color for communicating critical information."},"color-bg-surface-critical-active":{value:p[6],description:"The active state (on press) color for communicating critical information."},"color-bg-surface-emphasis":{value:b[3],description:"Use for backgrounds indicating areas of focus in editors, such as the theme editor."},"color-bg-surface-emphasis-hover":{value:b[4],description:"The hover state color for elements indicating areas of focus in editors."},"color-bg-surface-emphasis-active":{value:b[5],description:"The active state (on press) color for elements indicating areas of focus in editors."},"color-bg-surface-magic":{value:v[2],description:"Use for backgrounds of elements suggested by magic AI."},"color-bg-surface-magic-hover":{value:v[3],description:"The hover state color for elements suggested by magic AI."},"color-bg-surface-magic-active":{value:v[5],description:"The active state (on press) color for elements suggested by magic AI."},"color-bg-surface-inverse":{value:e[15],description:"Use for elements on bg-inverse."},"color-bg-surface-transparent":{value:m[1],description:"Use for elements that need a fully transparent background."},"color-bg-fill":{value:e[1],description:"The background color of contained elements with a smaller surface area, like a button."},"color-bg-fill-hover":{value:e[3],description:"The hover state color of contained elements with a smaller surface area, like a button."},"color-bg-fill-active":{value:e[4],description:"The active state (on press) color of contained elements with a smaller surface area, like a button."},"color-bg-fill-selected":{value:e[10],description:"The selected state color of contained elements with a smaller surface area, like a button or checkbox."},"color-bg-fill-disabled":{value:m[5],description:"The disabled state color of contained elements with a smaller surface area, like a button."},"color-bg-fill-secondary":{value:e[6],description:"The background color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-secondary-hover":{value:e[7],description:"The hover state color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-secondary-active":{value:e[8],description:"The active state (on press) color of elements with a smaller surface area and a secondary level of prominence."},"color-bg-fill-tertiary":{value:e[8],description:"The background color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-tertiary-hover":{value:e[9],description:"The hover state color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-tertiary-active":{value:e[10],description:"The active state (on press) color of elements with a smaller surface area and a third level of prominence."},"color-bg-fill-brand":{value:e[15],description:"The background color of main actions, like primary buttons."},"color-bg-fill-brand-hover":{value:e[16],description:"The hover state color of main actions, like primary buttons."},"color-bg-fill-brand-active":{value:e[16],description:"The active state (on press) color of main actions, like primary buttons."},"color-bg-fill-brand-selected":{value:e[15],description:"The selected state color of main actions, like primary buttons."},"color-bg-fill-brand-disabled":{value:m[9],description:"The disabled state color of main actions, like primary buttons."},"color-bg-fill-info":{value:u[9],description:"Use for backgrounds communicating important information on elements with a smaller surface area, like a badge or button."},"color-bg-fill-info-hover":{value:u[10],description:"The hover state color for communicating important information on elements with a smaller surface area."},"color-bg-fill-info-active":{value:u[11],description:"The active state (on press) color for communicating important information on elements with a smaller surface area."},"color-bg-fill-info-secondary":{value:u[5],description:"Use for backgrounds communicating important information on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-success":{value:h[12],description:"Use for backgrounds communicating success on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-success-hover":{value:h[13],description:"The hover state color for communicating success on elements with a smaller surface area."},"color-bg-fill-success-active":{value:h[14],description:"The active state (on press) color for communicating success on elements with a smaller surface area."},"color-bg-fill-success-secondary":{value:h[4],description:"Use for backgrounds communicating success on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-warning":{value:x[9],description:"Use for backgrounds communicating warning on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-warning-hover":{value:x[10],description:"The hover state color for communicating warning on elements with a smaller surface area."},"color-bg-fill-warning-active":{value:x[11],description:"The active state (on press) color for communicating warning on elements with a smaller surface area."},"color-bg-fill-warning-secondary":{value:x[7],description:"Use for backgrounds communicating warning on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-caution":{value:y[6],description:"Use for backgrounds communicating caution on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-caution-hover":{value:y[8],description:"The hover state color for communicating caution on elements with a smaller surface area."},"color-bg-fill-caution-active":{value:y[9],description:"The active state (on press) color for communicating caution on elements with a smaller surface area."},"color-bg-fill-caution-secondary":{value:y[5],description:"Use for backgrounds communicating caution on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-critical":{value:p[12],description:"Use for backgrounds communicating critical information on elements with a smaller surface area, like a badge or a banner."},"color-bg-fill-critical-hover":{value:p[13],description:"The hover state color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-active":{value:p[14],description:"The active state (on press) color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-selected":{value:p[14],description:"The selected state color for communicating critical information on elements with a smaller surface area."},"color-bg-fill-critical-secondary":{value:p[7],description:"Use for backgrounds communicating critical information on elements with a smaller surface area, with a secondary level of prominence."},"color-bg-fill-emphasis":{value:b[13],description:"Use for backgrounds indicating areas of focus in editors on elements with a smaller surface area, like a button or a badge."},"color-bg-fill-emphasis-hover":{value:b[14],description:"The hover state color for indicating areas of focus in editors on elements with a smaller surface area."},"color-bg-fill-emphasis-active":{value:b[15],description:"The active state (on press) color for indicating areas of focus in editors on elements with a smaller surface area."},"color-bg-fill-magic":{value:v[12],description:"The background color of elements suggested by magic AI, like a badge or a banner."},"color-bg-fill-magic-secondary":{value:v[5],description:"The background color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-magic-secondary-hover":{value:v[6],description:"The hover state color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-magic-secondary-active":{value:v[7],description:"The active state (on press) color of elements suggested by magic AI, with a secondary level of prominence."},"color-bg-fill-inverse":{value:e[15],description:"The background color of elements with a smaller surface area on an inverse background."},"color-bg-fill-inverse-hover":{value:e[14],description:"The hover state color of elements with a smaller surface area on an inverse background."},"color-bg-fill-inverse-active":{value:e[13],description:"The active state (on press) color of elements with a smaller surface area on an inverse background."},"color-bg-fill-transparent":{value:m[3],description:"The background color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-hover":{value:m[5],description:"The hover state color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-active":{value:m[7],description:"The active state (on press) color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-selected":{value:m[7],description:"The selected state color of elements that need to sit on different background colors, like tabs."},"color-bg-fill-transparent-secondary":{value:m[6],description:"The background color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-bg-fill-transparent-secondary-hover":{value:m[7],description:"The hover state color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-bg-fill-transparent-secondary-active":{value:m[8],description:"The active state (on press) color of elements that need to sit on different background colors, with a secondary level of prominence."},"color-text":{value:e[15],description:"The default text color."},"color-text-secondary":{value:e[13],description:"Use for text with a secondary level of prominence."},"color-text-disabled":{value:e[11],description:"Use for text in a disabled state."},"color-text-link":{value:b[13],description:"Use for text links."},"color-text-link-hover":{value:b[14],description:"The hover state color for text links."},"color-text-link-active":{value:b[15],description:"The active state (on press) color for text links."},"color-text-brand":{value:e[14],description:"Use for text that needs to pull attention."},"color-text-brand-hover":{value:e[15],description:"The hover state color for text that needs to pull attention."},"color-text-brand-on-bg-fill":{value:e[1],description:"Use for text on bg-fill-brand, like primary buttons."},"color-text-brand-on-bg-fill-hover":{value:e[8],description:"The hover state color for text on bg-fill-brand-hover."},"color-text-brand-on-bg-fill-active":{value:e[10],description:"The active state (on press) color for text on bg-fill-brand."},"color-text-brand-on-bg-fill-disabled":{value:e[1],description:"The disabled state color for text on bg-fill-brand-disabled."},"color-text-info":{value:u[15],description:"Use for text communicating important information."},"color-text-info-hover":{value:u[15],description:"The hover state color for text communicating important information."},"color-text-info-active":{value:u[16],description:"The active state (on press) color for text communicating important information."},"color-text-info-secondary":{value:u[12],description:"Use for text communicating important information with a secondary level of prominence."},"color-text-info-on-bg-fill":{value:u[16],description:"Use for text and icons on bg-fill-info."},"color-text-success":{value:h[14],description:"Use for text communicating success."},"color-text-success-hover":{value:h[15],description:"The hover state color for text communicating success."},"color-text-success-active":{value:h[16],description:"The active state (on press) color for text communicating success."},"color-text-success-secondary":{value:h[12],description:"Use for text communicating success with a secondary level of prominence."},"color-text-success-on-bg-fill":{value:h[1],description:"Use for text and icons on bg-fill-success."},"color-text-caution":{value:y[14],description:"Use for text communicating caution."},"color-text-caution-hover":{value:y[15],description:"The hover state color for text communicating caution."},"color-text-caution-active":{value:y[16],description:"The active state (on press) color for text communicating caution."},"color-text-caution-secondary":{value:y[12],description:"Use for text communicating caution with a secondary level of prominence."},"color-text-caution-on-bg-fill":{value:y[15],description:"Use for text and icons on bg-fill-caution."},"color-text-warning":{value:x[14],description:"Use for text communicating warning."},"color-text-warning-hover":{value:x[15],description:"The hover state color for text communicating warning."},"color-text-warning-active":{value:x[16],description:"The active state (on press) color for text communicating warning."},"color-text-warning-secondary":{value:x[12],description:"Use for text communicating warning with a secondary level of prominence."},"color-text-warning-on-bg-fill":{value:x[16],description:"Use for text and icons on bg-fill-warning."},"color-text-critical":{value:p[14],description:"Use for text communicating critical information."},"color-text-critical-hover":{value:p[15],description:"The hover state color for text communicating critical information."},"color-text-critical-active":{value:p[16],description:"The active state (on press) color for text communicating critical information."},"color-text-critical-secondary":{value:p[12],description:"Use for text communicating critical information with a secondary level of prominence."},"color-text-critical-on-bg-fill":{value:p[1],description:"Use for text and icons on bg-fill-critical."},"color-text-emphasis":{value:b[13],description:"Use for text indicating areas of focus in editors, like the theme editor."},"color-text-emphasis-hover":{value:b[14],description:"The hover state color for text indicating areas of focus."},"color-text-emphasis-active":{value:b[15],description:"The active state (on press) color for text indicating areas of focus."},"color-text-emphasis-on-bg-fill":{value:b[1],description:"Use for text and icons on bg-fill-emphasis."},"color-text-emphasis-on-bg-fill-hover":{value:b[5],description:"Use for text and icons on bg-fill-emphasis-hover."},"color-text-emphasis-on-bg-fill-active":{value:b[7],description:"Use for text and icons on bg-fill-emphasis-active."},"color-text-magic":{value:v[14],description:"Use for text suggested by magic AI."},"color-text-magic-secondary":{value:v[13],description:"Use for text suggested by magic AI with a secondary level of prominence."},"color-text-magic-on-bg-fill":{value:v[1],description:"Use for text and icons on bg-fill-magic."},"color-text-inverse":{value:e[8],description:"Use for text on an inverse background."},"color-text-inverse-secondary":{value:e[11],description:"Use for secondary text on an inverse background."},"color-text-link-inverse":{value:b[8],description:"Use for text links on an inverse background."},"color-border":{value:e[8],description:"The default color for borders on any element."},"color-border-hover":{value:e[10],description:"The hover color for borders on any element."},"color-border-disabled":{value:e[7],description:"The disabled color for borders on any element."},"color-border-secondary":{value:e[7],description:"The color for hr elements or any visual dividers."},"color-border-tertiary":{value:e[10],description:"The border color on any element. Pair with bg-surface-tertiary or bg-fill-tertiary."},"color-border-focus":{value:b[13],description:"The focus ring for any interactive element in a focused state."},"color-border-brand":{value:e[8],description:"Use for borders paired with brand colors."},"color-border-info":{value:u[8],description:"Use for borders communicating information."},"color-border-success":{value:h[5],description:"Use for borders communicating success."},"color-border-caution":{value:y[5],description:"Use for borders communicating caution."},"color-border-warning":{value:x[8],description:"Use for borders communicating warning."},"color-border-critical":{value:p[8],description:"Use for borders communicating critical information."},"color-border-critical-secondary":{value:p[14],description:"Use for borders communicating critical information, such as borders on invalid text fields."},"color-border-emphasis":{value:b[13],description:"Use for borders indicating areas of focus."},"color-border-emphasis-hover":{value:b[14],description:"The hover state color for borders indicating areas of focus."},"color-border-emphasis-active":{value:b[15],description:"The active state (on press) color for borders indicating areas of focus."},"color-border-magic":{value:v[6],description:"Use for borders suggested by magic AI."},"color-border-magic-secondary":{value:v[11],description:"Use for borders suggested by magic AI, such as borders on text fields."},"color-border-magic-secondary-hover":{value:v[12],description:"Use for borders suggested by magic AI, such as borders on text fields."},"color-border-inverse":{value:e[13],description:"Use for borders on an inverse background, such as borders on the global search."},"color-border-inverse-hover":{value:e[10],description:"The hover state color for borders on an inverse background."},"color-border-inverse-active":{value:e[8],description:"The active state (on press) color for borders on an inverse background."},"color-tooltip-tail-down-border-experimental":{value:e[9],description:"The border color for tooltip tails pointing down."},"color-tooltip-tail-up-border-experimental":{value:e[8],description:"The border color for tooltip tails pointing up."},"color-border-gradient-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-hover-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-selected-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-border-gradient-active-experimental":{value:"linear-gradient(to bottom, "+e[7]+", "+e[10]+" 78%, "+e[11]+")"},"color-icon":{value:e[14],description:"The default color for icons."},"color-icon-hover":{value:e[15],description:"The hover state color for icons."},"color-icon-active":{value:e[16],description:"The active state (on press) color for icons."},"color-icon-disabled":{value:e[10],description:"The disabled state color for icons."},"color-icon-secondary":{value:e[12],description:"Use for secondary icons."},"color-icon-secondary-hover":{value:e[13],description:"The hover state color for secondary icons."},"color-icon-secondary-active":{value:e[14],description:"The active state (on press) color for secondary icons."},"color-icon-brand":{value:e[16],description:"Use for icons that need to pull more focus."},"color-icon-info":{value:u[11],description:"Use for icons communicating information."},"color-icon-success":{value:h[12],description:"Use for icons communicating success."},"color-icon-caution":{value:y[11],description:"Use for icons communicating caution."},"color-icon-warning":{value:x[11],description:"Use for icons communicating warning."},"color-icon-critical":{value:p[11],description:"Use for icons communicating critical information."},"color-icon-emphasis":{value:b[13],description:"Use for icons indicating areas of focus in editors, like the theme editor."},"color-icon-emphasis-hover":{value:b[14],description:"The hover color for icons indicating areas of focus in editors."},"color-icon-emphasis-active":{value:b[15],description:"The active state (on press) color for icons indicating areas of focus in editors."},"color-icon-magic":{value:v[12],description:"Use for icons suggested by magic AI."},"color-icon-inverse":{value:e[8],description:"Use for icons on an inverse background."},"color-avatar-bg-fill":{value:e[11]},"color-avatar-five-bg-fill":{value:Z[11]},"color-avatar-five-text-on-bg-fill":{value:Z[2]},"color-avatar-four-bg-fill":{value:u[10]},"color-avatar-four-text-on-bg-fill":{value:u[16]},"color-avatar-one-bg-fill":{value:K[12]},"color-avatar-one-text-on-bg-fill":{value:K[3]},"color-avatar-seven-bg-fill":{value:v[11]},"color-avatar-seven-text-on-bg-fill":{value:v[2]},"color-avatar-six-bg-fill":{value:W[9]},"color-avatar-six-text-on-bg-fill":{value:W[15]},"color-avatar-text-on-bg-fill":{value:e[1]},"color-avatar-three-bg-fill":{value:J[9]},"color-avatar-three-text-on-bg-fill":{value:J[15]},"color-avatar-two-bg-fill":{value:h[7]},"color-avatar-two-text-on-bg-fill":{value:h[14]},"color-backdrop-bg":{value:m[14]},"color-button-gradient-bg-fill":{value:"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)"},"color-checkbox-bg-surface-disabled":{value:m[7]},"color-checkbox-icon-disabled":{value:e[1]},"color-input-bg-surface":{value:e[2]},"color-input-bg-surface-hover":{value:e[3]},"color-input-bg-surface-active":{value:e[4]},"color-input-border":{value:e[12]},"color-input-border-hover":{value:e[13]},"color-input-border-active":{value:e[16]},"color-nav-bg":{value:e[7]},"color-nav-bg-surface":{value:m[3]},"color-nav-bg-surface-hover":{value:e[6]},"color-nav-bg-surface-active":{value:e[3]},"color-nav-bg-surface-selected":{value:e[3]},"color-radio-button-bg-surface-disabled":{value:m[7]},"color-radio-button-icon-disabled":{value:e[1]},"color-video-thumbnail-play-button-bg-fill-hover":{value:m[15]},"color-video-thumbnail-play-button-bg-fill":{value:m[14]},"color-video-thumbnail-play-button-text-on-bg-fill":{value:e[1]},"color-scrollbar-thumb-bg-hover":{value:e[12]}},Pe={"font-family-sans":{value:"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif"},"font-family-mono":{value:"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace"},"font-size-275":{value:o[275]},"font-size-300":{value:o[300]},"font-size-325":{value:o[325]},"font-size-350":{value:o[350]},"font-size-400":{value:o[400]},"font-size-450":{value:o[450]},"font-size-500":{value:o[500]},"font-size-550":{value:o[550]},"font-size-600":{value:o[600]},"font-size-750":{value:o[750]},"font-size-800":{value:o[800]},"font-size-900":{value:o[900]},"font-size-1000":{value:o[1e3]},"font-weight-regular":{value:"450"},"font-weight-medium":{value:"550"},"font-weight-semibold":{value:"650"},"font-weight-bold":{value:"700"},"font-letter-spacing-densest":{value:"-0.54px"},"font-letter-spacing-denser":{value:"-0.3px"},"font-letter-spacing-dense":{value:"-0.2px"},"font-letter-spacing-normal":{value:"0px"},"font-line-height-300":{value:o[300]},"font-line-height-400":{value:o[400]},"font-line-height-500":{value:o[500]},"font-line-height-600":{value:o[600]},"font-line-height-700":{value:o[700]},"font-line-height-800":{value:o[800]},"font-line-height-1000":{value:o[1e3]},"font-line-height-1200":{value:o[1200]}},Ye={"height-0":{value:o[0]},"height-025":{value:o["025"]},"height-050":{value:o["050"]},"height-100":{value:o[100]},"height-150":{value:o[150]},"height-200":{value:o[200]},"height-300":{value:o[300]},"height-400":{value:o[400]},"height-500":{value:o[500]},"height-600":{value:o[600]},"height-700":{value:o[700]},"height-800":{value:o[800]},"height-900":{value:o[900]},"height-1000":{value:o[1e3]},"height-1200":{value:o[1200]},"height-1600":{value:o[1600]},"height-2000":{value:o[2e3]},"height-2400":{value:o[2400]},"height-2800":{value:o[2800]},"height-3200":{value:o[3200]}},Ge={"motion-duration-0":{value:"0ms"},"motion-duration-50":{value:"50ms"},"motion-duration-100":{value:"100ms"},"motion-duration-150":{value:"150ms"},"motion-duration-200":{value:"200ms"},"motion-duration-250":{value:"250ms"},"motion-duration-300":{value:"300ms"},"motion-duration-350":{value:"350ms"},"motion-duration-400":{value:"400ms"},"motion-duration-450":{value:"450ms"},"motion-duration-500":{value:"500ms"},"motion-duration-5000":{value:"5000ms"},"motion-ease":{value:"cubic-bezier(0.25, 0.1, 0.25, 1)",description:"Responds quickly and finishes with control. A great default for any user interaction."},"motion-ease-in":{value:"cubic-bezier(0.42, 0, 1, 1)",description:"Starts slowly and finishes at top speed. Use sparingly."},"motion-ease-out":{value:"cubic-bezier(0.19, 0.91, 0.38, 1)",description:"Starts at top speed and finishes slowly. Use sparingly."},"motion-ease-in-out":{value:"cubic-bezier(0.42, 0, 0.58, 1)",description:"Starts and finishes with equal speed. A good default for transitions triggered by the system."},"motion-linear":{value:"cubic-bezier(0, 0, 1, 1)",description:"Moves with constant speed. Use for continuous and mechanical animations, such as rotating spinners."},"motion-keyframes-bounce":{value:"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }"},"motion-keyframes-fade-in":{value:"{ to { opacity: 1 } }"},"motion-keyframes-pulse":{value:"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }"},"motion-keyframes-spin":{value:"{ to { transform: rotate(1turn) } }"},"motion-keyframes-appear-above":{value:"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }"},"motion-keyframes-appear-below":{value:"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"}},De={"shadow-0":{value:"none"},"shadow-100":{value:"0px 1px 0px 0px rgba(26, 26, 26, 0.07)"},"shadow-200":{value:"0px 3px 1px -1px rgba(26, 26, 26, 0.07)"},"shadow-300":{value:"0px 4px 6px -2px rgba(26, 26, 26, 0.20)"},"shadow-400":{value:"0px 8px 16px -4px rgba(26, 26, 26, 0.22)"},"shadow-500":{value:"0px 12px 20px -8px rgba(26, 26, 26, 0.24)"},"shadow-600":{value:"0px 20px 20px -8px rgba(26, 26, 26, 0.28)"},"shadow-bevel-100":{value:"1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset"},"shadow-inset-100":{value:"0px 1px 2px 0px rgba(26, 26, 26, 0.15) inset, 0px 1px 1px 0px rgba(26, 26, 26, 0.15) inset"},"shadow-inset-200":{value:"0px 2px 1px 0px rgba(26, 26, 26, 0.20) inset, 1px 0px 1px 0px rgba(26, 26, 26, 0.12) inset, -1px 0px 1px 0px rgba(26, 26, 26, 0.12) inset"},"shadow-button":{value:"0px -1px 0px 0px #b5b5b5 inset, 0px 0px 0px 1px rgba(0, 0, 0, 0.1) inset, 0px 0.5px 0px 1.5px #FFF inset"},"shadow-button-hover":{value:"0px 1px 0px 0px #EBEBEB inset, -1px 0px 0px 0px #EBEBEB inset, 1px 0px 0px 0px #EBEBEB inset, 0px -1px 0px 0px #CCC inset"},"shadow-button-inset":{value:"-1px 0px 1px 0px rgba(26, 26, 26, 0.122) inset, 1px 0px 1px 0px rgba(26, 26, 26, 0.122) inset, 0px 2px 1px 0px rgba(26, 26, 26, 0.2) inset"},"shadow-button-primary":{value:"0px -1px 0px 1px rgba(0, 0, 0, 0.8) inset, 0px 0px 0px 1px rgba(48, 48, 48, 1) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.25) inset;"},"shadow-button-primary-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.24) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1px 0px 0px #000 inset, 0px -1px 0px 1px #1A1A1A"},"shadow-button-primary-inset":{value:"0px 3px 0px 0px rgb(0, 0, 0) inset"},"shadow-button-primary-critical":{value:"0px -1px 0px 1px rgba(142, 31, 11, 0.8) inset, 0px 0px 0px 1px rgba(181, 38, 11, 0.8) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.349) inset"},"shadow-button-primary-critical-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.48) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1.5px 0px 0px rgba(0, 0, 0, 0.25) inset"},"shadow-button-primary-critical-inset":{value:"-1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 0px 2px 0px 0px rgba(0, 0, 0, 0.6) inset"},"shadow-button-primary-success":{value:"0px -1px 0px 1px rgba(12, 81, 50, 0.8) inset, 0px 0px 0px 1px rgba(19, 111, 69, 0.8) inset, 0px 0.5px 0px 1.5px rgba(255, 255, 255, 0.251) inset"},"shadow-button-primary-success-hover":{value:"0px 1px 0px 0px rgba(255, 255, 255, 0.48) inset, 1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, -1px 0px 0px 0px rgba(255, 255, 255, 0.20) inset, 0px -1.5px 0px 0px rgba(0, 0, 0, 0.25) inset"},"shadow-button-primary-success-inset":{value:"-1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 1px 0px 1px 0px rgba(0, 0, 0, 0.2) inset, 0px 2px 0px 0px rgba(0, 0, 0, 0.6) inset"},"shadow-border-inset":{value:"0px 0px 0px 1px rgba(0, 0, 0, 0.08) inset"}},$e={"space-0":{value:o[0]},"space-025":{value:o["025"]},"space-050":{value:o["050"]},"space-100":{value:o[100]},"space-150":{value:o[150]},"space-200":{value:o[200]},"space-300":{value:o[300]},"space-400":{value:o[400]},"space-500":{value:o[500]},"space-600":{value:o[600]},"space-800":{value:o[800]},"space-1000":{value:o[1e3]},"space-1200":{value:o[1200]},"space-1600":{value:o[1600]},"space-2000":{value:o[2e3]},"space-2400":{value:o[2400]},"space-2800":{value:o[2800]},"space-3200":{value:o[3200]},"space-button-group-gap":{value:H("space-200")},"space-card-gap":{value:H("space-400")},"space-card-padding":{value:H("space-400")},"space-table-cell-padding":{value:H("space-150")}};function H(r){return"var("+te(r)+")"}var qe={"text-heading-3xl-font-family":{value:a("font-family-sans")},"text-heading-3xl-font-size":{value:a("font-size-900")},"text-heading-3xl-font-weight":{value:a("font-weight-bold")},"text-heading-3xl-font-letter-spacing":{value:a("font-letter-spacing-densest")},"text-heading-3xl-font-line-height":{value:a("font-line-height-1200")},"text-heading-2xl-font-family":{value:a("font-family-sans")},"text-heading-2xl-font-size":{value:a("font-size-750")},"text-heading-2xl-font-weight":{value:a("font-weight-bold")},"text-heading-2xl-font-letter-spacing":{value:a("font-letter-spacing-denser")},"text-heading-2xl-font-line-height":{value:a("font-line-height-1000")},"text-heading-xl-font-family":{value:a("font-family-sans")},"text-heading-xl-font-size":{value:a("font-size-600")},"text-heading-xl-font-weight":{value:a("font-weight-bold")},"text-heading-xl-font-letter-spacing":{value:a("font-letter-spacing-dense")},"text-heading-xl-font-line-height":{value:a("font-line-height-800")},"text-heading-lg-font-family":{value:a("font-family-sans")},"text-heading-lg-font-size":{value:a("font-size-500")},"text-heading-lg-font-weight":{value:a("font-weight-semibold")},"text-heading-lg-font-letter-spacing":{value:a("font-letter-spacing-dense")},"text-heading-lg-font-line-height":{value:a("font-line-height-600")},"text-heading-md-font-family":{value:a("font-family-sans")},"text-heading-md-font-size":{value:a("font-size-350")},"text-heading-md-font-weight":{value:a("font-weight-semibold")},"text-heading-md-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-heading-md-font-line-height":{value:a("font-line-height-500")},"text-heading-sm-font-family":{value:a("font-family-sans")},"text-heading-sm-font-size":{value:a("font-size-325")},"text-heading-sm-font-weight":{value:a("font-weight-semibold")},"text-heading-sm-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-heading-sm-font-line-height":{value:a("font-line-height-500")},"text-heading-xs-font-family":{value:a("font-family-sans")},"text-heading-xs-font-size":{value:a("font-size-300")},"text-heading-xs-font-weight":{value:a("font-weight-semibold")},"text-heading-xs-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-heading-xs-font-line-height":{value:a("font-line-height-400")},"text-body-lg-font-family":{value:a("font-family-sans")},"text-body-lg-font-size":{value:a("font-size-350")},"text-body-lg-font-weight":{value:a("font-weight-regular")},"text-body-lg-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-body-lg-font-line-height":{value:a("font-line-height-500")},"text-body-md-font-family":{value:a("font-family-sans")},"text-body-md-font-size":{value:a("font-size-325")},"text-body-md-font-weight":{value:a("font-weight-regular")},"text-body-md-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-body-md-font-line-height":{value:a("font-line-height-500")},"text-body-sm-font-family":{value:a("font-family-sans")},"text-body-sm-font-size":{value:a("font-size-300")},"text-body-sm-font-weight":{value:a("font-weight-regular")},"text-body-sm-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-body-sm-font-line-height":{value:a("font-line-height-400")},"text-body-xs-font-family":{value:a("font-family-sans")},"text-body-xs-font-size":{value:a("font-size-275")},"text-body-xs-font-weight":{value:a("font-weight-regular")},"text-body-xs-font-letter-spacing":{value:a("font-letter-spacing-normal")},"text-body-xs-font-line-height":{value:a("font-line-height-300")}},Ve={"width-0":{value:o[0]},"width-025":{value:o["025"]},"width-050":{value:o["050"]},"width-100":{value:o[100]},"width-150":{value:o[150]},"width-200":{value:o[200]},"width-300":{value:o[300]},"width-400":{value:o[400]},"width-500":{value:o[500]},"width-600":{value:o[600]},"width-700":{value:o[700]},"width-800":{value:o[800]},"width-900":{value:o[900]},"width-1000":{value:o[1e3]},"width-1200":{value:o[1200]},"width-1600":{value:o[1600]},"width-2000":{value:o[2e3]},"width-2400":{value:o[2400]},"width-2800":{value:o[2800]},"width-3200":{value:o[3200]}},Qe={"z-index-0":{value:"auto"},"z-index-1":{value:"100"},"z-index-2":{value:"400"},"z-index-3":{value:"510"},"z-index-4":{value:"512"},"z-index-5":{value:"513"},"z-index-6":{value:"514"},"z-index-7":{value:"515"},"z-index-8":{value:"516"},"z-index-9":{value:"517"},"z-index-10":{value:"518"},"z-index-11":{value:"519"},"z-index-12":{value:"520"}},ne=Ce({border:Le,breakpoints:ke,color:_e,font:Pe,height:Ye,motion:Ge,shadow:De,space:$e,text:qe,width:Ve,zIndex:Qe});function L(r){return Object.fromEntries(Object.entries(r).map(function(t){var i=O(t,2),g=i[0],s=i[1];return[g,s&&ie.includes(g)?ae(s):s]}))}function A(r){return Oe(ne,r)}function sr(r){return"p-theme-"+r}function le(r){var t=new Set(Re(r));return function(i){return t.has(i)}}le(ne);var ce="light",D=ce,gr=[ce,"light-mobile","light-high-contrast-experimental","dark-experimental"],se=L({});A(se);var ge=L({color:{"color-text":{value:e[16]},"color-text-secondary":{value:e[16]},"color-text-brand":{value:e[16]},"color-icon-secondary":{value:e[14]},"color-border":{value:e[12]},"color-input-border":{value:e[14]},"color-border-secondary":{value:e[12]},"color-bg-surface-secondary":{value:e[6]}},shadow:{"shadow-bevel-100":{value:"0px 1px 0px 0px rgba(26, 26, 26, 0.07), 0px 1px 0px 0px rgba(208, 208, 208, 0.40) inset, 1px 0px 0px 0px #CCC inset, -1px 0px 0px 0px #CCC inset, 0px -1px 0px 0px #999 inset"}}});A(ge);var P="0 0 0 "+a("border-width-025")+" "+a("color-border")+" inset",be=L({color:{"color-button-gradient-bg-fill":{value:"none"}},shadow:{"shadow-100":{value:"none"},"shadow-bevel-100":{value:"none"},"shadow-button":{value:P},"shadow-button-hover":{value:P},"shadow-button-inset":{value:P},"shadow-button-primary":{value:"none"},"shadow-button-primary-hover":{value:"none"},"shadow-button-primary-inset":{value:"none"},"shadow-button-primary-critical":{value:"none"},"shadow-button-primary-critical-hover":{value:"none"},"shadow-button-primary-critical-inset":{value:"none"},"shadow-button-primary-success":{value:"none"},"shadow-button-primary-success-hover":{value:"none"},"shadow-button-primary-success-inset":{value:"none"}},space:{"space-card-gap":{value:a("space-200")}},text:{"text-heading-2xl-font-size":{value:a("font-size-800")},"text-heading-xl-font-size":{value:a("font-size-550")},"text-heading-xl-font-line-height":{value:a("font-line-height-700")},"text-heading-lg-font-size":{value:a("font-size-450")},"text-heading-md-font-size":{value:a("font-size-400")},"text-heading-sm-font-size":{value:a("font-size-350")},"text-body-lg-font-size":{value:a("font-size-450")},"text-body-lg-font-line-height":{value:a("font-line-height-700")},"text-body-md-font-size":{value:a("font-size-400")},"text-body-md-font-line-height":{value:a("font-line-height-600")},"text-body-sm-font-size":{value:a("font-size-350")},"text-body-sm-font-line-height":{value:a("font-line-height-500")},"text-body-xs-font-size":{value:a("font-size-300")},"text-body-xs-font-line-height":{value:a("font-line-height-400")}}});A(be);var me=L({color:{"color-scheme":{value:"dark"},"color-bg":{value:e[16]},"color-bg-surface":{value:e[15]},"color-bg-fill":{value:e[15]},"color-icon":{value:e[8]},"color-icon-secondary":{value:e[12]},"color-text":{value:e[8]},"color-text-secondary":{value:e[11]},"color-bg-surface-secondary-active":{value:e[13]},"color-bg-surface-secondary-hover":{value:e[14]},"color-bg-fill-transparent":{value:S[8]},"color-bg-fill-brand":{value:e[1]},"color-text-brand-on-bg-fill":{value:e[15]},"color-bg-surface-hover":{value:e[14]},"color-bg-fill-hover":{value:e[14]},"color-bg-fill-transparent-hover":{value:S[9]},"color-bg-fill-brand-hover":{value:e[5]},"color-bg-surface-selected":{value:e[13]},"color-bg-fill-selected":{value:e[13]},"color-bg-fill-transparent-selected":{value:S[11]},"color-bg-fill-brand-selected":{value:e[9]},"color-bg-surface-active":{value:e[13]},"color-bg-fill-active":{value:e[13]},"color-bg-fill-transparent-active":{value:S[10]},"color-bg-fill-brand-active":{value:e[4]},"color-bg-surface-brand-selected":{value:e[14]},"color-border-secondary":{value:e[13]},"color-tooltip-tail-down-border-experimental":{value:"rgba(60, 60, 60, 1)"},"color-tooltip-tail-up-border-experimental":{value:"rgba(71, 71, 71, 1)"},"color-border-gradient-experimental":{value:"linear-gradient(to bottom, "+S[9]+", "+S[4]+")"},"color-border-gradient-hover-experimental":{value:"linear-gradient(to bottom, "+S[9]+", "+S[4]+")"},"color-border-gradient-selected-experimental":{value:"linear-gradient(to bottom, "+m[10]+", "+S[10]+")"},"color-border-gradient-active-experimental":{value:"linear-gradient(to bottom, "+S[10]+", "+S[4]+")"}},shadow:{"shadow-bevel-100":{value:"1px 0px 0px 0px rgba(204, 204, 204, 0.08) inset, -1px 0px 0px 0px rgba(204, 204, 204, 0.08) inset, 0px -1px 0px 0px rgba(204, 204, 204, 0.08) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.16) inset"}}});A(me);var Xe={light:se,"light-mobile":be,"light-high-contrast-experimental":ge,"dark-experimental":me},We=Xe[D];A(We);var de={light:{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(48, 48, 48, 1)","color-text-secondary":"rgba(97, 97, 97, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(235, 235, 235, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"light-mobile":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(48, 48, 48, 1)","color-text-secondary":"rgba(97, 97, 97, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(235, 235, 235, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"none","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"none","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"none","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-hover":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-inset":"0 0 0 var(--p-border-width-025) var(--p-color-border) inset","shadow-button-primary":"none","shadow-button-primary-hover":"none","shadow-button-primary-inset":"none","shadow-button-primary-critical":"none","shadow-button-primary-critical-hover":"none","shadow-button-primary-critical-inset":"none","shadow-button-primary-success":"none","shadow-button-primary-success-hover":"none","shadow-button-primary-success-inset":"none","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"0.5rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"2rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.375rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"1.75rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.125rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"1rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.875rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"1.125rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.75rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"1rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.5rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.875rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1.25rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.75rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"1rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"light-high-contrast-experimental":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"light","color-bg":"rgba(241, 241, 241, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(255, 255, 255, 1)","color-bg-surface-hover":"rgba(247, 247, 247, 1)","color-bg-surface-active":"rgba(243, 243, 243, 1)","color-bg-surface-selected":"rgba(241, 241, 241, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-hover":"rgba(241, 241, 241, 1)","color-bg-surface-secondary-active":"rgba(235, 235, 235, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(241, 241, 241, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(255, 255, 255, 1)","color-bg-fill-hover":"rgba(250, 250, 250, 1)","color-bg-fill-active":"rgba(247, 247, 247, 1)","color-bg-fill-selected":"rgba(204, 204, 204, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(48, 48, 48, 1)","color-bg-fill-brand-hover":"rgba(26, 26, 26, 1)","color-bg-fill-brand-active":"rgba(26, 26, 26, 1)","color-bg-fill-brand-selected":"rgba(48, 48, 48, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(0, 0, 0, 0.02)","color-bg-fill-transparent-hover":"rgba(0, 0, 0, 0.05)","color-bg-fill-transparent-active":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-selected":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(26, 26, 26, 1)","color-text-secondary":"rgba(26, 26, 26, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(26, 26, 26, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(255, 255, 255, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(138, 138, 138, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(138, 138, 138, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(212, 212, 212, 1)","color-tooltip-tail-up-border-experimental":"rgba(227, 227, 227, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(235, 235, 235, 1), rgba(204, 204, 204, 1) 78%, rgba(181, 181, 181, 1))","color-icon":"rgba(74, 74, 74, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(74, 74, 74, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(74, 74, 74, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07), 0rem 0.0625rem 0rem 0rem rgba(208, 208, 208, 0.40) inset, 0.0625rem 0rem 0rem 0rem #CCC inset, -0.0625rem 0rem 0rem 0rem #CCC inset, 0rem -0.0625rem 0rem 0rem #999 inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}},"dark-experimental":{border:{"border-radius-0":"0rem","border-radius-050":"0.125rem","border-radius-100":"0.25rem","border-radius-150":"0.375rem","border-radius-200":"0.5rem","border-radius-300":"0.75rem","border-radius-400":"1rem","border-radius-500":"1.25rem","border-radius-750":"1.875rem","border-radius-full":"624.9375rem","border-width-0":"0rem","border-width-0165":"0.04125rem","border-width-025":"0.0625rem","border-width-050":"0.125rem","border-width-100":"0.25rem"},breakpoints:{"breakpoints-xs":"0rem","breakpoints-sm":"30.625rem","breakpoints-md":"48rem","breakpoints-lg":"65rem","breakpoints-xl":"90rem"},color:{"color-scheme":"dark","color-bg":"rgba(26, 26, 26, 1)","color-bg-inverse":"rgba(26, 26, 26, 1)","color-bg-surface":"rgba(48, 48, 48, 1)","color-bg-surface-hover":"rgba(74, 74, 74, 1)","color-bg-surface-active":"rgba(97, 97, 97, 1)","color-bg-surface-selected":"rgba(97, 97, 97, 1)","color-bg-surface-disabled":"rgba(0, 0, 0, 0.05)","color-bg-surface-secondary":"rgba(247, 247, 247, 1)","color-bg-surface-secondary-hover":"rgba(74, 74, 74, 1)","color-bg-surface-secondary-active":"rgba(97, 97, 97, 1)","color-bg-surface-secondary-selected":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary":"rgba(243, 243, 243, 1)","color-bg-surface-tertiary-hover":"rgba(235, 235, 235, 1)","color-bg-surface-tertiary-active":"rgba(227, 227, 227, 1)","color-bg-surface-brand":"rgba(227, 227, 227, 1)","color-bg-surface-brand-hover":"rgba(235, 235, 235, 1)","color-bg-surface-brand-active":"rgba(241, 241, 241, 1)","color-bg-surface-brand-selected":"rgba(74, 74, 74, 1)","color-bg-surface-info":"rgba(234, 244, 255, 1)","color-bg-surface-info-hover":"rgba(224, 240, 255, 1)","color-bg-surface-info-active":"rgba(202, 230, 255, 1)","color-bg-surface-success":"rgba(205, 254, 225, 1)","color-bg-surface-success-hover":"rgba(180, 254, 210, 1)","color-bg-surface-success-active":"rgba(146, 254, 194, 1)","color-bg-surface-caution":"rgba(255, 248, 219, 1)","color-bg-surface-caution-hover":"rgba(255, 244, 191, 1)","color-bg-surface-caution-active":"rgba(255, 239, 157, 1)","color-bg-surface-warning":"rgba(255, 241, 227, 1)","color-bg-surface-warning-hover":"rgba(255, 235, 213, 1)","color-bg-surface-warning-active":"rgba(255, 228, 198, 1)","color-bg-surface-critical":"rgba(254, 233, 232, 1)","color-bg-surface-critical-hover":"rgba(254, 226, 225, 1)","color-bg-surface-critical-active":"rgba(254, 218, 217, 1)","color-bg-surface-emphasis":"rgba(240, 242, 255, 1)","color-bg-surface-emphasis-hover":"rgba(234, 237, 255, 1)","color-bg-surface-emphasis-active":"rgba(226, 231, 255, 1)","color-bg-surface-magic":"rgba(248, 247, 255, 1)","color-bg-surface-magic-hover":"rgba(243, 241, 255, 1)","color-bg-surface-magic-active":"rgba(233, 229, 255, 1)","color-bg-surface-inverse":"rgba(48, 48, 48, 1)","color-bg-surface-transparent":"rgba(0, 0, 0, 0)","color-bg-fill":"rgba(48, 48, 48, 1)","color-bg-fill-hover":"rgba(74, 74, 74, 1)","color-bg-fill-active":"rgba(97, 97, 97, 1)","color-bg-fill-selected":"rgba(97, 97, 97, 1)","color-bg-fill-disabled":"rgba(0, 0, 0, 0.05)","color-bg-fill-secondary":"rgba(241, 241, 241, 1)","color-bg-fill-secondary-hover":"rgba(235, 235, 235, 1)","color-bg-fill-secondary-active":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary":"rgba(227, 227, 227, 1)","color-bg-fill-tertiary-hover":"rgba(212, 212, 212, 1)","color-bg-fill-tertiary-active":"rgba(204, 204, 204, 1)","color-bg-fill-brand":"rgba(255, 255, 255, 1)","color-bg-fill-brand-hover":"rgba(243, 243, 243, 1)","color-bg-fill-brand-active":"rgba(247, 247, 247, 1)","color-bg-fill-brand-selected":"rgba(212, 212, 212, 1)","color-bg-fill-brand-disabled":"rgba(0, 0, 0, 0.17)","color-bg-fill-info":"rgba(145, 208, 255, 1)","color-bg-fill-info-hover":"rgba(81, 192, 255, 1)","color-bg-fill-info-active":"rgba(0, 148, 213, 1)","color-bg-fill-info-secondary":"rgba(213, 235, 255, 1)","color-bg-fill-success":"rgba(41, 132, 90, 1)","color-bg-fill-success-hover":"rgba(19, 111, 69, 1)","color-bg-fill-success-active":"rgba(12, 81, 50, 1)","color-bg-fill-success-secondary":"rgba(180, 254, 210, 1)","color-bg-fill-warning":"rgba(255, 184, 0, 1)","color-bg-fill-warning-hover":"rgba(229, 165, 0, 1)","color-bg-fill-warning-active":"rgba(178, 132, 0, 1)","color-bg-fill-warning-secondary":"rgba(255, 214, 164, 1)","color-bg-fill-caution":"rgba(255, 230, 0, 1)","color-bg-fill-caution-hover":"rgba(234, 211, 0, 1)","color-bg-fill-caution-active":"rgba(225, 203, 0, 1)","color-bg-fill-caution-secondary":"rgba(255, 235, 120, 1)","color-bg-fill-critical":"rgba(229, 28, 0, 1)","color-bg-fill-critical-hover":"rgba(181, 38, 11, 1)","color-bg-fill-critical-active":"rgba(142, 31, 11, 1)","color-bg-fill-critical-selected":"rgba(142, 31, 11, 1)","color-bg-fill-critical-secondary":"rgba(254, 211, 209, 1)","color-bg-fill-emphasis":"rgba(0, 91, 211, 1)","color-bg-fill-emphasis-hover":"rgba(0, 66, 153, 1)","color-bg-fill-emphasis-active":"rgba(0, 46, 106, 1)","color-bg-fill-magic":"rgba(128, 81, 255, 1)","color-bg-fill-magic-secondary":"rgba(233, 229, 255, 1)","color-bg-fill-magic-secondary-hover":"rgba(228, 222, 255, 1)","color-bg-fill-magic-secondary-active":"rgba(223, 217, 255, 1)","color-bg-fill-inverse":"rgba(48, 48, 48, 1)","color-bg-fill-inverse-hover":"rgba(74, 74, 74, 1)","color-bg-fill-inverse-active":"rgba(97, 97, 97, 1)","color-bg-fill-transparent":"rgba(255, 255, 255, 0.11)","color-bg-fill-transparent-hover":"rgba(255, 255, 255, 0.17)","color-bg-fill-transparent-active":"rgba(255, 255, 255, 0.20)","color-bg-fill-transparent-selected":"rgba(255, 255, 255, 0.28)","color-bg-fill-transparent-secondary":"rgba(0, 0, 0, 0.06)","color-bg-fill-transparent-secondary-hover":"rgba(0, 0, 0, 0.08)","color-bg-fill-transparent-secondary-active":"rgba(0, 0, 0, 0.11)","color-text":"rgba(227, 227, 227, 1)","color-text-secondary":"rgba(181, 181, 181, 1)","color-text-disabled":"rgba(181, 181, 181, 1)","color-text-link":"rgba(0, 91, 211, 1)","color-text-link-hover":"rgba(0, 66, 153, 1)","color-text-link-active":"rgba(0, 46, 106, 1)","color-text-brand":"rgba(74, 74, 74, 1)","color-text-brand-hover":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill":"rgba(48, 48, 48, 1)","color-text-brand-on-bg-fill-hover":"rgba(227, 227, 227, 1)","color-text-brand-on-bg-fill-active":"rgba(204, 204, 204, 1)","color-text-brand-on-bg-fill-disabled":"rgba(255, 255, 255, 1)","color-text-info":"rgba(0, 58, 90, 1)","color-text-info-hover":"rgba(0, 58, 90, 1)","color-text-info-active":"rgba(0, 33, 51, 1)","color-text-info-secondary":"rgba(0, 124, 180, 1)","color-text-info-on-bg-fill":"rgba(0, 33, 51, 1)","color-text-success":"rgba(12, 81, 50, 1)","color-text-success-hover":"rgba(8, 61, 37, 1)","color-text-success-active":"rgba(9, 42, 27, 1)","color-text-success-secondary":"rgba(41, 132, 90, 1)","color-text-success-on-bg-fill":"rgba(248, 255, 251, 1)","color-text-caution":"rgba(79, 71, 0, 1)","color-text-caution-hover":"rgba(51, 46, 0, 1)","color-text-caution-active":"rgba(31, 28, 0, 1)","color-text-caution-secondary":"rgba(130, 117, 0, 1)","color-text-caution-on-bg-fill":"rgba(51, 46, 0, 1)","color-text-warning":"rgba(94, 66, 0, 1)","color-text-warning-hover":"rgba(65, 45, 0, 1)","color-text-warning-active":"rgba(37, 26, 0, 1)","color-text-warning-secondary":"rgba(149, 111, 0, 1)","color-text-warning-on-bg-fill":"rgba(37, 26, 0, 1)","color-text-critical":"rgba(142, 31, 11, 1)","color-text-critical-hover":"rgba(95, 21, 7, 1)","color-text-critical-active":"rgba(47, 10, 4, 1)","color-text-critical-secondary":"rgba(229, 28, 0, 1)","color-text-critical-on-bg-fill":"rgba(255, 251, 251, 1)","color-text-emphasis":"rgba(0, 91, 211, 1)","color-text-emphasis-hover":"rgba(0, 66, 153, 1)","color-text-emphasis-active":"rgba(0, 46, 106, 1)","color-text-emphasis-on-bg-fill":"rgba(252, 253, 255, 1)","color-text-emphasis-on-bg-fill-hover":"rgba(226, 231, 255, 1)","color-text-emphasis-on-bg-fill-active":"rgba(213, 220, 255, 1)","color-text-magic":"rgba(87, 0, 209, 1)","color-text-magic-secondary":"rgba(113, 38, 255, 1)","color-text-magic-on-bg-fill":"rgba(253, 253, 255, 1)","color-text-inverse":"rgba(227, 227, 227, 1)","color-text-inverse-secondary":"rgba(181, 181, 181, 1)","color-text-link-inverse":"rgba(197, 208, 255, 1)","color-border":"rgba(227, 227, 227, 1)","color-border-hover":"rgba(204, 204, 204, 1)","color-border-disabled":"rgba(235, 235, 235, 1)","color-border-secondary":"rgba(97, 97, 97, 1)","color-border-tertiary":"rgba(204, 204, 204, 1)","color-border-focus":"rgba(0, 91, 211, 1)","color-border-brand":"rgba(227, 227, 227, 1)","color-border-info":"rgba(168, 216, 255, 1)","color-border-success":"rgba(146, 254, 194, 1)","color-border-caution":"rgba(255, 235, 120, 1)","color-border-warning":"rgba(255, 200, 121, 1)","color-border-critical":"rgba(254, 195, 193, 1)","color-border-critical-secondary":"rgba(142, 31, 11, 1)","color-border-emphasis":"rgba(0, 91, 211, 1)","color-border-emphasis-hover":"rgba(0, 66, 153, 1)","color-border-emphasis-active":"rgba(0, 46, 106, 1)","color-border-magic":"rgba(228, 222, 255, 1)","color-border-magic-secondary":"rgba(148, 116, 255, 1)","color-border-magic-secondary-hover":"rgba(128, 81, 255, 1)","color-border-inverse":"rgba(97, 97, 97, 1)","color-border-inverse-hover":"rgba(204, 204, 204, 1)","color-border-inverse-active":"rgba(227, 227, 227, 1)","color-tooltip-tail-down-border-experimental":"rgba(60, 60, 60, 1)","color-tooltip-tail-up-border-experimental":"rgba(71, 71, 71, 1)","color-border-gradient-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.17), rgba(255, 255, 255, 0.03))","color-border-gradient-hover-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.17), rgba(255, 255, 255, 0.03))","color-border-gradient-selected-experimental":"linear-gradient(to bottom, rgba(0, 0, 0, 0.20), rgba(255, 255, 255, 0.20))","color-border-gradient-active-experimental":"linear-gradient(to bottom, rgba(255, 255, 255, 0.20), rgba(255, 255, 255, 0.03))","color-icon":"rgba(227, 227, 227, 1)","color-icon-hover":"rgba(48, 48, 48, 1)","color-icon-active":"rgba(26, 26, 26, 1)","color-icon-disabled":"rgba(204, 204, 204, 1)","color-icon-secondary":"rgba(138, 138, 138, 1)","color-icon-secondary-hover":"rgba(97, 97, 97, 1)","color-icon-secondary-active":"rgba(74, 74, 74, 1)","color-icon-brand":"rgba(26, 26, 26, 1)","color-icon-info":"rgba(0, 148, 213, 1)","color-icon-success":"rgba(41, 132, 90, 1)","color-icon-caution":"rgba(153, 138, 0, 1)","color-icon-warning":"rgba(178, 132, 0, 1)","color-icon-critical":"rgba(239, 77, 47, 1)","color-icon-emphasis":"rgba(0, 91, 211, 1)","color-icon-emphasis-hover":"rgba(0, 66, 153, 1)","color-icon-emphasis-active":"rgba(0, 46, 106, 1)","color-icon-magic":"rgba(128, 81, 255, 1)","color-icon-inverse":"rgba(227, 227, 227, 1)","color-avatar-bg-fill":"rgba(181, 181, 181, 1)","color-avatar-five-bg-fill":"rgba(253, 75, 146, 1)","color-avatar-five-text-on-bg-fill":"rgba(255, 246, 248, 1)","color-avatar-four-bg-fill":"rgba(81, 192, 255, 1)","color-avatar-four-text-on-bg-fill":"rgba(0, 33, 51, 1)","color-avatar-one-bg-fill":"rgba(197, 48, 197, 1)","color-avatar-one-text-on-bg-fill":"rgba(253, 239, 253, 1)","color-avatar-seven-bg-fill":"rgba(148, 116, 255, 1)","color-avatar-seven-text-on-bg-fill":"rgba(248, 247, 255, 1)","color-avatar-six-bg-fill":"rgba(37, 232, 43, 1)","color-avatar-six-text-on-bg-fill":"rgba(3, 61, 5, 1)","color-avatar-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-avatar-three-bg-fill":"rgba(44, 224, 212, 1)","color-avatar-three-text-on-bg-fill":"rgba(3, 60, 57, 1)","color-avatar-two-bg-fill":"rgba(56, 250, 163, 1)","color-avatar-two-text-on-bg-fill":"rgba(12, 81, 50, 1)","color-backdrop-bg":"rgba(0, 0, 0, 0.71)","color-button-gradient-bg-fill":"linear-gradient(180deg, rgba(48, 48, 48, 0) 63.53%, rgba(255, 255, 255, 0.15) 100%)","color-checkbox-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-checkbox-icon-disabled":"rgba(255, 255, 255, 1)","color-input-bg-surface":"rgba(253, 253, 253, 1)","color-input-bg-surface-hover":"rgba(250, 250, 250, 1)","color-input-bg-surface-active":"rgba(247, 247, 247, 1)","color-input-border":"rgba(138, 138, 138, 1)","color-input-border-hover":"rgba(97, 97, 97, 1)","color-input-border-active":"rgba(26, 26, 26, 1)","color-nav-bg":"rgba(235, 235, 235, 1)","color-nav-bg-surface":"rgba(0, 0, 0, 0.02)","color-nav-bg-surface-hover":"rgba(241, 241, 241, 1)","color-nav-bg-surface-active":"rgba(250, 250, 250, 1)","color-nav-bg-surface-selected":"rgba(250, 250, 250, 1)","color-radio-button-bg-surface-disabled":"rgba(0, 0, 0, 0.08)","color-radio-button-icon-disabled":"rgba(255, 255, 255, 1)","color-video-thumbnail-play-button-bg-fill-hover":"rgba(0, 0, 0, 0.81)","color-video-thumbnail-play-button-bg-fill":"rgba(0, 0, 0, 0.71)","color-video-thumbnail-play-button-text-on-bg-fill":"rgba(255, 255, 255, 1)","color-scrollbar-thumb-bg-hover":"rgba(138, 138, 138, 1)"},font:{"font-family-sans":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","font-family-mono":"ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace","font-size-275":"0.6875rem","font-size-300":"0.75rem","font-size-325":"0.8125rem","font-size-350":"0.875rem","font-size-400":"1rem","font-size-450":"1.125rem","font-size-500":"1.25rem","font-size-550":"1.375rem","font-size-600":"1.5rem","font-size-750":"1.875rem","font-size-800":"2rem","font-size-900":"2.25rem","font-size-1000":"2.5rem","font-weight-regular":"450","font-weight-medium":"550","font-weight-semibold":"650","font-weight-bold":"700","font-letter-spacing-densest":"-0.03375rem","font-letter-spacing-denser":"-0.01875rem","font-letter-spacing-dense":"-0.0125rem","font-letter-spacing-normal":"0rem","font-line-height-300":"0.75rem","font-line-height-400":"1rem","font-line-height-500":"1.25rem","font-line-height-600":"1.5rem","font-line-height-700":"1.75rem","font-line-height-800":"2rem","font-line-height-1000":"2.5rem","font-line-height-1200":"3rem"},height:{"height-0":"0rem","height-025":"0.0625rem","height-050":"0.125rem","height-100":"0.25rem","height-150":"0.375rem","height-200":"0.5rem","height-300":"0.75rem","height-400":"1rem","height-500":"1.25rem","height-600":"1.5rem","height-700":"1.75rem","height-800":"2rem","height-900":"2.25rem","height-1000":"2.5rem","height-1200":"3rem","height-1600":"4rem","height-2000":"5rem","height-2400":"6rem","height-2800":"7rem","height-3200":"8rem"},motion:{"motion-duration-0":"0ms","motion-duration-50":"50ms","motion-duration-100":"100ms","motion-duration-150":"150ms","motion-duration-200":"200ms","motion-duration-250":"250ms","motion-duration-300":"300ms","motion-duration-350":"350ms","motion-duration-400":"400ms","motion-duration-450":"450ms","motion-duration-500":"500ms","motion-duration-5000":"5000ms","motion-ease":"cubic-bezier(0.25, 0.1, 0.25, 1)","motion-ease-in":"cubic-bezier(0.42, 0, 1, 1)","motion-ease-out":"cubic-bezier(0.19, 0.91, 0.38, 1)","motion-ease-in-out":"cubic-bezier(0.42, 0, 0.58, 1)","motion-linear":"cubic-bezier(0, 0, 1, 1)","motion-keyframes-bounce":"{ from, 65%, 85% { transform: scale(1) } 75% { transform: scale(0.85) } 82.5% { transform: scale(1.05) } }","motion-keyframes-fade-in":"{ to { opacity: 1 } }","motion-keyframes-pulse":"{ from, 75% { transform: scale(0.85); opacity: 1; } to { transform: scale(2.5); opacity: 0; } }","motion-keyframes-spin":"{ to { transform: rotate(1turn) } }","motion-keyframes-appear-above":"{ from { transform: translateY(var(--p-space-100)); opacity: 0; } to { transform: none; opacity: 1; } }","motion-keyframes-appear-below":"{ from { transform: translateY(calc(var(--p-space-100) * -1)); opacity: 0; } to { transform: none; opacity: 1; } }"},shadow:{"shadow-0":"none","shadow-100":"0rem 0.0625rem 0rem 0rem rgba(26, 26, 26, 0.07)","shadow-200":"0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07)","shadow-300":"0rem 0.25rem 0.375rem -0.125rem rgba(26, 26, 26, 0.20)","shadow-400":"0rem 0.5rem 1rem -0.25rem rgba(26, 26, 26, 0.22)","shadow-500":"0rem 0.75rem 1.25rem -0.5rem rgba(26, 26, 26, 0.24)","shadow-600":"0rem 1.25rem 1.25rem -0.5rem rgba(26, 26, 26, 0.28)","shadow-bevel-100":"0.0625rem 0rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, -0.0625rem 0rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, 0rem -0.0625rem 0rem 0rem rgba(204, 204, 204, 0.08) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.16) inset","shadow-inset-100":"0rem 0.0625rem 0.125rem 0rem rgba(26, 26, 26, 0.15) inset, 0rem 0.0625rem 0.0625rem 0rem rgba(26, 26, 26, 0.15) inset","shadow-inset-200":"0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.20) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset, -0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.12) inset","shadow-button":"0rem -0.0625rem 0rem 0rem #b5b5b5 inset, 0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.1) inset, 0rem 0.03125rem 0rem 0.09375rem #FFF inset","shadow-button-hover":"0rem 0.0625rem 0rem 0rem #EBEBEB inset, -0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0.0625rem 0rem 0rem 0rem #EBEBEB inset, 0rem -0.0625rem 0rem 0rem #CCC inset","shadow-button-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(26, 26, 26, 0.122) inset, 0rem 0.125rem 0.0625rem 0rem rgba(26, 26, 26, 0.2) inset","shadow-button-primary":"0rem -0.0625rem 0rem 0.0625rem rgba(0, 0, 0, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(48, 48, 48, 1) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.25) inset;","shadow-button-primary-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.24) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.0625rem 0rem 0rem #000 inset, 0rem -0.0625rem 0rem 0.0625rem #1A1A1A","shadow-button-primary-inset":"0rem 0.1875rem 0rem 0rem rgb(0, 0, 0) inset","shadow-button-primary-critical":"0rem -0.0625rem 0rem 0.0625rem rgba(142, 31, 11, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(181, 38, 11, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.349) inset","shadow-button-primary-critical-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-critical-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-button-primary-success":"0rem -0.0625rem 0rem 0.0625rem rgba(12, 81, 50, 0.8) inset, 0rem 0rem 0rem 0.0625rem rgba(19, 111, 69, 0.8) inset, 0rem 0.03125rem 0rem 0.09375rem rgba(255, 255, 255, 0.251) inset","shadow-button-primary-success-hover":"0rem 0.0625rem 0rem 0rem rgba(255, 255, 255, 0.48) inset, 0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, -0.0625rem 0rem 0rem 0rem rgba(255, 255, 255, 0.20) inset, 0rem -0.09375rem 0rem 0rem rgba(0, 0, 0, 0.25) inset","shadow-button-primary-success-inset":"-0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0.0625rem 0rem 0.0625rem 0rem rgba(0, 0, 0, 0.2) inset, 0rem 0.125rem 0rem 0rem rgba(0, 0, 0, 0.6) inset","shadow-border-inset":"0rem 0rem 0rem 0.0625rem rgba(0, 0, 0, 0.08) inset"},space:{"space-0":"0rem","space-025":"0.0625rem","space-050":"0.125rem","space-100":"0.25rem","space-150":"0.375rem","space-200":"0.5rem","space-300":"0.75rem","space-400":"1rem","space-500":"1.25rem","space-600":"1.5rem","space-800":"2rem","space-1000":"2.5rem","space-1200":"3rem","space-1600":"4rem","space-2000":"5rem","space-2400":"6rem","space-2800":"7rem","space-3200":"8rem","space-button-group-gap":"0.5rem","space-card-gap":"1rem","space-card-padding":"1rem","space-table-cell-padding":"0.375rem"},text:{"text-heading-3xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-3xl-font-size":"2.25rem","text-heading-3xl-font-weight":"700","text-heading-3xl-font-letter-spacing":"-0.03375rem","text-heading-3xl-font-line-height":"3rem","text-heading-2xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-2xl-font-size":"1.875rem","text-heading-2xl-font-weight":"700","text-heading-2xl-font-letter-spacing":"-0.01875rem","text-heading-2xl-font-line-height":"2.5rem","text-heading-xl-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xl-font-size":"1.5rem","text-heading-xl-font-weight":"700","text-heading-xl-font-letter-spacing":"-0.0125rem","text-heading-xl-font-line-height":"2rem","text-heading-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-lg-font-size":"1.25rem","text-heading-lg-font-weight":"650","text-heading-lg-font-letter-spacing":"-0.0125rem","text-heading-lg-font-line-height":"1.5rem","text-heading-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-md-font-size":"0.875rem","text-heading-md-font-weight":"650","text-heading-md-font-letter-spacing":"0rem","text-heading-md-font-line-height":"1.25rem","text-heading-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-sm-font-size":"0.8125rem","text-heading-sm-font-weight":"650","text-heading-sm-font-letter-spacing":"0rem","text-heading-sm-font-line-height":"1.25rem","text-heading-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-heading-xs-font-size":"0.75rem","text-heading-xs-font-weight":"650","text-heading-xs-font-letter-spacing":"0rem","text-heading-xs-font-line-height":"1rem","text-body-lg-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-lg-font-size":"0.875rem","text-body-lg-font-weight":"450","text-body-lg-font-letter-spacing":"0rem","text-body-lg-font-line-height":"1.25rem","text-body-md-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-md-font-size":"0.8125rem","text-body-md-font-weight":"450","text-body-md-font-letter-spacing":"0rem","text-body-md-font-line-height":"1.25rem","text-body-sm-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-sm-font-size":"0.75rem","text-body-sm-font-weight":"450","text-body-sm-font-letter-spacing":"0rem","text-body-sm-font-line-height":"1rem","text-body-xs-font-family":"'Inter', -apple-system, BlinkMacSystemFont, 'San Francisco', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif","text-body-xs-font-size":"0.6875rem","text-body-xs-font-weight":"450","text-body-xs-font-letter-spacing":"0rem","text-body-xs-font-line-height":"0.75rem"},width:{"width-0":"0rem","width-025":"0.0625rem","width-050":"0.125rem","width-100":"0.25rem","width-150":"0.375rem","width-200":"0.5rem","width-300":"0.75rem","width-400":"1rem","width-500":"1.25rem","width-600":"1.5rem","width-700":"1.75rem","width-800":"2rem","width-900":"2.25rem","width-1000":"2.5rem","width-1200":"3rem","width-1600":"4rem","width-2000":"5rem","width-2400":"6rem","width-2800":"7rem","width-3200":"8rem"},zIndex:{"z-index-0":"auto","z-index-1":"100","z-index-2":"400","z-index-3":"510","z-index-4":"512","z-index-5":"513","z-index-6":"514","z-index-7":"515","z-index-8":"516","z-index-9":"517","z-index-10":"518","z-index-11":"519","z-index-12":"520"}}},Ke=de[D];le(de[D]);const fe=typeof window>"u"||typeof document>"u",Ze=fe?U.useEffect:U.useLayoutEffect,ue={navigationBarCollapsed:"767.95px",stackedContent:"1039.95px"},he={media:"",addListener:N,removeListener:N,matches:!1,onchange:N,addEventListener:N,removeEventListener:N,dispatchEvent:r=>!0};function N(){}function br(){return typeof window>"u"?he:window.matchMedia(`(max-width: ${ue.navigationBarCollapsed})`)}function mr(){return typeof window>"u"?he:window.matchMedia(`(max-width: ${ue.stackedContent})`)}const Y=Je(Ke.breakpoints);function ee(r,t){return Object.fromEntries(!fe&&!t?Y.map(([i,g])=>[i,window.matchMedia(g).matches]):Y.map(([i])=>[i,!1]))}function dr(r){const[t,i]=U.useState(ee(r==null?void 0:r.defaults,!0));return Ze(()=>{const g=Y.map(([d,k])=>window.matchMedia(k)),s=()=>i(ee());return g.forEach(d=>{d.addListener?d.addListener(s):d.addEventListener("change",s)}),s(),()=>{g.forEach(d=>{d.removeListener?d.removeListener(s):d.removeEventListener("change",s)})}},[]),t}function Je(r){return Object.entries(Ne(r)).map(([i,g])=>Object.entries(g).map(([s,d])=>[`${i.split("-")[1]}${er(s)}`,d])).flat()}function er(r){return r.charAt(0).toUpperCase()+r.slice(1)}const fr=U.createContext(void 0),ur=U.createContext(void 0);function hr(){const[r,t]=U.useState(!1);return U.useEffect(()=>{t(!0)},[]),r}export{fr as I,ur as L,Ke as a,or as b,hr as c,nr as d,gr as e,sr as f,D as g,cr as h,fe as i,ir as j,Ze as k,tr as l,br as n,ar as o,lr as p,mr as s,de as t,dr as u};
