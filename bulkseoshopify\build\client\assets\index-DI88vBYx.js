import{r as E}from"./index-BJYSoprK.js";/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function B(){return B=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},B.apply(this,arguments)}var V;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(V||(V={}));const Jt="popstate";function ia(e){e===void 0&&(e={});function t(n,a){let{pathname:s,search:o,hash:c}=n.location;return Je("",{pathname:s,search:o,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(n,a){return typeof a=="string"?a:Ge(a)}return Wr(t,r,null,e)}function O(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Fe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Hr(){return Math.random().toString(36).substr(2,8)}function Yt(e,t){return{usr:e.state,key:e.key,idx:t}}function Je(e,t,r,n){return r===void 0&&(r=null),B({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?ve(t):t,{state:r,key:t&&t.key||n||Hr()})}function Ge(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function ve(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Wr(e,t,r,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:s=!1}=n,o=a.history,c=V.Pop,d=null,m=v();m==null&&(m=0,o.replaceState(B({},o.state,{idx:m}),""));function v(){return(o.state||{idx:null}).idx}function h(){c=V.Pop;let S=v(),A=S==null?null:S-m;m=S,d&&d({action:c,location:C.location,delta:A})}function y(S,A){c=V.Push;let T=Je(C.location,S,A);m=v()+1;let J=Yt(T,m),te=C.createHref(T);try{o.pushState(J,"",te)}catch(Q){if(Q instanceof DOMException&&Q.name==="DataCloneError")throw Q;a.location.assign(te)}s&&d&&d({action:c,location:C.location,delta:1})}function b(S,A){c=V.Replace;let T=Je(C.location,S,A);m=v();let J=Yt(T,m),te=C.createHref(T);o.replaceState(J,"",te),s&&d&&d({action:c,location:C.location,delta:0})}function M(S){let A=a.location.origin!=="null"?a.location.origin:a.location.href,T=typeof S=="string"?S:Ge(S);return T=T.replace(/ $/,"%20"),O(A,"No window.location.(origin|href) available to create URL for href: "+T),new URL(T,A)}let C={get action(){return c},get location(){return e(a,o)},listen(S){if(d)throw new Error("A history only accepts one active listener");return a.addEventListener(Jt,h),d=S,()=>{a.removeEventListener(Jt,h),d=null}},createHref(S){return t(a,S)},createURL:M,encodeLocation(S){let A=M(S);return{pathname:A.pathname,search:A.search,hash:A.hash}},push:y,replace:b,go(S){return o.go(S)}};return C}var I;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(I||(I={}));const Kr=new Set(["lazy","caseSensitive","path","id","index","children"]);function Vr(e){return e.index===!0}function ft(e,t,r,n){return r===void 0&&(r=[]),n===void 0&&(n={}),e.map((a,s)=>{let o=[...r,String(s)],c=typeof a.id=="string"?a.id:o.join("-");if(O(a.index!==!0||!a.children,"Cannot specify children on an index route"),O(!n[c],'Found a route id collision on id "'+c+`".  Route id's must be globally unique within Data Router usages`),Vr(a)){let d=B({},a,t(a),{id:c});return n[c]=d,d}else{let d=B({},a,t(a),{id:c,children:void 0});return n[c]=d,a.children&&(d.children=ft(a.children,t,o,n)),d}})}function xe(e,t,r){return r===void 0&&(r="/"),ct(e,t,r,!1)}function ct(e,t,r,n){let a=typeof t=="string"?ve(t):t,s=Xe(a.pathname||"/",r);if(s==null)return null;let o=fr(e);$r(o);let c=null;for(let d=0;c==null&&d<o.length;++d){let m=nn(s);c=tn(o[d],m,n)}return c}function cr(e,t){let{route:r,pathname:n,params:a}=e;return{id:r.id,pathname:n,params:a,data:t[r.id],handle:r.handle}}function fr(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let a=(s,o,c)=>{let d={relativePath:c===void 0?s.path||"":c,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};d.relativePath.startsWith("/")&&(O(d.relativePath.startsWith(n),'Absolute route path "'+d.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),d.relativePath=d.relativePath.slice(n.length));let m=ce([n,d.relativePath]),v=r.concat(d);s.children&&s.children.length>0&&(O(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+m+'".')),fr(s.children,t,v,m)),!(s.path==null&&!s.index)&&t.push({path:m,score:qr(m,s.index),routesMeta:v})};return e.forEach((s,o)=>{var c;if(s.path===""||!((c=s.path)!=null&&c.includes("?")))a(s,o);else for(let d of hr(s.path))a(s,o,d)}),t}function hr(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,a=r.endsWith("?"),s=r.replace(/\?$/,"");if(n.length===0)return a?[s,""]:[s];let o=hr(n.join("/")),c=[];return c.push(...o.map(d=>d===""?s:[s,d].join("/"))),a&&c.push(...o),c.map(d=>e.startsWith("/")&&d===""?"/":d)}function $r(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:en(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Jr=/^:[\w-]+$/,Yr=3,Gr=2,Xr=1,Qr=10,Zr=-2,Gt=e=>e==="*";function qr(e,t){let r=e.split("/"),n=r.length;return r.some(Gt)&&(n+=Zr),t&&(n+=Gr),r.filter(a=>!Gt(a)).reduce((a,s)=>a+(Jr.test(s)?Yr:s===""?Xr:Qr),n)}function en(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function tn(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,a={},s="/",o=[];for(let c=0;c<n.length;++c){let d=n[c],m=c===n.length-1,v=s==="/"?t:t.slice(s.length)||"/",h=Xt({path:d.relativePath,caseSensitive:d.caseSensitive,end:m},v),y=d.route;if(!h&&m&&r&&!n[n.length-1].route.index&&(h=Xt({path:d.relativePath,caseSensitive:d.caseSensitive,end:!1},v)),!h)return null;Object.assign(a,h.params),o.push({params:a,pathname:ce([s,h.pathname]),pathnameBase:ln(ce([s,h.pathnameBase])),route:y}),h.pathnameBase!=="/"&&(s=ce([s,h.pathnameBase]))}return o}function Xt(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=rn(e.path,e.caseSensitive,e.end),a=t.match(r);if(!a)return null;let s=a[0],o=s.replace(/(.)\/+$/,"$1"),c=a.slice(1);return{params:n.reduce((m,v,h)=>{let{paramName:y,isOptional:b}=v;if(y==="*"){let C=c[h]||"";o=s.slice(0,s.length-C.length).replace(/(.)\/+$/,"$1")}const M=c[h];return b&&!M?m[y]=void 0:m[y]=(M||"").replace(/%2F/g,"/"),m},{}),pathname:s,pathnameBase:o,pattern:e}}function rn(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Fe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,c,d)=>(n.push({paramName:c,isOptional:d!=null}),d?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function nn(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Fe(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Xe(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function an(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:a=""}=typeof e=="string"?ve(e):e;return{pathname:r?r.startsWith("/")?r:on(r,t):t,search:sn(n),hash:dn(a)}}function on(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?r.length>1&&r.pop():a!=="."&&r.push(a)}),r.length>1?r.join("/"):"/"}function bt(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function mr(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Mt(e,t){let r=mr(e);return t?r.map((n,a)=>a===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function Ct(e,t,r,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=ve(e):(a=B({},e),O(!a.pathname||!a.pathname.includes("?"),bt("?","pathname","search",a)),O(!a.pathname||!a.pathname.includes("#"),bt("#","pathname","hash",a)),O(!a.search||!a.search.includes("#"),bt("#","search","hash",a)));let s=e===""||a.pathname==="",o=s?"/":a.pathname,c;if(o==null)c=r;else{let h=t.length-1;if(!n&&o.startsWith("..")){let y=o.split("/");for(;y[0]==="..";)y.shift(),h-=1;a.pathname=y.join("/")}c=h>=0?t[h]:"/"}let d=an(a,c),m=o&&o!=="/"&&o.endsWith("/"),v=(s||o===".")&&r.endsWith("/");return!d.pathname.endsWith("/")&&(m||v)&&(d.pathname+="/"),d}const ce=e=>e.join("/").replace(/\/\/+/g,"/"),ln=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),sn=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,dn=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class un{constructor(t,r){this.type="DataWithResponseInit",this.data=t,this.init=r||null}}function oa(e,t){return new un(e,typeof t=="number"?{status:t}:t)}class Rt extends Error{}class la{constructor(t,r){this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],O(t&&typeof t=="object"&&!Array.isArray(t),"defer() only accepts plain objects");let n;this.abortPromise=new Promise((s,o)=>n=o),this.controller=new AbortController;let a=()=>n(new Rt("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(t).reduce((s,o)=>{let[c,d]=o;return Object.assign(s,{[c]:this.trackPromise(c,d)})},{}),this.done&&this.unlistenAbortSignal(),this.init=r}trackPromise(t,r){if(!(r instanceof Promise))return r;this.deferredKeys.push(t),this.pendingKeysSet.add(t);let n=Promise.race([r,this.abortPromise]).then(a=>this.onSettle(n,t,void 0,a),a=>this.onSettle(n,t,a));return n.catch(()=>{}),Object.defineProperty(n,"_tracked",{get:()=>!0}),n}onSettle(t,r,n,a){if(this.controller.signal.aborted&&n instanceof Rt)return this.unlistenAbortSignal(),Object.defineProperty(t,"_error",{get:()=>n}),Promise.reject(n);if(this.pendingKeysSet.delete(r),this.done&&this.unlistenAbortSignal(),n===void 0&&a===void 0){let s=new Error('Deferred data for key "'+r+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(t,"_error",{get:()=>s}),this.emit(!1,r),Promise.reject(s)}return a===void 0?(Object.defineProperty(t,"_error",{get:()=>n}),this.emit(!1,r),Promise.reject(n)):(Object.defineProperty(t,"_data",{get:()=>a}),this.emit(!1,r),a)}emit(t,r){this.subscribers.forEach(n=>n(t,r))}subscribe(t){return this.subscribers.add(t),()=>this.subscribers.delete(t)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach((t,r)=>this.pendingKeysSet.delete(r)),this.emit(!0)}async resolveData(t){let r=!1;if(!this.done){let n=()=>this.cancel();t.addEventListener("abort",n),r=await new Promise(a=>{this.subscribe(s=>{t.removeEventListener("abort",n),(s||this.done)&&a(s)})})}return r}get done(){return this.pendingKeysSet.size===0}get unwrappedData(){return O(this.data!==null&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce((t,r)=>{let[n,a]=r;return Object.assign(t,{[n]:fn(a)})},{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function cn(e){return e instanceof Promise&&e._tracked===!0}function fn(e){if(!cn(e))return e;if(e._error)throw e._error;return e._data}const sa=function(t,r){r===void 0&&(r=302);let n=r;typeof n=="number"?n={status:n}:typeof n.status>"u"&&(n.status=302);let a=new Headers(n.headers);return a.set("Location",t),new Response(null,B({},n,{headers:a}))};class Pt{constructor(t,r,n,a){a===void 0&&(a=!1),this.status=t,this.statusText=r||"",this.internal=a,n instanceof Error?(this.data=n.toString(),this.error=n):this.data=n}}function Ye(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const pr=["post","put","patch","delete"],hn=new Set(pr),mn=["get",...pr],pn=new Set(mn),vn=new Set([301,302,303,307,308]),gn=new Set([307,308]),wt={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},yn={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ke={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},Lt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,bn=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),vr="remix-router-transitions";function da(e){const t=e.window?e.window:typeof window<"u"?window:void 0,r=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",n=!r;O(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let a;if(e.mapRouteProperties)a=e.mapRouteProperties;else if(e.detectErrorBoundary){let i=e.detectErrorBoundary;a=l=>({hasErrorBoundary:i(l)})}else a=bn;let s={},o=ft(e.routes,a,void 0,s),c,d=e.basename||"/",m=e.dataStrategy||Pn,v=e.patchRoutesOnNavigation,h=B({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),y=null,b=new Set,M=null,C=null,S=null,A=e.hydrationData!=null,T=xe(o,e.history.location,d),J=!1,te=null;if(T==null&&!v){let i=Z(404,{pathname:e.history.location.pathname}),{matches:l,route:u}=lr(o);T=l,te={[u.id]:i}}T&&!e.hydrationData&&ot(T,o,e.history.location.pathname).active&&(T=null);let Q;if(T)if(T.some(i=>i.route.lazy))Q=!1;else if(!T.some(i=>i.route.loader))Q=!0;else if(h.v7_partialHydration){let i=e.hydrationData?e.hydrationData.loaderData:null,l=e.hydrationData?e.hydrationData.errors:null;if(l){let u=T.findIndex(p=>l[p.route.id]!==void 0);Q=T.slice(0,u+1).every(p=>!Dt(p.route,i,l))}else Q=T.every(u=>!Dt(u.route,i,l))}else Q=e.hydrationData!=null;else if(Q=!1,T=[],h.v7_partialHydration){let i=ot(null,o,e.history.location.pathname);i.active&&i.matches&&(J=!0,T=i.matches)}let Ae,f={historyAction:e.history.action,location:e.history.location,matches:T,initialized:Q,navigation:wt,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||te,fetchers:new Map,blockers:new Map},z=V.Pop,G=!1,k,q=!1,re=new Map,ae=null,Me=!1,ye=!1,et=[],tt=new Set,$=new Map,rt=0,Ne=-1,Ce=new Map,se=new Set,Le=new Map,Be=new Map,ie=new Set,be=new Map,we=new Map,nt;function Dr(){if(y=e.history.listen(i=>{let{action:l,location:u,delta:p}=i;if(nt){nt(),nt=void 0;return}Fe(we.size===0||p!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let g=Wt({currentLocation:f.location,nextLocation:u,historyAction:l});if(g&&p!=null){let D=new Promise(L=>{nt=L});e.history.go(p*-1),it(g,{state:"blocked",location:u,proceed(){it(g,{state:"proceeding",proceed:void 0,reset:void 0,location:u}),D.then(()=>e.history.go(p))},reset(){let L=new Map(f.blockers);L.set(g,Ke),X({blockers:L})}});return}return Ee(l,u)}),r){An(t,re);let i=()=>Nn(t,re);t.addEventListener("pagehide",i),ae=()=>t.removeEventListener("pagehide",i)}return f.initialized||Ee(V.Pop,f.location,{initialHydration:!0}),Ae}function Sr(){y&&y(),ae&&ae(),b.clear(),k&&k.abort(),f.fetchers.forEach((i,l)=>at(l)),f.blockers.forEach((i,l)=>Ht(l))}function Mr(i){return b.add(i),()=>b.delete(i)}function X(i,l){l===void 0&&(l={}),f=B({},f,i);let u=[],p=[];h.v7_fetcherPersist&&f.fetchers.forEach((g,D)=>{g.state==="idle"&&(ie.has(D)?p.push(D):u.push(D))}),ie.forEach(g=>{!f.fetchers.has(g)&&!$.has(g)&&p.push(g)}),[...b].forEach(g=>g(f,{deletedFetchers:p,viewTransitionOpts:l.viewTransitionOpts,flushSync:l.flushSync===!0})),h.v7_fetcherPersist?(u.forEach(g=>f.fetchers.delete(g)),p.forEach(g=>at(g))):p.forEach(g=>ie.delete(g))}function je(i,l,u){var p,g;let{flushSync:D}=u===void 0?{}:u,L=f.actionData!=null&&f.navigation.formMethod!=null&&oe(f.navigation.formMethod)&&f.navigation.state==="loading"&&((p=i.state)==null?void 0:p._isRedirect)!==!0,R;l.actionData?Object.keys(l.actionData).length>0?R=l.actionData:R=null:L?R=f.actionData:R=null;let P=l.loaderData?ir(f.loaderData,l.loaderData,l.matches||[],l.errors):f.loaderData,w=f.blockers;w.size>0&&(w=new Map(w),w.forEach((_,Y)=>w.set(Y,Ke)));let x=G===!0||f.navigation.formMethod!=null&&oe(f.navigation.formMethod)&&((g=i.state)==null?void 0:g._isRedirect)!==!0;c&&(o=c,c=void 0),Me||z===V.Pop||(z===V.Push?e.history.push(i,i.state):z===V.Replace&&e.history.replace(i,i.state));let j;if(z===V.Pop){let _=re.get(f.location.pathname);_&&_.has(i.pathname)?j={currentLocation:f.location,nextLocation:i}:re.has(i.pathname)&&(j={currentLocation:i,nextLocation:f.location})}else if(q){let _=re.get(f.location.pathname);_?_.add(i.pathname):(_=new Set([i.pathname]),re.set(f.location.pathname,_)),j={currentLocation:f.location,nextLocation:i}}X(B({},l,{actionData:R,loaderData:P,historyAction:z,location:i,initialized:!0,navigation:wt,revalidation:"idle",restoreScrollPosition:Vt(i,l.matches||f.matches),preventScrollReset:x,blockers:w}),{viewTransitionOpts:j,flushSync:D===!0}),z=V.Pop,G=!1,q=!1,Me=!1,ye=!1,et=[]}async function Ft(i,l){if(typeof i=="number"){e.history.go(i);return}let u=xt(f.location,f.matches,d,h.v7_prependBasename,i,h.v7_relativeSplatPath,l==null?void 0:l.fromRouteId,l==null?void 0:l.relative),{path:p,submission:g,error:D}=Qt(h.v7_normalizeFormMethod,!1,u,l),L=f.location,R=Je(f.location,p,l&&l.state);R=B({},R,e.history.encodeLocation(R));let P=l&&l.replace!=null?l.replace:void 0,w=V.Push;P===!0?w=V.Replace:P===!1||g!=null&&oe(g.formMethod)&&g.formAction===f.location.pathname+f.location.search&&(w=V.Replace);let x=l&&"preventScrollReset"in l?l.preventScrollReset===!0:void 0,j=(l&&l.flushSync)===!0,_=Wt({currentLocation:L,nextLocation:R,historyAction:w});if(_){it(_,{state:"blocked",location:R,proceed(){it(_,{state:"proceeding",proceed:void 0,reset:void 0,location:R}),Ft(i,l)},reset(){let Y=new Map(f.blockers);Y.set(_,Ke),X({blockers:Y})}});return}return await Ee(w,R,{submission:g,pendingError:D,preventScrollReset:x,replace:l&&l.replace,enableViewTransition:l&&l.viewTransition,flushSync:j})}function Cr(){if(pt(),X({revalidation:"loading"}),f.navigation.state!=="submitting"){if(f.navigation.state==="idle"){Ee(f.historyAction,f.location,{startUninterruptedRevalidation:!0});return}Ee(z||f.historyAction,f.navigation.location,{overrideNavigation:f.navigation,enableViewTransition:q===!0})}}async function Ee(i,l,u){k&&k.abort(),k=null,z=i,Me=(u&&u.startUninterruptedRevalidation)===!0,Nr(f.location,f.matches),G=(u&&u.preventScrollReset)===!0,q=(u&&u.enableViewTransition)===!0;let p=c||o,g=u&&u.overrideNavigation,D=u!=null&&u.initialHydration&&f.matches&&f.matches.length>0&&!J?f.matches:xe(p,l,d),L=(u&&u.flushSync)===!0;if(D&&f.initialized&&!ye&&Ln(f.location,l)&&!(u&&u.submission&&oe(u.submission.formMethod))){je(l,{matches:D},{flushSync:L});return}let R=ot(D,p,l.pathname);if(R.active&&R.matches&&(D=R.matches),!D){let{error:N,notFoundMatches:F,route:H}=vt(l.pathname);je(l,{matches:F,loaderData:{},errors:{[H.id]:N}},{flushSync:L});return}k=new AbortController;let P=Ue(e.history,l,k.signal,u&&u.submission),w;if(u&&u.pendingError)w=[De(D).route.id,{type:I.error,error:u.pendingError}];else if(u&&u.submission&&oe(u.submission.formMethod)){let N=await Lr(P,l,u.submission,D,R.active,{replace:u.replace,flushSync:L});if(N.shortCircuited)return;if(N.pendingActionResult){let[F,H]=N.pendingActionResult;if(ee(H)&&Ye(H.error)&&H.error.status===404){k=null,je(l,{matches:N.matches,loaderData:{},errors:{[F]:H.error}});return}}D=N.matches||D,w=N.pendingActionResult,g=Et(l,u.submission),L=!1,R.active=!1,P=Ue(e.history,P.url,P.signal)}let{shortCircuited:x,matches:j,loaderData:_,errors:Y}=await jr(P,l,D,R.active,g,u&&u.submission,u&&u.fetcherSubmission,u&&u.replace,u&&u.initialHydration===!0,L,w);x||(k=null,je(l,B({matches:j||D},or(w),{loaderData:_,errors:Y})))}async function Lr(i,l,u,p,g,D){D===void 0&&(D={}),pt();let L=Fn(l,u);if(X({navigation:L},{flushSync:D.flushSync===!0}),g){let w=await lt(p,l.pathname,i.signal);if(w.type==="aborted")return{shortCircuited:!0};if(w.type==="error"){let x=De(w.partialMatches).route.id;return{matches:w.partialMatches,pendingActionResult:[x,{type:I.error,error:w.error}]}}else if(w.matches)p=w.matches;else{let{notFoundMatches:x,error:j,route:_}=vt(l.pathname);return{matches:x,pendingActionResult:[_.id,{type:I.error,error:j}]}}}let R,P=$e(p,l);if(!P.route.action&&!P.route.lazy)R={type:I.error,error:Z(405,{method:i.method,pathname:l.pathname,routeId:P.route.id})};else if(R=(await ze("action",f,i,[P],p,null))[P.route.id],i.signal.aborted)return{shortCircuited:!0};if(Se(R)){let w;return D&&D.replace!=null?w=D.replace:w=rr(R.response.headers.get("Location"),new URL(i.url),d)===f.location.pathname+f.location.search,await Re(i,R,!0,{submission:u,replace:w}),{shortCircuited:!0}}if(pe(R))throw Z(400,{type:"defer-action"});if(ee(R)){let w=De(p,P.route.id);return(D&&D.replace)!==!0&&(z=V.Push),{matches:p,pendingActionResult:[w.route.id,R]}}return{matches:p,pendingActionResult:[P.route.id,R]}}async function jr(i,l,u,p,g,D,L,R,P,w,x){let j=g||Et(l,D),_=D||L||dr(j),Y=!Me&&(!h.v7_partialHydration||!P);if(p){if(Y){let W=It(x);X(B({navigation:j},W!==void 0?{actionData:W}:{}),{flushSync:w})}let U=await lt(u,l.pathname,i.signal);if(U.type==="aborted")return{shortCircuited:!0};if(U.type==="error"){let W=De(U.partialMatches).route.id;return{matches:U.partialMatches,loaderData:{},errors:{[W]:U.error}}}else if(U.matches)u=U.matches;else{let{error:W,notFoundMatches:Oe,route:We}=vt(l.pathname);return{matches:Oe,loaderData:{},errors:{[We.id]:W}}}}let N=c||o,[F,H]=qt(e.history,f,u,_,l,h.v7_partialHydration&&P===!0,h.v7_skipActionErrorRevalidation,ye,et,tt,ie,Le,se,N,d,x);if(gt(U=>!(u&&u.some(W=>W.route.id===U))||F&&F.some(W=>W.route.id===U)),Ne=++rt,F.length===0&&H.length===0){let U=zt();return je(l,B({matches:u,loaderData:{},errors:x&&ee(x[1])?{[x[0]]:x[1].error}:null},or(x),U?{fetchers:new Map(f.fetchers)}:{}),{flushSync:w}),{shortCircuited:!0}}if(Y){let U={};if(!p){U.navigation=j;let W=It(x);W!==void 0&&(U.actionData=W)}H.length>0&&(U.fetchers=Tr(H)),X(U,{flushSync:w})}H.forEach(U=>{he(U.key),U.controller&&$.set(U.key,U.controller)});let Te=()=>H.forEach(U=>he(U.key));k&&k.signal.addEventListener("abort",Te);let{loaderResults:ke,fetcherResults:ue}=await At(f,u,F,H,i);if(i.signal.aborted)return{shortCircuited:!0};k&&k.signal.removeEventListener("abort",Te),H.forEach(U=>$.delete(U.key));let le=ut(ke);if(le)return await Re(i,le.result,!0,{replace:R}),{shortCircuited:!0};if(le=ut(ue),le)return se.add(le.key),await Re(i,le.result,!0,{replace:R}),{shortCircuited:!0};let{loaderData:yt,errors:He}=ar(f,u,ke,x,H,ue,be);be.forEach((U,W)=>{U.subscribe(Oe=>{(Oe||U.done)&&be.delete(W)})}),h.v7_partialHydration&&P&&f.errors&&(He=B({},f.errors,He));let Pe=zt(),st=kt(Ne),dt=Pe||st||H.length>0;return B({matches:u,loaderData:yt,errors:He},dt?{fetchers:new Map(f.fetchers)}:{})}function It(i){if(i&&!ee(i[1]))return{[i[0]]:i[1].data};if(f.actionData)return Object.keys(f.actionData).length===0?null:f.actionData}function Tr(i){return i.forEach(l=>{let u=f.fetchers.get(l.key),p=Ve(void 0,u?u.data:void 0);f.fetchers.set(l.key,p)}),new Map(f.fetchers)}function Or(i,l,u,p){if(n)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");he(i);let g=(p&&p.flushSync)===!0,D=c||o,L=xt(f.location,f.matches,d,h.v7_prependBasename,u,h.v7_relativeSplatPath,l,p==null?void 0:p.relative),R=xe(D,L,d),P=ot(R,D,L);if(P.active&&P.matches&&(R=P.matches),!R){de(i,l,Z(404,{pathname:L}),{flushSync:g});return}let{path:w,submission:x,error:j}=Qt(h.v7_normalizeFormMethod,!0,L,p);if(j){de(i,l,j,{flushSync:g});return}let _=$e(R,w),Y=(p&&p.preventScrollReset)===!0;if(x&&oe(x.formMethod)){_r(i,l,w,_,R,P.active,g,Y,x);return}Le.set(i,{routeId:l,path:w}),Ur(i,l,w,_,R,P.active,g,Y,x)}async function _r(i,l,u,p,g,D,L,R,P){pt(),Le.delete(i);function w(K){if(!K.route.action&&!K.route.lazy){let _e=Z(405,{method:P.formMethod,pathname:u,routeId:l});return de(i,l,_e,{flushSync:L}),!0}return!1}if(!D&&w(p))return;let x=f.fetchers.get(i);fe(i,In(P,x),{flushSync:L});let j=new AbortController,_=Ue(e.history,u,j.signal,P);if(D){let K=await lt(g,new URL(_.url).pathname,_.signal,i);if(K.type==="aborted")return;if(K.type==="error"){de(i,l,K.error,{flushSync:L});return}else if(K.matches){if(g=K.matches,p=$e(g,u),w(p))return}else{de(i,l,Z(404,{pathname:u}),{flushSync:L});return}}$.set(i,j);let Y=rt,F=(await ze("action",f,_,[p],g,i))[p.route.id];if(_.signal.aborted){$.get(i)===j&&$.delete(i);return}if(h.v7_fetcherPersist&&ie.has(i)){if(Se(F)||ee(F)){fe(i,me(void 0));return}}else{if(Se(F))if($.delete(i),Ne>Y){fe(i,me(void 0));return}else return se.add(i),fe(i,Ve(P)),Re(_,F,!1,{fetcherSubmission:P,preventScrollReset:R});if(ee(F)){de(i,l,F.error);return}}if(pe(F))throw Z(400,{type:"defer-action"});let H=f.navigation.location||f.location,Te=Ue(e.history,H,j.signal),ke=c||o,ue=f.navigation.state!=="idle"?xe(ke,f.navigation.location,d):f.matches;O(ue,"Didn't find any matches after fetcher action");let le=++rt;Ce.set(i,le);let yt=Ve(P,F.data);f.fetchers.set(i,yt);let[He,Pe]=qt(e.history,f,ue,P,H,!1,h.v7_skipActionErrorRevalidation,ye,et,tt,ie,Le,se,ke,d,[p.route.id,F]);Pe.filter(K=>K.key!==i).forEach(K=>{let _e=K.key,$t=f.fetchers.get(_e),kr=Ve(void 0,$t?$t.data:void 0);f.fetchers.set(_e,kr),he(_e),K.controller&&$.set(_e,K.controller)}),X({fetchers:new Map(f.fetchers)});let st=()=>Pe.forEach(K=>he(K.key));j.signal.addEventListener("abort",st);let{loaderResults:dt,fetcherResults:U}=await At(f,ue,He,Pe,Te);if(j.signal.aborted)return;j.signal.removeEventListener("abort",st),Ce.delete(i),$.delete(i),Pe.forEach(K=>$.delete(K.key));let W=ut(dt);if(W)return Re(Te,W.result,!1,{preventScrollReset:R});if(W=ut(U),W)return se.add(W.key),Re(Te,W.result,!1,{preventScrollReset:R});let{loaderData:Oe,errors:We}=ar(f,ue,dt,void 0,Pe,U,be);if(f.fetchers.has(i)){let K=me(F.data);f.fetchers.set(i,K)}kt(le),f.navigation.state==="loading"&&le>Ne?(O(z,"Expected pending action"),k&&k.abort(),je(f.navigation.location,{matches:ue,loaderData:Oe,errors:We,fetchers:new Map(f.fetchers)})):(X({errors:We,loaderData:ir(f.loaderData,Oe,ue,We),fetchers:new Map(f.fetchers)}),ye=!1)}async function Ur(i,l,u,p,g,D,L,R,P){let w=f.fetchers.get(i);fe(i,Ve(P,w?w.data:void 0),{flushSync:L});let x=new AbortController,j=Ue(e.history,u,x.signal);if(D){let F=await lt(g,new URL(j.url).pathname,j.signal,i);if(F.type==="aborted")return;if(F.type==="error"){de(i,l,F.error,{flushSync:L});return}else if(F.matches)g=F.matches,p=$e(g,u);else{de(i,l,Z(404,{pathname:u}),{flushSync:L});return}}$.set(i,x);let _=rt,N=(await ze("loader",f,j,[p],g,i))[p.route.id];if(pe(N)&&(N=await jt(N,j.signal,!0)||N),$.get(i)===x&&$.delete(i),!j.signal.aborted){if(ie.has(i)){fe(i,me(void 0));return}if(Se(N))if(Ne>_){fe(i,me(void 0));return}else{se.add(i),await Re(j,N,!1,{preventScrollReset:R});return}if(ee(N)){de(i,l,N.error);return}O(!pe(N),"Unhandled fetcher deferred data"),fe(i,me(N.data))}}async function Re(i,l,u,p){let{submission:g,fetcherSubmission:D,preventScrollReset:L,replace:R}=p===void 0?{}:p;l.response.headers.has("X-Remix-Revalidate")&&(ye=!0);let P=l.response.headers.get("Location");O(P,"Expected a Location header on the redirect Response"),P=rr(P,new URL(i.url),d);let w=Je(f.location,P,{_isRedirect:!0});if(r){let F=!1;if(l.response.headers.has("X-Remix-Reload-Document"))F=!0;else if(Lt.test(P)){const H=e.history.createURL(P);F=H.origin!==t.location.origin||Xe(H.pathname,d)==null}if(F){R?t.location.replace(P):t.location.assign(P);return}}k=null;let x=R===!0||l.response.headers.has("X-Remix-Replace")?V.Replace:V.Push,{formMethod:j,formAction:_,formEncType:Y}=f.navigation;!g&&!D&&j&&_&&Y&&(g=dr(f.navigation));let N=g||D;if(gn.has(l.response.status)&&N&&oe(N.formMethod))await Ee(x,w,{submission:B({},N,{formAction:P}),preventScrollReset:L||G,enableViewTransition:u?q:void 0});else{let F=Et(w,g);await Ee(x,w,{overrideNavigation:F,fetcherSubmission:D,preventScrollReset:L||G,enableViewTransition:u?q:void 0})}}async function ze(i,l,u,p,g,D){let L,R={};try{L=await xn(m,i,l,u,p,g,D,s,a)}catch(P){return p.forEach(w=>{R[w.route.id]={type:I.error,error:P}}),R}for(let[P,w]of Object.entries(L))if(jn(w)){let x=w.result;R[P]={type:I.redirect,response:Mn(x,u,P,g,d,h.v7_relativeSplatPath)}}else R[P]=await Sn(w);return R}async function At(i,l,u,p,g){let D=i.matches,L=ze("loader",i,g,u,l,null),R=Promise.all(p.map(async x=>{if(x.matches&&x.match&&x.controller){let _=(await ze("loader",i,Ue(e.history,x.path,x.controller.signal),[x.match],x.matches,x.key))[x.match.route.id];return{[x.key]:_}}else return Promise.resolve({[x.key]:{type:I.error,error:Z(404,{pathname:x.path})}})})),P=await L,w=(await R).reduce((x,j)=>Object.assign(x,j),{});return await Promise.all([_n(l,P,g.signal,D,i.loaderData),Un(l,w,p)]),{loaderResults:P,fetcherResults:w}}function pt(){ye=!0,et.push(...gt()),Le.forEach((i,l)=>{$.has(l)&&tt.add(l),he(l)})}function fe(i,l,u){u===void 0&&(u={}),f.fetchers.set(i,l),X({fetchers:new Map(f.fetchers)},{flushSync:(u&&u.flushSync)===!0})}function de(i,l,u,p){p===void 0&&(p={});let g=De(f.matches,l);at(i),X({errors:{[g.route.id]:u},fetchers:new Map(f.fetchers)},{flushSync:(p&&p.flushSync)===!0})}function Nt(i){return Be.set(i,(Be.get(i)||0)+1),ie.has(i)&&ie.delete(i),f.fetchers.get(i)||yn}function at(i){let l=f.fetchers.get(i);$.has(i)&&!(l&&l.state==="loading"&&Ce.has(i))&&he(i),Le.delete(i),Ce.delete(i),se.delete(i),h.v7_fetcherPersist&&ie.delete(i),tt.delete(i),f.fetchers.delete(i)}function Fr(i){let l=(Be.get(i)||0)-1;l<=0?(Be.delete(i),ie.add(i),h.v7_fetcherPersist||at(i)):Be.set(i,l),X({fetchers:new Map(f.fetchers)})}function he(i){let l=$.get(i);l&&(l.abort(),$.delete(i))}function Bt(i){for(let l of i){let u=Nt(l),p=me(u.data);f.fetchers.set(l,p)}}function zt(){let i=[],l=!1;for(let u of se){let p=f.fetchers.get(u);O(p,"Expected fetcher: "+u),p.state==="loading"&&(se.delete(u),i.push(u),l=!0)}return Bt(i),l}function kt(i){let l=[];for(let[u,p]of Ce)if(p<i){let g=f.fetchers.get(u);O(g,"Expected fetcher: "+u),g.state==="loading"&&(he(u),Ce.delete(u),l.push(u))}return Bt(l),l.length>0}function Ir(i,l){let u=f.blockers.get(i)||Ke;return we.get(i)!==l&&we.set(i,l),u}function Ht(i){f.blockers.delete(i),we.delete(i)}function it(i,l){let u=f.blockers.get(i)||Ke;O(u.state==="unblocked"&&l.state==="blocked"||u.state==="blocked"&&l.state==="blocked"||u.state==="blocked"&&l.state==="proceeding"||u.state==="blocked"&&l.state==="unblocked"||u.state==="proceeding"&&l.state==="unblocked","Invalid blocker state transition: "+u.state+" -> "+l.state);let p=new Map(f.blockers);p.set(i,l),X({blockers:p})}function Wt(i){let{currentLocation:l,nextLocation:u,historyAction:p}=i;if(we.size===0)return;we.size>1&&Fe(!1,"A router only supports one blocker at a time");let g=Array.from(we.entries()),[D,L]=g[g.length-1],R=f.blockers.get(D);if(!(R&&R.state==="proceeding")&&L({currentLocation:l,nextLocation:u,historyAction:p}))return D}function vt(i){let l=Z(404,{pathname:i}),u=c||o,{matches:p,route:g}=lr(u);return gt(),{notFoundMatches:p,route:g,error:l}}function gt(i){let l=[];return be.forEach((u,p)=>{(!i||i(p))&&(u.cancel(),l.push(p),be.delete(p))}),l}function Ar(i,l,u){if(M=i,S=l,C=u||null,!A&&f.navigation===wt){A=!0;let p=Vt(f.location,f.matches);p!=null&&X({restoreScrollPosition:p})}return()=>{M=null,S=null,C=null}}function Kt(i,l){return C&&C(i,l.map(p=>cr(p,f.loaderData)))||i.key}function Nr(i,l){if(M&&S){let u=Kt(i,l);M[u]=S()}}function Vt(i,l){if(M){let u=Kt(i,l),p=M[u];if(typeof p=="number")return p}return null}function ot(i,l,u){if(v)if(i){if(Object.keys(i[0].params).length>0)return{active:!0,matches:ct(l,u,d,!0)}}else return{active:!0,matches:ct(l,u,d,!0)||[]};return{active:!1,matches:null}}async function lt(i,l,u,p){if(!v)return{type:"success",matches:i};let g=i;for(;;){let D=c==null,L=c||o,R=s;try{await v({signal:u,path:l,matches:g,fetcherKey:p,patch:(x,j)=>{u.aborted||tr(x,j,L,R,a)}})}catch(x){return{type:"error",error:x,partialMatches:g}}finally{D&&!u.aborted&&(o=[...o])}if(u.aborted)return{type:"aborted"};let P=xe(L,l,d);if(P)return{type:"success",matches:P};let w=ct(L,l,d,!0);if(!w||g.length===w.length&&g.every((x,j)=>x.route.id===w[j].route.id))return{type:"success",matches:null};g=w}}function Br(i){s={},c=ft(i,a,void 0,s)}function zr(i,l){let u=c==null;tr(i,l,c||o,s,a),u&&(o=[...o],X({}))}return Ae={get basename(){return d},get future(){return h},get state(){return f},get routes(){return o},get window(){return t},initialize:Dr,subscribe:Mr,enableScrollRestoration:Ar,navigate:Ft,fetch:Or,revalidate:Cr,createHref:i=>e.history.createHref(i),encodeLocation:i=>e.history.encodeLocation(i),getFetcher:Nt,deleteFetcher:Fr,dispose:Sr,getBlocker:Ir,deleteBlocker:Ht,patchRoutes:zr,_internalFetchControllers:$,_internalActiveDeferreds:be,_internalSetRoutes:Br},Ae}function wn(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function xt(e,t,r,n,a,s,o,c){let d,m;if(o){d=[];for(let h of t)if(d.push(h),h.route.id===o){m=h;break}}else d=t,m=t[t.length-1];let v=Ct(a||".",Mt(d,s),Xe(e.pathname,r)||e.pathname,c==="path");if(a==null&&(v.search=e.search,v.hash=e.hash),(a==null||a===""||a===".")&&m){let h=Tt(v.search);if(m.route.index&&!h)v.search=v.search?v.search.replace(/^\?/,"?index&"):"?index";else if(!m.route.index&&h){let y=new URLSearchParams(v.search),b=y.getAll("index");y.delete("index"),b.filter(C=>C).forEach(C=>y.append("index",C));let M=y.toString();v.search=M?"?"+M:""}}return n&&r!=="/"&&(v.pathname=v.pathname==="/"?r:ce([r,v.pathname])),Ge(v)}function Qt(e,t,r,n){if(!n||!wn(n))return{path:r};if(n.formMethod&&!On(n.formMethod))return{path:r,error:Z(405,{method:n.formMethod})};let a=()=>({path:r,error:Z(400,{type:"invalid-body"})}),s=n.formMethod||"get",o=e?s.toUpperCase():s.toLowerCase(),c=br(r);if(n.body!==void 0){if(n.formEncType==="text/plain"){if(!oe(o))return a();let y=typeof n.body=="string"?n.body:n.body instanceof FormData||n.body instanceof URLSearchParams?Array.from(n.body.entries()).reduce((b,M)=>{let[C,S]=M;return""+b+C+"="+S+`
`},""):String(n.body);return{path:r,submission:{formMethod:o,formAction:c,formEncType:n.formEncType,formData:void 0,json:void 0,text:y}}}else if(n.formEncType==="application/json"){if(!oe(o))return a();try{let y=typeof n.body=="string"?JSON.parse(n.body):n.body;return{path:r,submission:{formMethod:o,formAction:c,formEncType:n.formEncType,formData:void 0,json:y,text:void 0}}}catch{return a()}}}O(typeof FormData=="function","FormData is not available in this environment");let d,m;if(n.formData)d=St(n.formData),m=n.formData;else if(n.body instanceof FormData)d=St(n.body),m=n.body;else if(n.body instanceof URLSearchParams)d=n.body,m=nr(d);else if(n.body==null)d=new URLSearchParams,m=new FormData;else try{d=new URLSearchParams(n.body),m=nr(d)}catch{return a()}let v={formMethod:o,formAction:c,formEncType:n&&n.formEncType||"application/x-www-form-urlencoded",formData:m,json:void 0,text:void 0};if(oe(v.formMethod))return{path:r,submission:v};let h=ve(r);return t&&h.search&&Tt(h.search)&&d.append("index",""),h.search="?"+d,{path:Ge(h),submission:v}}function Zt(e,t,r){r===void 0&&(r=!1);let n=e.findIndex(a=>a.route.id===t);return n>=0?e.slice(0,r?n+1:n):e}function qt(e,t,r,n,a,s,o,c,d,m,v,h,y,b,M,C){let S=C?ee(C[1])?C[1].error:C[1].data:void 0,A=e.createURL(t.location),T=e.createURL(a),J=r;s&&t.errors?J=Zt(r,Object.keys(t.errors)[0],!0):C&&ee(C[1])&&(J=Zt(r,C[0]));let te=C?C[1].statusCode:void 0,Q=o&&te&&te>=400,Ae=J.filter((z,G)=>{let{route:k}=z;if(k.lazy)return!0;if(k.loader==null)return!1;if(s)return Dt(k,t.loaderData,t.errors);if(En(t.loaderData,t.matches[G],z)||d.some(ae=>ae===z.route.id))return!0;let q=t.matches[G],re=z;return er(z,B({currentUrl:A,currentParams:q.params,nextUrl:T,nextParams:re.params},n,{actionResult:S,actionStatus:te,defaultShouldRevalidate:Q?!1:c||A.pathname+A.search===T.pathname+T.search||A.search!==T.search||gr(q,re)}))}),f=[];return h.forEach((z,G)=>{if(s||!r.some(Me=>Me.route.id===z.routeId)||v.has(G))return;let k=xe(b,z.path,M);if(!k){f.push({key:G,routeId:z.routeId,path:z.path,matches:null,match:null,controller:null});return}let q=t.fetchers.get(G),re=$e(k,z.path),ae=!1;y.has(G)?ae=!1:m.has(G)?(m.delete(G),ae=!0):q&&q.state!=="idle"&&q.data===void 0?ae=c:ae=er(re,B({currentUrl:A,currentParams:t.matches[t.matches.length-1].params,nextUrl:T,nextParams:r[r.length-1].params},n,{actionResult:S,actionStatus:te,defaultShouldRevalidate:Q?!1:c})),ae&&f.push({key:G,routeId:z.routeId,path:z.path,matches:k,match:re,controller:new AbortController})}),[Ae,f]}function Dt(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let n=t!=null&&t[e.id]!==void 0,a=r!=null&&r[e.id]!==void 0;return!n&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!n&&!a}function En(e,t,r){let n=!t||r.route.id!==t.route.id,a=e[r.route.id]===void 0;return n||a}function gr(e,t){let r=e.route.path;return e.pathname!==t.pathname||r!=null&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function er(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if(typeof r=="boolean")return r}return t.defaultShouldRevalidate}function tr(e,t,r,n,a){var s;let o;if(e){let m=n[e];O(m,"No route found to patch children into: routeId = "+e),m.children||(m.children=[]),o=m.children}else o=r;let c=t.filter(m=>!o.some(v=>yr(m,v))),d=ft(c,a,[e||"_","patch",String(((s=o)==null?void 0:s.length)||"0")],n);o.push(...d)}function yr(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((r,n)=>{var a;return(a=t.children)==null?void 0:a.some(s=>yr(r,s))}):!1}async function Rn(e,t,r){if(!e.lazy)return;let n=await e.lazy();if(!e.lazy)return;let a=r[e.id];O(a,"No route found in manifest");let s={};for(let o in n){let d=a[o]!==void 0&&o!=="hasErrorBoundary";Fe(!d,'Route "'+a.id+'" has a static property "'+o+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+o+'" will be ignored.')),!d&&!Kr.has(o)&&(s[o]=n[o])}Object.assign(a,s),Object.assign(a,B({},t(a),{lazy:void 0}))}async function Pn(e){let{matches:t}=e,r=t.filter(a=>a.shouldLoad);return(await Promise.all(r.map(a=>a.resolve()))).reduce((a,s,o)=>Object.assign(a,{[r[o].route.id]:s}),{})}async function xn(e,t,r,n,a,s,o,c,d,m){let v=s.map(b=>b.route.lazy?Rn(b.route,d,c):void 0),h=s.map((b,M)=>{let C=v[M],S=a.some(T=>T.route.id===b.route.id);return B({},b,{shouldLoad:S,resolve:async T=>(T&&n.method==="GET"&&(b.route.lazy||b.route.loader)&&(S=!0),S?Dn(t,n,b,C,T,m):Promise.resolve({type:I.data,result:void 0}))})}),y=await e({matches:h,request:n,params:s[0].params,fetcherKey:o,context:m});try{await Promise.all(v)}catch{}return y}async function Dn(e,t,r,n,a,s){let o,c,d=m=>{let v,h=new Promise((M,C)=>v=C);c=()=>v(),t.signal.addEventListener("abort",c);let y=M=>typeof m!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+r.route.id+"]"))):m({request:t,params:r.params,context:s},...M!==void 0?[M]:[]),b=(async()=>{try{return{type:"data",result:await(a?a(C=>y(C)):y())}}catch(M){return{type:"error",result:M}}})();return Promise.race([b,h])};try{let m=r.route[e];if(n)if(m){let v,[h]=await Promise.all([d(m).catch(y=>{v=y}),n]);if(v!==void 0)throw v;o=h}else if(await n,m=r.route[e],m)o=await d(m);else if(e==="action"){let v=new URL(t.url),h=v.pathname+v.search;throw Z(405,{method:t.method,pathname:h,routeId:r.route.id})}else return{type:I.data,result:void 0};else if(m)o=await d(m);else{let v=new URL(t.url),h=v.pathname+v.search;throw Z(404,{pathname:h})}O(o.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+r.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(m){return{type:I.error,result:m}}finally{c&&t.signal.removeEventListener("abort",c)}return o}async function Sn(e){let{result:t,type:r}=e;if(wr(t)){let h;try{let y=t.headers.get("Content-Type");y&&/\bapplication\/json\b/.test(y)?t.body==null?h=null:h=await t.json():h=await t.text()}catch(y){return{type:I.error,error:y}}return r===I.error?{type:I.error,error:new Pt(t.status,t.statusText,h),statusCode:t.status,headers:t.headers}:{type:I.data,data:h,statusCode:t.status,headers:t.headers}}if(r===I.error){if(sr(t)){var n,a;if(t.data instanceof Error){var s,o;return{type:I.error,error:t.data,statusCode:(s=t.init)==null?void 0:s.status,headers:(o=t.init)!=null&&o.headers?new Headers(t.init.headers):void 0}}return{type:I.error,error:new Pt(((n=t.init)==null?void 0:n.status)||500,void 0,t.data),statusCode:Ye(t)?t.status:void 0,headers:(a=t.init)!=null&&a.headers?new Headers(t.init.headers):void 0}}return{type:I.error,error:t,statusCode:Ye(t)?t.status:void 0}}if(Tn(t)){var c,d;return{type:I.deferred,deferredData:t,statusCode:(c=t.init)==null?void 0:c.status,headers:((d=t.init)==null?void 0:d.headers)&&new Headers(t.init.headers)}}if(sr(t)){var m,v;return{type:I.data,data:t.data,statusCode:(m=t.init)==null?void 0:m.status,headers:(v=t.init)!=null&&v.headers?new Headers(t.init.headers):void 0}}return{type:I.data,data:t}}function Mn(e,t,r,n,a,s){let o=e.headers.get("Location");if(O(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!Lt.test(o)){let c=n.slice(0,n.findIndex(d=>d.route.id===r)+1);o=xt(new URL(t.url),c,a,!0,o,s),e.headers.set("Location",o)}return e}function rr(e,t,r){if(Lt.test(e)){let n=e,a=n.startsWith("//")?new URL(t.protocol+n):new URL(n),s=Xe(a.pathname,r)!=null;if(a.origin===t.origin&&s)return a.pathname+a.search+a.hash}return e}function Ue(e,t,r,n){let a=e.createURL(br(t)).toString(),s={signal:r};if(n&&oe(n.formMethod)){let{formMethod:o,formEncType:c}=n;s.method=o.toUpperCase(),c==="application/json"?(s.headers=new Headers({"Content-Type":c}),s.body=JSON.stringify(n.json)):c==="text/plain"?s.body=n.text:c==="application/x-www-form-urlencoded"&&n.formData?s.body=St(n.formData):s.body=n.formData}return new Request(a,s)}function St(e){let t=new URLSearchParams;for(let[r,n]of e.entries())t.append(r,typeof n=="string"?n:n.name);return t}function nr(e){let t=new FormData;for(let[r,n]of e.entries())t.append(r,n);return t}function Cn(e,t,r,n,a){let s={},o=null,c,d=!1,m={},v=r&&ee(r[1])?r[1].error:void 0;return e.forEach(h=>{if(!(h.route.id in t))return;let y=h.route.id,b=t[y];if(O(!Se(b),"Cannot handle redirect results in processLoaderData"),ee(b)){let M=b.error;v!==void 0&&(M=v,v=void 0),o=o||{};{let C=De(e,y);o[C.route.id]==null&&(o[C.route.id]=M)}s[y]=void 0,d||(d=!0,c=Ye(b.error)?b.error.status:500),b.headers&&(m[y]=b.headers)}else pe(b)?(n.set(y,b.deferredData),s[y]=b.deferredData.data,b.statusCode!=null&&b.statusCode!==200&&!d&&(c=b.statusCode),b.headers&&(m[y]=b.headers)):(s[y]=b.data,b.statusCode&&b.statusCode!==200&&!d&&(c=b.statusCode),b.headers&&(m[y]=b.headers))}),v!==void 0&&r&&(o={[r[0]]:v},s[r[0]]=void 0),{loaderData:s,errors:o,statusCode:c||200,loaderHeaders:m}}function ar(e,t,r,n,a,s,o){let{loaderData:c,errors:d}=Cn(t,r,n,o);return a.forEach(m=>{let{key:v,match:h,controller:y}=m,b=s[v];if(O(b,"Did not find corresponding fetcher result"),!(y&&y.signal.aborted))if(ee(b)){let M=De(e.matches,h==null?void 0:h.route.id);d&&d[M.route.id]||(d=B({},d,{[M.route.id]:b.error})),e.fetchers.delete(v)}else if(Se(b))O(!1,"Unhandled fetcher revalidation redirect");else if(pe(b))O(!1,"Unhandled fetcher deferred data");else{let M=me(b.data);e.fetchers.set(v,M)}}),{loaderData:c,errors:d}}function ir(e,t,r,n){let a=B({},t);for(let s of r){let o=s.route.id;if(t.hasOwnProperty(o)?t[o]!==void 0&&(a[o]=t[o]):e[o]!==void 0&&s.route.loader&&(a[o]=e[o]),n&&n.hasOwnProperty(o))break}return a}function or(e){return e?ee(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function De(e,t){return(t?e.slice(0,e.findIndex(n=>n.route.id===t)+1):[...e]).reverse().find(n=>n.route.hasErrorBoundary===!0)||e[0]}function lr(e){let t=e.length===1?e[0]:e.find(r=>r.index||!r.path||r.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Z(e,t){let{pathname:r,routeId:n,method:a,type:s,message:o}=t===void 0?{}:t,c="Unknown Server Error",d="Unknown @remix-run/router error";return e===400?(c="Bad Request",a&&r&&n?d="You made a "+a+' request to "'+r+'" but '+('did not provide a `loader` for route "'+n+'", ')+"so there is no way to handle the request.":s==="defer-action"?d="defer() is not supported in actions":s==="invalid-body"&&(d="Unable to encode submission body")):e===403?(c="Forbidden",d='Route "'+n+'" does not match URL "'+r+'"'):e===404?(c="Not Found",d='No route matches URL "'+r+'"'):e===405&&(c="Method Not Allowed",a&&r&&n?d="You made a "+a.toUpperCase()+' request to "'+r+'" but '+('did not provide an `action` for route "'+n+'", ')+"so there is no way to handle the request.":a&&(d='Invalid request method "'+a.toUpperCase()+'"')),new Pt(e||500,c,new Error(d),!0)}function ut(e){let t=Object.entries(e);for(let r=t.length-1;r>=0;r--){let[n,a]=t[r];if(Se(a))return{key:n,result:a}}}function br(e){let t=typeof e=="string"?ve(e):e;return Ge(B({},t,{hash:""}))}function Ln(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function jn(e){return wr(e.result)&&vn.has(e.result.status)}function pe(e){return e.type===I.deferred}function ee(e){return e.type===I.error}function Se(e){return(e&&e.type)===I.redirect}function sr(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function Tn(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function wr(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function On(e){return pn.has(e.toLowerCase())}function oe(e){return hn.has(e.toLowerCase())}async function _n(e,t,r,n,a){let s=Object.entries(t);for(let o=0;o<s.length;o++){let[c,d]=s[o],m=e.find(y=>(y==null?void 0:y.route.id)===c);if(!m)continue;let v=n.find(y=>y.route.id===m.route.id),h=v!=null&&!gr(v,m)&&(a&&a[m.route.id])!==void 0;pe(d)&&h&&await jt(d,r,!1).then(y=>{y&&(t[c]=y)})}}async function Un(e,t,r){for(let n=0;n<r.length;n++){let{key:a,routeId:s,controller:o}=r[n],c=t[a];e.find(m=>(m==null?void 0:m.route.id)===s)&&pe(c)&&(O(o,"Expected an AbortController for revalidating fetcher deferred result"),await jt(c,o.signal,!0).then(m=>{m&&(t[a]=m)}))}}async function jt(e,t,r){if(r===void 0&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:I.data,data:e.deferredData.unwrappedData}}catch(a){return{type:I.error,error:a}}return{type:I.data,data:e.deferredData.data}}}function Tt(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function $e(e,t){let r=typeof t=="string"?ve(t).search:t.search;if(e[e.length-1].route.index&&Tt(r||""))return e[e.length-1];let n=mr(e);return n[n.length-1]}function dr(e){let{formMethod:t,formAction:r,formEncType:n,text:a,formData:s,json:o}=e;if(!(!t||!r||!n)){if(a!=null)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:void 0,text:a};if(s!=null)return{formMethod:t,formAction:r,formEncType:n,formData:s,json:void 0,text:void 0};if(o!==void 0)return{formMethod:t,formAction:r,formEncType:n,formData:void 0,json:o,text:void 0}}}function Et(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function Fn(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Ve(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function In(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function me(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function An(e,t){try{let r=e.sessionStorage.getItem(vr);if(r){let n=JSON.parse(r);for(let[a,s]of Object.entries(n||{}))s&&Array.isArray(s)&&t.set(a,new Set(s||[]))}}catch{}}function Nn(e,t){if(t.size>0){let r={};for(let[n,a]of t)r[n]=[...a];try{e.sessionStorage.setItem(vr,JSON.stringify(r))}catch(n){Fe(!1,"Failed to save applied view transitions in sessionStorage ("+n+").")}}}/**
 * React Router v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ht(){return ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},ht.apply(this,arguments)}const Ot=E.createContext(null),Bn=E.createContext(null),mt=E.createContext(null),Ie=E.createContext(null),_t=E.createContext(null),ge=E.createContext({outlet:null,matches:[],isDataRoute:!1}),Er=E.createContext(null);function ua(e,t){let{relative:r}=t===void 0?{}:t;Qe()||O(!1);let{basename:n,navigator:a}=E.useContext(Ie),{hash:s,pathname:o,search:c}=Wn(e,{relative:r}),d=o;return n!=="/"&&(d=o==="/"?n:ce([n,o])),a.createHref({pathname:d,search:c,hash:s})}function Qe(){return E.useContext(_t)!=null}function Ut(){return Qe()||O(!1),E.useContext(_t).location}function Rr(e){E.useContext(Ie).static||E.useLayoutEffect(e)}function ca(){let{isDataRoute:e}=E.useContext(ge);return e?qn():zn()}function zn(){Qe()||O(!1);let e=E.useContext(Ot),{basename:t,future:r,navigator:n}=E.useContext(Ie),{matches:a}=E.useContext(ge),{pathname:s}=Ut(),o=JSON.stringify(Mt(a,r.v7_relativeSplatPath)),c=E.useRef(!1);return Rr(()=>{c.current=!0}),E.useCallback(function(m,v){if(v===void 0&&(v={}),!c.current)return;if(typeof m=="number"){n.go(m);return}let h=Ct(m,JSON.parse(o),s,v.relative==="path");e==null&&t!=="/"&&(h.pathname=h.pathname==="/"?t:ce([t,h.pathname])),(v.replace?n.replace:n.push)(h,v.state,v)},[t,n,o,s,e])}const kn=E.createContext(null);function Hn(e){let t=E.useContext(ge).outlet;return t&&E.createElement(kn.Provider,{value:e},t)}function Wn(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=E.useContext(Ie),{matches:a}=E.useContext(ge),{pathname:s}=Ut(),o=JSON.stringify(Mt(a,n.v7_relativeSplatPath));return E.useMemo(()=>Ct(e,JSON.parse(o),s,r==="path"),[e,o,s,r])}function fa(e,t,r,n){Qe()||O(!1);let{navigator:a,static:s}=E.useContext(Ie),{matches:o}=E.useContext(ge),c=o[o.length-1],d=c?c.params:{};c&&c.pathname;let m=c?c.pathnameBase:"/";c&&c.route;let v=Ut(),h;h=v;let y=h.pathname||"/",b=y;if(m!=="/"){let S=m.replace(/^\//,"").split("/");b="/"+y.replace(/^\//,"").split("/").slice(S.length).join("/")}let M=!s&&r&&r.matches&&r.matches.length>0?r.matches:xe(e,{pathname:b});return Yn(M&&M.map(S=>Object.assign({},S,{params:Object.assign({},d,S.params),pathname:ce([m,a.encodeLocation?a.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?m:ce([m,a.encodeLocation?a.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),o,r,n)}function Kn(){let e=Qn(),t=Ye(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return E.createElement(E.Fragment,null,E.createElement("h2",null,"Unexpected Application Error!"),E.createElement("h3",{style:{fontStyle:"italic"}},t),r?E.createElement("pre",{style:a},r):null,null)}const Vn=E.createElement(Kn,null);class $n extends E.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?E.createElement(ge.Provider,{value:this.props.routeContext},E.createElement(Er.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Jn(e){let{routeContext:t,match:r,children:n}=e,a=E.useContext(Ot);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),E.createElement(ge.Provider,{value:t},n)}function Yn(e,t,r,n){var a;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var s;if(!r)return null;if(r.errors)e=r.matches;else if((s=n)!=null&&s.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let o=e,c=(a=r)==null?void 0:a.errors;if(c!=null){let v=o.findIndex(h=>h.route.id&&(c==null?void 0:c[h.route.id])!==void 0);v>=0||O(!1),o=o.slice(0,Math.min(o.length,v+1))}let d=!1,m=-1;if(r&&n&&n.v7_partialHydration)for(let v=0;v<o.length;v++){let h=o[v];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(m=v),h.route.id){let{loaderData:y,errors:b}=r,M=h.route.loader&&y[h.route.id]===void 0&&(!b||b[h.route.id]===void 0);if(h.route.lazy||M){d=!0,m>=0?o=o.slice(0,m+1):o=[o[0]];break}}}return o.reduceRight((v,h,y)=>{let b,M=!1,C=null,S=null;r&&(b=c&&h.route.id?c[h.route.id]:void 0,C=h.route.errorElement||Vn,d&&(m<0&&y===0?(ea("route-fallback"),M=!0,S=null):m===y&&(M=!0,S=h.route.hydrateFallbackElement||null)));let A=t.concat(o.slice(0,y+1)),T=()=>{let J;return b?J=C:M?J=S:h.route.Component?J=E.createElement(h.route.Component,null):h.route.element?J=h.route.element:J=v,E.createElement(Jn,{match:h,routeContext:{outlet:v,matches:A,isDataRoute:r!=null},children:J})};return r&&(h.route.ErrorBoundary||h.route.errorElement||y===0)?E.createElement($n,{location:r.location,revalidation:r.revalidation,component:C,error:b,children:T(),routeContext:{outlet:null,matches:A,isDataRoute:!0}}):T()},null)}var Pr=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Pr||{}),xr=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(xr||{});function Gn(e){let t=E.useContext(Ot);return t||O(!1),t}function Ze(e){let t=E.useContext(Bn);return t||O(!1),t}function Xn(e){let t=E.useContext(ge);return t||O(!1),t}function qe(e){let t=Xn(),r=t.matches[t.matches.length-1];return r.route.id||O(!1),r.route.id}function ha(){return qe()}function ma(){return Ze().navigation}function pa(){let{matches:e,loaderData:t}=Ze();return E.useMemo(()=>e.map(r=>cr(r,t)),[e,t])}function va(){let e=Ze(),t=qe();if(e.errors&&e.errors[t]!=null){console.error("You cannot `useLoaderData` in an errorElement (routeId: "+t+")");return}return e.loaderData[t]}function ga(){let e=Ze(),t=qe();return e.actionData?e.actionData[t]:void 0}function Qn(){var e;let t=E.useContext(Er),r=Ze(xr.UseRouteError),n=qe();return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function Zn(){let e=E.useContext(mt);return e==null?void 0:e._data}function ya(){let e=E.useContext(mt);return e==null?void 0:e._error}function qn(){let{router:e}=Gn(Pr.UseNavigateStable),t=qe(),r=E.useRef(!1);return Rr(()=>{r.current=!0}),E.useCallback(function(a,s){s===void 0&&(s={}),r.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,ht({fromRouteId:t},s)))},[e,t])}const ur={};function ea(e,t,r){ur[e]||(ur[e]=!0)}function ba(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function wa(e){return Hn(e.context)}function Ea(e){let{basename:t="/",children:r=null,location:n,navigationType:a=V.Pop,navigator:s,static:o=!1,future:c}=e;Qe()&&O(!1);let d=t.replace(/^\/*/,"/"),m=E.useMemo(()=>({basename:d,navigator:s,static:o,future:ht({v7_relativeSplatPath:!1},c)}),[d,c,s,o]);typeof n=="string"&&(n=ve(n));let{pathname:v="/",search:h="",hash:y="",state:b=null,key:M="default"}=n,C=E.useMemo(()=>{let S=Xe(v,d);return S==null?null:{location:{pathname:S,search:h,hash:y,state:b,key:M},navigationType:a}},[d,v,h,y,b,M,a]);return C==null?null:E.createElement(Ie.Provider,{value:m},E.createElement(_t.Provider,{children:r,value:C}))}function Ra(e){let{children:t,errorElement:r,resolve:n}=e;return E.createElement(ra,{resolve:n,errorElement:r},E.createElement(na,null,t))}var ne=function(e){return e[e.pending=0]="pending",e[e.success=1]="success",e[e.error=2]="error",e}(ne||{});const ta=new Promise(()=>{});class ra extends E.Component{constructor(t){super(t),this.state={error:null}}static getDerivedStateFromError(t){return{error:t}}componentDidCatch(t,r){console.error("<Await> caught the following error during render",t,r)}render(){let{children:t,errorElement:r,resolve:n}=this.props,a=null,s=ne.pending;if(!(n instanceof Promise))s=ne.success,a=Promise.resolve(),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_data",{get:()=>n});else if(this.state.error){s=ne.error;let o=this.state.error;a=Promise.reject().catch(()=>{}),Object.defineProperty(a,"_tracked",{get:()=>!0}),Object.defineProperty(a,"_error",{get:()=>o})}else n._tracked?(a=n,s="_error"in a?ne.error:"_data"in a?ne.success:ne.pending):(s=ne.pending,Object.defineProperty(n,"_tracked",{get:()=>!0}),a=n.then(o=>Object.defineProperty(n,"_data",{get:()=>o}),o=>Object.defineProperty(n,"_error",{get:()=>o})));if(s===ne.error&&a._error instanceof Rt)throw ta;if(s===ne.error&&!r)throw a._error;if(s===ne.error)return E.createElement(mt.Provider,{value:a,children:r});if(s===ne.success)return E.createElement(mt.Provider,{value:a,children:t});throw a}}function na(e){let{children:t}=e,r=Zn(),n=typeof t=="function"?t(r):t;return E.createElement(E.Fragment,null,n)}function Pa(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:E.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:E.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:E.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}export{Rt as A,Ra as B,ya as C,Ot as D,Pt as E,va as F,ga as G,yn as I,Ie as N,wa as O,Ea as R,Ut as a,pa as b,da as c,ia as d,Pa as e,Qn as f,Bn as g,fa as h,ua as i,Wn as j,Ge as k,ba as l,xe as m,ma as n,ge as o,O as p,ha as q,ce as r,Xe as s,Xt as t,ca as u,ve as v,la as w,Ye as x,oa as y,sa as z};
