/**
 * Unified Billing Action Handler
 * Centralizes all billing-related form actions with proper error handling
 */

import type { ActionFunctionArgs } from "@remix-run/node";
import { json, redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { getEnvironmentConfig } from "./env-validation.server";
import type { Prisma } from "@prisma/client";
import { validateBillingFormData } from "./validation.server";
import { validateCSRFFromForm } from "./csrf.server";
import { applyRateLimit, RATE_LIMITERS } from "./rate-limiting.server";
import db from "../db.server";

export interface BillingActionResult {
  success?: boolean;
  error?: string;
  confirmationUrl?: string;
  subscriptionId?: string;
  purchaseId?: string;
  redirectTo?: string;
}

/**
 * Handles subscription creation with proper validation and error handling
 */
export async function handleCreateSubscription(
  sanitizedData: any,
  admin: any,
  shop: string
): Promise<BillingActionResult> {
  try {
    const planId = sanitizedData.planId;

    const config = getEnvironmentConfig();
    const returnUrl = `${config.SHOPIFY_APP_URL}/billing-success?type=subscription&shop=${shop}`;
    
    console.log(`🔄 Creating subscription for shop: ${shop}, plan: ${planId}`);
    
    const billingService = new BillingService(admin, shop);
    const result = await billingService.createSubscription(planId, returnUrl);
    
    // Check for errors in the response
    if (result.data?.appSubscriptionCreate?.userErrors?.length > 0) {
      const error = result.data.appSubscriptionCreate.userErrors[0];
      console.error(`❌ Subscription creation failed:`, error);
      return { 
        error: `Subscription creation failed: ${error.message}${error.field ? ` (Field: ${error.field})` : ''}` 
      };
    }

    // Check if subscription was created successfully
    if (!result.data?.appSubscriptionCreate?.appSubscription) {
      console.error(`❌ No subscription returned from Shopify`);
      return { error: "Subscription creation failed: No subscription returned from Shopify" };
    }

    // Check if confirmation URL exists
    if (!result.data.appSubscriptionCreate.confirmationUrl) {
      console.error(`❌ No confirmation URL returned from Shopify`);
      return { error: "Subscription creation failed: No confirmation URL returned" };
    }

    const subscriptionId = result.data.appSubscriptionCreate.appSubscription.id;
    const confirmationUrl = result.data.appSubscriptionCreate.confirmationUrl;
    
    console.log(`✅ Subscription created successfully:`, {
      subscriptionId,
      confirmationUrl: confirmationUrl.substring(0, 100) + '...'
    });

    return {
      success: true,
      confirmationUrl,
      subscriptionId
    };

  } catch (error) {
    console.error(`❌ Subscription creation error for shop ${shop}:`, error);
    return { 
      error: error instanceof Error ? error.message : "An unexpected error occurred during subscription creation" 
    };
  }
}

/**
 * Handles subscription cancellation
 */
export async function handleCancelSubscription(
  sanitizedData: any,
  admin: any,
  shop: string
): Promise<BillingActionResult> {
  try {
    const subscriptionId = sanitizedData.subscriptionId;

    console.log(`🔄 Cancelling subscription for shop: ${shop}, subscription: ${subscriptionId}`);

    const billingService = new BillingService(admin, shop);
    const result = await billingService.cancelSubscription(subscriptionId);

    if (result.data?.appSubscriptionCancel?.userErrors?.length > 0) {
      const error = result.data.appSubscriptionCancel.userErrors[0];
      console.error(`❌ Subscription cancellation failed:`, error);
      return {
        error: `Subscription cancellation failed: ${error.message}`
      };
    }

    // Update local database to reflect cancellation
    if (result.data?.appSubscriptionCancel?.appSubscription) {
      try {
        await db.$transaction(async (tx: Prisma.TransactionClient) => {
          // Update billing subscription status
          await tx.billingSubscription.updateMany({
            where: {
              shop: shop,
              subscriptionId: subscriptionId
            },
            data: {
              status: 'CANCELLED',
              updatedAt: new Date()
            }
          });

          // Log billing event
          await tx.billingEvent.create({
            data: {
              shop: shop,
              eventType: 'subscription_cancelled',
              referenceId: subscriptionId,
              eventData: JSON.stringify({
                subscriptionId: subscriptionId,
                status: 'CANCELLED',
                cancelledAt: new Date().toISOString()
              })
            }
          });

          // Update session to remove subscription info
          await tx.session.updateMany({
            where: { shop: shop },
            data: {
              subscriptionId: null,
              subscriptionStatus: 'CANCELLED',
              billingPlanId: 'pay_per_use', // Default back to pay-per-use
              lastBillingCheck: new Date()
            }
          });
        });

        console.log(`✅ Database updated for cancelled subscription: ${subscriptionId}`);
      } catch (dbError) {
        console.error(`⚠️ Failed to update database for cancelled subscription:`, dbError);
        // Don't fail the whole operation if database update fails
      }

      // Invalidate billing cache to force refresh
      const { invalidateBillingCache } = await import("../utils/cache.server");
      invalidateBillingCache(shop);
      console.log(`🔄 Billing cache invalidated for shop: ${shop}`);
    }

    console.log(`✅ Subscription cancelled successfully for shop: ${shop}`);

    return {
      success: true,
      redirectTo: "/app/billing?cancelled=true"
    };

  } catch (error) {
    console.error(`❌ Subscription cancellation error for shop ${shop}:`, error);
    return {
      error: error instanceof Error ? error.message : "An unexpected error occurred during subscription cancellation"
    };
  }
}

/**
 * Handles pay-per-use purchase creation
 */
export async function handleCreatePayPerUsePurchase(
  sanitizedData: any,
  admin: any,
  shop: string
): Promise<BillingActionResult> {
  try {
    const productCount = sanitizedData.productCount;

    const config = getEnvironmentConfig();
    const returnUrl = `${config.SHOPIFY_APP_URL}/billing-success?type=purchase&shop=${shop}`;
    
    console.log(`🔄 Creating pay-per-use purchase for shop: ${shop}, products: ${productCount}`);
    
    const billingService = new BillingService(admin, shop);
    const result = await billingService.createOneTimePurchase(productCount, returnUrl);
    
    if (result.data?.appPurchaseOneTimeCreate?.userErrors?.length > 0) {
      const error = result.data.appPurchaseOneTimeCreate.userErrors[0];
      console.error(`❌ Pay-per-use purchase creation failed:`, error);
      return { 
        error: `Purchase creation failed: ${error.message}` 
      };
    }

    if (!result.data?.appPurchaseOneTimeCreate?.confirmationUrl) {
      console.error(`❌ No confirmation URL returned for pay-per-use purchase`);
      return { error: "Purchase creation failed: No confirmation URL returned" };
    }

    const purchaseId = result.data.appPurchaseOneTimeCreate.appPurchaseOneTime?.id;
    const confirmationUrl = result.data.appPurchaseOneTimeCreate.confirmationUrl;
    
    console.log(`✅ Pay-per-use purchase created successfully:`, {
      purchaseId,
      confirmationUrl: confirmationUrl.substring(0, 100) + '...'
    });

    return {
      success: true,
      confirmationUrl,
      purchaseId
    };

  } catch (error) {
    console.error(`❌ Pay-per-use purchase creation error for shop ${shop}:`, error);
    return { 
      error: error instanceof Error ? error.message : "An unexpected error occurred during purchase creation" 
    };
  }
}

/**
 * Main billing action handler that routes to specific handlers
 */
export async function handleBillingAction({ request }: ActionFunctionArgs) {
  try {
    console.log(`🔄 Billing action - Request URL: ${request.url}`);

    // Authenticate the request
    const { admin, session } = await authenticate.admin(request);
    console.log(`✅ Billing action - Authenticated for shop: ${session.shop}`);

    if (!session.shop) {
      console.error(`❌ No shop in session`);
      return json({ error: "Authentication failed: No shop in session" }, { status: 401 });
    }

    // Check rate limiting
    try {
      await applyRateLimit(RATE_LIMITERS.BILLING_OPERATIONS(session.shop), {
        action: 'billing_action',
        shop: session.shop
      });
    } catch (error) {
      console.error(`❌ Rate limit exceeded for shop: ${session.shop}`);
      return json({ error: "Too many requests. Please try again later." }, { status: 429 });
    }

    const formData = await request.formData();

    // Validate CSRF token
    if (!validateCSRFFromForm(formData, session.shop)) {
      console.error(`❌ CSRF validation failed for shop: ${session.shop}`);
      return json({ error: "Invalid security token. Please refresh the page and try again." }, { status: 403 });
    }

    // Validate all form data
    console.log(`📋 Raw form data:`, Object.fromEntries(formData.entries()));

    const validationResult = validateBillingFormData(formData);
    if (!validationResult.isValid) {
      console.error(`❌ Form validation failed:`, validationResult.errors);
      return json({
        error: `Validation failed: ${validationResult.errors.join(', ')}`
      }, { status: 400 });
    }

    const sanitizedData = validationResult.sanitizedValue!;
    const action = sanitizedData.action;

    console.log(`🎯 Billing action type: ${action}`);
    console.log(`✅ Sanitized data:`, sanitizedData);

    let result: BillingActionResult;

    switch (action) {
      case "create_subscription":
        result = await handleCreateSubscription(sanitizedData, admin, session.shop);
        break;

      case "cancel_subscription":
        result = await handleCancelSubscription(sanitizedData, admin, session.shop);
        break;

      case "create_pay_per_use_purchase":
        result = await handleCreatePayPerUsePurchase(sanitizedData, admin, session.shop);
        break;

      default:
        return json({ error: `Unknown action: ${action}` }, { status: 400 });
    }

    // Handle the result
    if (result.error) {
      console.error(`❌ Billing action failed:`, result.error);
      return json({ error: result.error }, { status: 400 });
    }

    if (result.redirectTo) {
      console.log(`🔄 Redirecting to: ${result.redirectTo}`);
      return redirect(result.redirectTo);
    }

    if (result.confirmationUrl) {
      console.log(`✅ Billing action successful, returning confirmation URL`);
      return json({
        success: true,
        confirmationUrl: result.confirmationUrl,
        subscriptionId: result.subscriptionId,
        purchaseId: result.purchaseId
      });
    }

    return json({ success: true });

  } catch (error) {
    console.error(`❌ Billing action handler error:`, error);
    return json({ 
      error: error instanceof Error ? error.message : "An unexpected error occurred" 
    }, { status: 500 });
  }
}
