import { useEffect } from "react";
import { useNavigate } from "@remix-run/react";
import { <PERSON>, Spinner, BlockStack, Text } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Review() {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to new SEO dashboard
    navigate('/app/seo-dashboard');
  }, [navigate]);

  return (
    <Page title="SEO Review">
      <TitleBar title="SEO Review" />
      <BlockStack gap="400" align="center">
        <Spinner size="large" />
        <Text as="p" variant="bodyMd">Redirecting to SEO Dashboard...</Text>
      </BlockStack>
    </Page>
  );
}
