import * as React from "react"

import { cn } from "@/lib/utils"

function Input({ className, type, ...props }: React.ComponentProps<"input">) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "flex h-14 w-full rounded-2xl border-2 border-gray-300 bg-white px-6 py-4 text-base font-medium text-black placeholder:text-gray-400 transition-all duration-300 focus:border-black focus:ring-4 focus:ring-gray-100 focus:outline-none hover:border-gray-400 hover:shadow-lg focus:shadow-xl disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50 shadow-md",
        className
      )}
      {...props}
    />
  )
}

export { Input }
