/**
 * Unified Type Definitions
 * Centralized type definitions for improved type safety across the application
 */

// ============================================================================
// SHOPIFY TYPES
// ============================================================================

export interface ShopifyProduct {
  id: string;
  title: string;
  description: string;
  productType: string;
  vendor: string;
  handle: string;
  status: 'ACTIVE' | 'ARCHIVED' | 'DRAFT';
  seo: {
    title: string;
    description: string;
  };
  images: {
    edges: Array<{
      node: {
        id: string;
        altText: string | null;
        url: string;
      };
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ShopifySession {
  id: string;
  shop: string;
  accessToken: string;
  expires?: Date;
  userId?: string;
  email?: string;
  isOnline: boolean;
  scope?: string;
}

export interface ShopifySubscription {
  id: string;
  name: string;
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'FROZEN' | 'PENDING';
  trialDays: number;
  currentPeriodEnd: string;
  lineItems: Array<{
    id: string;
    plan: {
      pricingDetails: {
        __typename: string;
      };
    };
  }>;
}

export interface ShopifyPurchase {
  id: string;
  name: string;
  status: 'ACTIVE' | 'CANCELLED' | 'EXPIRED' | 'PENDING';
  createdAt: string;
  price: {
    amount: string;
    currencyCode: string;
  };
}

// ============================================================================
// BILLING TYPES
// ============================================================================

export interface BillingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: 'EVERY_30_DAYS' | 'ANNUAL';
  trialDays: number;
  features: string[];
  limits: {
    products: number;
    optimizations: number;
  };
}

export interface BillingSubscription {
  id: string;
  shop: string;
  subscriptionId: string;
  planId: string;
  status: string;
  trialDays: number;
  trialEndsAt: Date | null;
  currentPeriodEnd: Date | null;
  priceAmount: number;
  priceCurrency: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BillingUsage {
  id: string;
  shop: string;
  subscriptionId?: string;
  purchaseId?: string;
  usageType: 'optimization' | 'api_call' | 'product_update';
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  metadata?: Record<string, any>;
  createdAt: Date;
}

export interface SubscriptionData {
  hasActiveSubscription: boolean;
  subscriptionStatus: string | null;
  planId: string | null;
  trialEndsAt: Date | null;
  currentPeriodEnd: Date | null;
  isInTrial: boolean;
  daysLeftInTrial: number;
  canUseFeatures: boolean;
  usageData: {
    optimizationsUsed: number;
    optimizationsLimit: number;
    productsOptimized: number;
    productsLimit: number;
  };
}

// ============================================================================
// SEO TYPES
// ============================================================================

export interface SEOOptimizationSettings {
  updateProductTitle: boolean;
  updateProductDescription: boolean;
  updateSeoFields: boolean;
  updateHandle: boolean;
  updateImageAlts: boolean;
  autoApply: boolean;
}

export interface SEOOptimizationResult {
  productId: string;
  originalProductTitle: string;
  optimizedProductTitle?: string;
  originalProductDescription: string;
  optimizedProductDescription?: string;
  originalSeoTitle: string;
  optimizedSeoTitle?: string;
  originalSeoDescription: string;
  optimizedSeoDescription?: string;
  originalHandle: string;
  optimizedHandle?: string;
  viralKeyword?: string;
  seoScore?: number;
  error?: string;
}

export interface ProductSEOData {
  title: string;
  description: string;
  productType: string;
  vendor: string;
  handle: string;
  seoTitle: string;
  seoDescription: string;
}

export interface OptimizedSEOData {
  optimizedTitle?: string;
  optimizedDescription?: string;
  optimizedSeoTitle?: string;
  optimizedSeoDescription?: string;
  optimizedHandle?: string;
  viralKeyword?: string;
  seoScore?: number;
}

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface GraphQLResponse<T = any> {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: Array<string | number>;
    extensions?: {
      code?: string;
      cost?: number;
      maxCost?: number;
    };
  }>;
  extensions?: {
    cost?: {
      requestedQueryCost: number;
      actualQueryCost: number;
      throttleStatus: {
        maximumAvailable: number;
        currentlyAvailable: number;
        restoreRate: number;
      };
    };
  };
}

// ============================================================================
// FORM TYPES
// ============================================================================

export interface FormValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface FormState<T = any> {
  data: T;
  errors: FormValidationError[];
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
}

// ============================================================================
// CACHE TYPES
// ============================================================================

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

export interface CacheOptions {
  ttl?: number;
  tags?: string[];
  namespace?: string;
}

// ============================================================================
// RATE LIMITING TYPES
// ============================================================================

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  identifier: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ErrorContext {
  shop?: string;
  userId?: string;
  action?: string;
  metadata?: Record<string, any>;
}

export interface AppError {
  code: string;
  message: string;
  userMessage: string;
  statusCode: number;
  context?: ErrorContext;
  originalError?: Error;
}

// ============================================================================
// WEBHOOK TYPES
// ============================================================================

export interface WebhookPayload {
  id: string;
  topic: string;
  shop: string;
  created_at: string;
  updated_at: string;
  [key: string]: any;
}

export interface WebhookContext {
  payload: WebhookPayload;
  session: ShopifySession;
  topic: string;
  shop: string;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type Nullable<T> = T | null;

export type Optional<T> = T | undefined;

// ============================================================================
// COMPONENT TYPES
// ============================================================================

export interface LoaderData {
  [key: string]: any;
}

export interface ActionData {
  success?: boolean;
  error?: string;
  data?: any;
}

export interface RouteContext {
  session: ShopifySession;
  admin: any;
  billing: SubscriptionData;
  shop: string;
}

// ============================================================================
// DATABASE TYPES
// ============================================================================

export interface DatabaseHealthCheck {
  isHealthy: boolean;
  connectionTime: number;
  error?: string;
  details: {
    canConnect: boolean;
    canQuery: boolean;
    canWrite: boolean;
    modelsAvailable: boolean;
  };
}

export interface DatabaseStats {
  sessions: number;
  billingSubscriptions: number;
  billingPurchases: number;
  billingUsage: number;
  billingEvents: number;
  csrfTokens: number;
  rateLimitEntries: number;
}
