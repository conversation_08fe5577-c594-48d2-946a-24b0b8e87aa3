# AI BULK SEO - Deployment Guide

## Prerequisites

- Node.js 18.20+ or 20.10+ or 21.0.0+
- npm or yarn
- Shopify Partner account
- Vercel account
- Gemini API key (for SEO optimization)

## Environment Variables

Copy `.env.example` to `.env` and configure the following variables:

### Required Variables
- `SHOPIFY_API_KEY` - Your Shopify app's API key
- `SHOPIFY_API_SECRET` - Your Shopify app's API secret
- `SHOPIFY_APP_URL` - Your production URL (e.g., https://your-app.vercel.app)
- `SCOPES` - Required Shopify scopes: `read_products,write_products,read_orders,write_orders`
- `DATABASE_URL` - Database connection string
- `SESSION_SECRET` - Secure random string (64+ characters)
- `GEMINI_API_KEY` - Google Gemini API key for SEO optimization

### Optional Variables
- `BILLING_ENABLED` - Enable billing features (default: true)
- `BILLING_TRIAL_DAYS` - Trial period in days (default: 1)
- `BILLING_CURRENCY` - Billing currency (default: USD)
- `RATE_LIMIT_ENABLED` - Enable rate limiting (default: true)
- `CACHE_ENABLED` - Enable caching (default: true)

## Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel**
   ```bash
   vercel login
   ```

3. **Deploy**
   ```bash
   vercel --prod
   ```

4. **Set Environment Variables**
   In Vercel dashboard, go to your project settings and add all environment variables from your `.env` file.

## Database Setup

For production, consider using:
- **PostgreSQL** (recommended for Vercel)
- **MySQL**
- **SQLite** (for development only)

Update your `DATABASE_URL` accordingly and run:
```bash
npx prisma migrate deploy
npx prisma generate
```

## Shopify App Configuration

1. Update your app URL in Shopify Partner Dashboard
2. Set webhook endpoints:
   - App uninstalled: `https://your-app.vercel.app/webhooks/app/uninstalled`
   - Subscription updates: `https://your-app.vercel.app/webhooks/app_subscriptions/update`
   - Purchase updates: `https://your-app.vercel.app/webhooks/app_purchases_one_time/update`

## Build Verification

Before deploying, verify the build works locally:
```bash
npm run build
npm start
```

## Security Checklist

- [ ] All environment variables are set in Vercel
- [ ] SESSION_SECRET is a secure random string
- [ ] Database is properly secured
- [ ] HTTPS is enabled (automatic with Vercel)
- [ ] Rate limiting is enabled
- [ ] CSRF protection is active

## Monitoring

The app includes built-in monitoring and error handling. Check Vercel logs for any issues.

## Support

For deployment issues, check:
1. Vercel build logs
2. Runtime logs in Vercel dashboard
3. Database connection status
4. Environment variable configuration
