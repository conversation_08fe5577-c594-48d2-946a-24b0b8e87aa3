var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
var _a;
import { jsx, jsxs, Fragment } from "react/jsx-runtime";
import { PassThrough } from "stream";
import { renderToPipeableStream } from "react-dom/server";
import { RemixServer, Meta, Links, Outlet, ScrollRestoration, Scripts, useLoaderData, useActionData, Form, useNavigate, useLocation, Link, useRouteError, useFetcher } from "@remix-run/react";
import { createReadableStreamFromReadable, redirect, json } from "@remix-run/node";
import { isbot } from "isbot";
import "@shopify/shopify-app-remix/adapters/node";
import { shopifyApp, DeliveryMethod, AppDistribution, ApiVersion, LoginErrorType, boundary } from "@shopify/shopify-app-remix/server";
import { restResources } from "@shopify/shopify-api/rest/admin/2025-01";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import { PrismaClient } from "@prisma/client";
import * as React from "react";
import { useState, useEffect, useRef, useCallback } from "react";
import { AppProvider, Page, Card as Card$1, FormLayout, Text, TextField, Button as Button$1, Layout, BlockStack, Link as Link$1, List, Box, Spinner } from "@shopify/polaris";
import { AppProvider as AppProvider$1 } from "@shopify/shopify-app-remix/react";
import { NavMenu, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import { motion, AnimatePresence } from "framer-motion";
import { randomBytes, createHash } from "crypto";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";
import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import * as SelectPrimitive from "@radix-ui/react-select";
import { ChevronDownIcon, CheckIcon, ChevronUpIcon, Star, CreditCard, AlertTriangle, CheckCircle, Zap, X, Shield, DollarSign, Check, Clock } from "lucide-react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import * as SeparatorPrimitive from "@radix-ui/react-separator";
import * as CheckboxPrimitive from "@radix-ui/react-checkbox";
let prisma;
if (process.env.NODE_ENV === "production") {
  console.log("🚀 Initializing production database with PostgreSQL");
  prisma = new PrismaClient({
    log: ["error"],
    errorFormat: "pretty"
  });
} else {
  console.log("🔧 Development mode - using PostgreSQL");
  if (!global.prismaGlobal) {
    global.prismaGlobal = new PrismaClient({
      log: ["query", "error", "warn"],
      errorFormat: "pretty"
    });
  }
  prisma = global.prismaGlobal;
}
prisma.$connect().catch((error) => {
  console.error("❌ Failed to initialize database connection:", error);
});
process.on("SIGINT", async () => {
  console.log("🛑 Received SIGINT, closing database connection...");
  await prisma.$disconnect();
  process.exit(0);
});
process.on("SIGTERM", async () => {
  console.log("🛑 Received SIGTERM, closing database connection...");
  await prisma.$disconnect();
  process.exit(0);
});
const db = prisma;
class SessionTokenStorage extends PrismaSessionStorage {
  constructor(prisma2) {
    super(prisma2);
  }
  async storeSession(session) {
    try {
      console.log(`💾 [SESSION-TOKEN-STORAGE] Storing session for shop: ${session.shop}`);
      const result = await super.storeSession(session);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Session stored successfully for shop: ${session.shop}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to store session for shop: ${session.shop}`, error);
      return false;
    }
  }
  async loadSession(id) {
    try {
      console.log(`🔍 [SESSION-TOKEN-STORAGE] Loading session: ${id}`);
      const session = await super.loadSession(id);
      if (session) {
        console.log(`✅ [SESSION-TOKEN-STORAGE] Session loaded for shop: ${session.shop}`);
      } else {
        console.log(`⚠️ [SESSION-TOKEN-STORAGE] No session found for ID: ${id}`);
      }
      return session;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to load session: ${id}`, error);
      return void 0;
    }
  }
  async deleteSession(id) {
    try {
      console.log(`🗑️ [SESSION-TOKEN-STORAGE] Deleting session: ${id}`);
      const result = await super.deleteSession(id);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Session deleted: ${id}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to delete session: ${id}`, error);
      return false;
    }
  }
  async deleteSessions(ids) {
    try {
      console.log(`🗑️ [SESSION-TOKEN-STORAGE] Deleting sessions: ${ids.join(", ")}`);
      const result = await super.deleteSessions(ids);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Sessions deleted: ${ids.join(", ")}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to delete sessions: ${ids.join(", ")}`, error);
      return false;
    }
  }
  async findSessionsByShop(shop) {
    try {
      console.log(`🔍 [SESSION-TOKEN-STORAGE] Finding sessions for shop: ${shop}`);
      const result = await super.findSessionsByShop(shop);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Found ${result.length} sessions for shop: ${shop}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to find sessions for shop: ${shop}`, error);
      return [];
    }
  }
}
const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: (_a = process.env.SCOPES) == null ? void 0 : _a.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new SessionTokenStorage(db),
  distribution: AppDistribution.AppStore,
  restResources,
  future: {
    // Disable the problematic new auth strategy - it causes 302 redirect loops
    unstable_newEmbeddedAuthStrategy: false
  },
  // Session configuration for maximum persistence
  useOnlineTokens: false,
  // Use offline tokens for better persistence
  exitIframePath: "/exitiframe",
  // Force session persistence
  isEmbeddedApp: true,
  // Enhanced webhook configuration
  webhooks: {
    APP_UNINSTALLED: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/uninstalled"
    },
    APP_SUBSCRIPTIONS_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_subscriptions/update"
    },
    APP_PURCHASES_ONE_TIME_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_purchases_one_time/update"
    },
    APP_SCOPES_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/scopes_update"
    }
  },
  // Enhanced authentication configuration
  auth: {
    path: "/auth",
    callbackPath: "/auth/callback"
  },
  ...process.env.SHOP_CUSTOM_DOMAIN ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] } : {}
});
ApiVersion.January25;
const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
const authenticate = shopify.authenticate;
shopify.unauthenticated;
const login = shopify.login;
shopify.registerWebhooks;
shopify.sessionStorage;
const streamTimeout = 5e3;
async function handleRequest(request, responseStatusCode, responseHeaders, remixContext) {
  addDocumentResponseHeaders(request, responseHeaders);
  const userAgent = request.headers.get("user-agent");
  const callbackName = isbot(userAgent ?? "") ? "onAllReady" : "onShellReady";
  return new Promise((resolve, reject) => {
    const { pipe, abort } = renderToPipeableStream(
      /* @__PURE__ */ jsx(
        RemixServer,
        {
          context: remixContext,
          url: request.url
        }
      ),
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);
          responseHeaders.set("Content-Type", "text/html");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          console.error(error);
        }
      }
    );
    setTimeout(abort, streamTimeout + 1e3);
  });
}
const entryServer = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: handleRequest,
  streamTimeout
}, Symbol.toStringTag, { value: "Module" }));
const styles = "/assets/tailwind-DD_5iXyO.css";
const links$2 = () => [
  { rel: "stylesheet", href: styles }
];
function App$2() {
  return /* @__PURE__ */ jsxs("html", { children: [
    /* @__PURE__ */ jsxs("head", { children: [
      /* @__PURE__ */ jsx("meta", { charSet: "utf-8" }),
      /* @__PURE__ */ jsx("meta", { name: "viewport", content: "width=device-width,initial-scale=1" }),
      /* @__PURE__ */ jsx("link", { rel: "preconnect", href: "https://cdn.shopify.com/" }),
      /* @__PURE__ */ jsx(
        "link",
        {
          rel: "stylesheet",
          href: "https://cdn.shopify.com/static/fonts/inter/v4/styles.css"
        }
      ),
      /* @__PURE__ */ jsx(Meta, {}),
      /* @__PURE__ */ jsx(Links, {})
    ] }),
    /* @__PURE__ */ jsxs("body", { children: [
      /* @__PURE__ */ jsx(Outlet, {}),
      /* @__PURE__ */ jsx(ScrollRestoration, {}),
      /* @__PURE__ */ jsx(Scripts, {})
    ] })
  ] });
}
const route0 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$2,
  links: links$2
}, Symbol.toStringTag, { value: "Module" }));
const action$b = async ({ request }) => {
  var _a2, _b;
  try {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    console.log(`Received ${topic} webhook for ${shop}:`, JSON.stringify(payload, null, 2));
    if (!(payload == null ? void 0 : payload.app_purchase_one_time)) {
      console.error("❌ Invalid webhook payload: missing app_purchase_one_time");
      return new Response("Invalid payload", { status: 400 });
    }
    const purchase = payload.app_purchase_one_time;
    const purchaseId = purchase.admin_graphql_api_id || purchase.id;
    const status = purchase.status;
    const priceAmount = ((_a2 = purchase.price) == null ? void 0 : _a2.amount) ? parseFloat(purchase.price.amount) : 0;
    const currency = ((_b = purchase.price) == null ? void 0 : _b.currency_code) || "USD";
    const productCount = Math.round(priceAmount / 0.1);
    const description = purchase.name || "One-time purchase";
    console.log(`📋 Processing purchase: ${purchaseId}, status: ${status}, amount: ${priceAmount}, products: ${productCount}`);
    await db.$transaction(async (tx) => {
      if (session) {
        await tx.session.update({
          where: {
            id: session.id
          },
          data: {
            lastBillingCheck: /* @__PURE__ */ new Date()
          }
        });
        console.log(`✅ Updated session for shop: ${shop}`);
      }
      await tx.billingPurchase.upsert({
        where: {
          purchaseId
        },
        update: {
          status,
          updatedAt: /* @__PURE__ */ new Date()
        },
        create: {
          shop,
          purchaseId,
          status,
          amount: priceAmount,
          currency,
          productCount,
          description
        }
      });
      await tx.billingEvent.create({
        data: {
          shop,
          eventType: "purchase_updated",
          referenceId: purchaseId,
          eventData: JSON.stringify({
            status,
            amount: priceAmount,
            currency,
            productCount,
            description,
            webhookTopic: topic,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          })
        }
      });
      console.log(`✅ Processed purchase update for shop: ${shop}, status: ${status}`);
    });
    switch (purchase.status) {
      case "ACCEPTED":
        console.log(`✅ One-time purchase ${purchaseId} was accepted for shop ${shop} - ${productCount} credits available`);
        break;
      case "DECLINED":
        console.log(`❌ One-time purchase ${purchaseId} was declined for shop ${shop}`);
        break;
      case "PENDING":
        console.log(`⏳ One-time purchase ${purchaseId} is pending for shop ${shop}`);
        break;
    }
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("App purchase one-time webhook error:", error);
    return new Response("Error processing webhook", { status: 500 });
  }
};
const route1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$b
}, Symbol.toStringTag, { value: "Module" }));
class EnhancedError extends Error {
  constructor(appError) {
    super(appError.message);
    __publicField(this, "code");
    __publicField(this, "userMessage");
    __publicField(this, "statusCode");
    __publicField(this, "context");
    __publicField(this, "originalError");
    this.name = "EnhancedError";
    this.code = appError.code;
    this.userMessage = appError.userMessage;
    this.statusCode = appError.statusCode;
    this.context = appError.context;
    this.originalError = appError.originalError;
  }
}
const ERROR_CODES = {
  // Authentication Errors
  AUTH_SESSION_EXPIRED: {
    code: "AUTH_SESSION_EXPIRED",
    message: "Session has expired",
    userMessage: "Your session has expired. Please log in again.",
    statusCode: 401
  },
  AUTH_INVALID_SESSION: {
    code: "AUTH_INVALID_SESSION",
    message: "Invalid session",
    userMessage: "Invalid session. Please log in again.",
    statusCode: 401
  },
  AUTH_MISSING_PERMISSIONS: {
    code: "AUTH_MISSING_PERMISSIONS",
    message: "Missing required permissions",
    userMessage: "You don't have permission to perform this action.",
    statusCode: 403
  },
  AUTH_ACCOUNT_LOCKED: {
    code: "AUTH_ACCOUNT_LOCKED",
    message: "Account temporarily locked",
    userMessage: "Account is temporarily locked due to too many failed attempts. Please try again later.",
    statusCode: 423
  },
  ROUTE_ACCESS_DENIED: {
    code: "ROUTE_ACCESS_DENIED",
    message: "Route access denied",
    userMessage: "You don't have permission to access this page.",
    statusCode: 403
  },
  // Billing Errors
  BILLING_SUBSCRIPTION_NOT_FOUND: {
    code: "BILLING_SUBSCRIPTION_NOT_FOUND",
    message: "Subscription not found",
    userMessage: "No active subscription found. Please subscribe to continue.",
    statusCode: 404
  },
  BILLING_PAYMENT_FAILED: {
    code: "BILLING_PAYMENT_FAILED",
    message: "Payment processing failed",
    userMessage: "Payment failed. Please check your payment method and try again.",
    statusCode: 402
  },
  BILLING_INSUFFICIENT_CREDITS: {
    code: "BILLING_INSUFFICIENT_CREDITS",
    message: "Insufficient credits",
    userMessage: "You don't have enough credits. Please purchase more to continue.",
    statusCode: 402
  },
  BILLING_TRIAL_EXPIRED: {
    code: "BILLING_TRIAL_EXPIRED",
    message: "Trial period has expired",
    userMessage: "Your trial period has expired. Please subscribe to continue using the app.",
    statusCode: 402
  },
  // Validation Errors
  VALIDATION_INVALID_INPUT: {
    code: "VALIDATION_INVALID_INPUT",
    message: "Invalid input provided",
    userMessage: "Please check your input and try again.",
    statusCode: 400
  },
  VALIDATION_MISSING_REQUIRED_FIELD: {
    code: "VALIDATION_MISSING_REQUIRED_FIELD",
    message: "Required field is missing",
    userMessage: "Please fill in all required fields.",
    statusCode: 400
  },
  VALIDATION_CSRF_FAILED: {
    code: "VALIDATION_CSRF_FAILED",
    message: "CSRF validation failed",
    userMessage: "Security validation failed. Please refresh the page and try again.",
    statusCode: 403
  },
  // API Errors
  API_RATE_LIMIT_EXCEEDED: {
    code: "API_RATE_LIMIT_EXCEEDED",
    message: "API rate limit exceeded",
    userMessage: "Too many requests. Please wait a moment and try again.",
    statusCode: 429
  },
  RATE_LIMIT_EXCEEDED: {
    code: "RATE_LIMIT_EXCEEDED",
    message: "Rate limit exceeded",
    userMessage: "Too many requests. Please wait a moment and try again.",
    statusCode: 429
  },
  API_SHOPIFY_ERROR: {
    code: "API_SHOPIFY_ERROR",
    message: "Shopify API error",
    userMessage: "There was an issue connecting to Shopify. Please try again.",
    statusCode: 502
  },
  API_EXTERNAL_SERVICE_ERROR: {
    code: "API_EXTERNAL_SERVICE_ERROR",
    message: "External service error",
    userMessage: "External service is temporarily unavailable. Please try again later.",
    statusCode: 503
  },
  GRAPHQL_QUERY_TOO_COMPLEX: {
    code: "GRAPHQL_QUERY_TOO_COMPLEX",
    message: "GraphQL query too complex",
    userMessage: "The request is too complex. Please try with fewer items.",
    statusCode: 400
  },
  // Database Errors
  DATABASE_CONNECTION_ERROR: {
    code: "DATABASE_CONNECTION_ERROR",
    message: "Database connection failed",
    userMessage: "Database is temporarily unavailable. Please try again.",
    statusCode: 503
  },
  DATABASE_CONSTRAINT_VIOLATION: {
    code: "DATABASE_CONSTRAINT_VIOLATION",
    message: "Database constraint violation",
    userMessage: "Data validation failed. Please check your input.",
    statusCode: 400
  },
  // SEO Optimization Errors
  SEO_OPTIMIZATION_IN_PROGRESS: {
    code: "SEO_OPTIMIZATION_IN_PROGRESS",
    message: "SEO optimization already in progress",
    userMessage: "An SEO optimization is already running. Please wait for it to complete.",
    statusCode: 409
  },
  // Generic Errors
  INTERNAL_SERVER_ERROR: {
    code: "INTERNAL_SERVER_ERROR",
    message: "Internal server error",
    userMessage: "Something went wrong. Please try again or contact support.",
    statusCode: 500
  },
  NOT_FOUND: {
    code: "NOT_FOUND",
    message: "Resource not found",
    userMessage: "The requested resource was not found.",
    statusCode: 404
  }
};
function createError(errorCode, context, originalError) {
  const errorDef = ERROR_CODES[errorCode];
  return new EnhancedError({
    ...errorDef,
    context,
    originalError
  });
}
async function logError(error, context) {
  var _a2, _b;
  try {
    const errorData = error instanceof EnhancedError ? error : {
      code: "UNKNOWN_ERROR",
      message: error.message,
      userMessage: "An unexpected error occurred",
      statusCode: 500,
      context,
      originalError: error
    };
    console.error("❌ Error occurred:", {
      code: errorData.code,
      message: errorData.message,
      context: errorData.context,
      stack: error.stack
    });
    if ((_a2 = errorData.context) == null ? void 0 : _a2.shop) {
      await db.billingEvent.create({
        data: {
          shop: errorData.context.shop,
          eventType: "error_logged",
          referenceId: null,
          eventData: JSON.stringify({
            errorCode: errorData.code,
            errorMessage: errorData.message,
            userMessage: errorData.userMessage,
            statusCode: errorData.statusCode,
            context: errorData.context,
            stack: (_b = error.stack) == null ? void 0 : _b.substring(0, 1e3),
            // Limit stack trace length
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          })
        }
      });
    }
  } catch (logError2) {
    console.error("❌ Failed to log error:", logError2);
  }
}
const action$a = async ({ request }) => {
  try {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    console.log(`📨 Received ${topic} webhook for ${shop}`);
    console.log(`📋 Processing webhook directly: ${topic} for ${shop}`);
    if (!(payload == null ? void 0 : payload.app_subscription)) {
      console.error("❌ Invalid webhook payload: missing app_subscription");
      return new Response("Invalid payload", { status: 400 });
    }
    const subscription = payload.app_subscription;
    const subscriptionId = subscription.admin_graphql_api_id || subscription.id;
    const status = subscription.status;
    const trialDays = subscription.trial_days || 0;
    const trialEndsOn = subscription.trial_ends_on;
    const planName = subscription.name || "unknown";
    const isTest = subscription.test || false;
    console.log(`📋 Processing subscription: ${subscriptionId}, status: ${status}`);
    await db.$transaction(async (tx) => {
      var _a2, _b, _c, _d;
      if (session) {
        await tx.session.update({
          where: {
            id: session.id
          },
          data: {
            subscriptionId,
            subscriptionStatus: status,
            billingPlanId: determinePlanIdFromSubscription(subscription),
            trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
            lastBillingCheck: /* @__PURE__ */ new Date()
          }
        });
        console.log(`✅ Updated session for shop: ${shop}`);
      } else {
        await tx.session.updateMany({
          where: { shop },
          data: {
            subscriptionId,
            subscriptionStatus: status,
            billingPlanId: determinePlanIdFromSubscription(subscription),
            trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
            lastBillingCheck: /* @__PURE__ */ new Date()
          }
        });
        console.log(`✅ Updated session by shop: ${shop}`);
      }
      await tx.billingSubscription.upsert({
        where: {
          subscriptionId
        },
        update: {
          status,
          trialDays,
          trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
          currentPeriodStart: subscription.created_at ? new Date(subscription.created_at) : null,
          currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
          priceAmount: ((_b = (_a2 = subscription.line_items) == null ? void 0 : _a2[0]) == null ? void 0 : _b.price) ? parseFloat(subscription.line_items[0].price) : null,
          updatedAt: /* @__PURE__ */ new Date()
        },
        create: {
          shop,
          subscriptionId,
          planId: determinePlanIdFromSubscription(subscription),
          status,
          trialDays,
          trialEndsAt: trialEndsOn ? new Date(trialEndsOn) : null,
          currentPeriodStart: subscription.created_at ? new Date(subscription.created_at) : /* @__PURE__ */ new Date(),
          currentPeriodEnd: subscription.current_period_end ? new Date(subscription.current_period_end) : null,
          priceAmount: ((_d = (_c = subscription.line_items) == null ? void 0 : _c[0]) == null ? void 0 : _d.price) ? parseFloat(subscription.line_items[0].price) : null,
          priceCurrency: "USD"
        }
      });
      await tx.billingEvent.create({
        data: {
          shop,
          eventType: "subscription_updated",
          referenceId: subscriptionId,
          eventData: JSON.stringify({
            status,
            trialDays,
            trialEndsOn,
            planName,
            isTest,
            webhookTopic: topic,
            timestamp: (/* @__PURE__ */ new Date()).toISOString()
          })
        }
      });
      console.log(`✅ Processed subscription update for shop: ${shop}, status: ${status}`);
    });
    const { invalidateBillingCache: invalidateBillingCache2, setSubscriptionUpdateFlag: setSubscriptionUpdateFlag2 } = await Promise.resolve().then(() => cache_server);
    invalidateBillingCache2(shop, "subscription");
    console.log(`🔄 Billing cache invalidated for shop: ${shop} after subscription update`);
    setSubscriptionUpdateFlag2(shop);
    console.log(`🚩 Subscription update flag set for shop ${shop} via webhook`);
    switch (subscription.status) {
      case "ACTIVE":
        console.log(`Subscription ${subscriptionId} is now active for shop ${shop}`);
        break;
      case "CANCELLED":
        console.log(`Subscription ${subscriptionId} was cancelled for shop ${shop}`);
        try {
          await db.session.updateMany({
            where: { shop },
            data: {
              subscriptionId: null,
              subscriptionStatus: "CANCELLED",
              billingPlanId: "pay_per_use",
              lastBillingCheck: /* @__PURE__ */ new Date()
            }
          });
          console.log(`✅ Session updated for cancelled subscription: ${subscriptionId}`);
        } catch (error) {
          console.error(`⚠️ Failed to update session for cancelled subscription:`, error);
        }
        break;
      case "EXPIRED":
        console.log(`Subscription ${subscriptionId} has expired for shop ${shop}`);
        break;
      case "PENDING":
        if (subscription.trial_days && subscription.trial_days > 0) {
          console.log(`Subscription ${subscriptionId} is in trial period (${subscription.trial_days} days) for shop ${shop}`);
        } else {
          console.log(`Subscription ${subscriptionId} is pending payment for shop ${shop}`);
        }
        break;
    }
    console.log(`✅ Webhook processed successfully`);
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("❌ App subscription webhook error:", error);
    await logError(
      error instanceof Error ? error : new Error("Webhook processing failed"),
      {
        action: "webhook_subscription_update"
      }
    );
    if (error instanceof Error && error.message.includes("RATE_LIMIT_EXCEEDED")) {
      return new Response("Rate limit exceeded", { status: 429 });
    }
    return new Response("Error processing webhook", { status: 500 });
  }
};
function determinePlanIdFromSubscription(subscription) {
  var _a2;
  const lineItem = (_a2 = subscription.line_items) == null ? void 0 : _a2[0];
  if (!lineItem) return "unknown";
  const price = parseFloat(lineItem.price || "0");
  if (price === 199.99) return "annual";
  if (price === 19.99) return "monthly";
  return "unknown";
}
const route2 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$a
}, Symbol.toStringTag, { value: "Module" }));
const action$9 = async ({ request }) => {
  try {
    const { payload, session, topic, shop } = await authenticate.webhook(request);
    console.log(`📨 Received ${topic} webhook for ${shop}`);
    const current = payload.current;
    if (session) {
      await db.session.update({
        where: {
          id: session.id
        },
        data: {
          scope: current.toString()
        }
      });
      console.log(`✅ Updated scopes for session ${session.id}: ${current.join(", ")}`);
    } else {
      console.log(`⚠️ No session found for shop ${shop} - scopes update skipped`);
    }
    console.log(`✅ Scopes update webhook processed successfully for ${shop}`);
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("❌ Scopes update webhook error:", error);
    return new Response("Error processing webhook", { status: 500 });
  }
};
const route3 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$9
}, Symbol.toStringTag, { value: "Module" }));
const action$8 = async ({ request }) => {
  try {
    const { shop, session, topic } = await authenticate.webhook(request);
    console.log(`📨 Received ${topic} webhook for ${shop}`);
    if (session) {
      await db.session.deleteMany({ where: { shop } });
      console.log(`✅ Deleted sessions for uninstalled app: ${shop}`);
    } else {
      console.log(`⚠️ No session found for shop ${shop} - app may have been already uninstalled`);
    }
    console.log(`✅ App uninstalled webhook processed successfully for ${shop}`);
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("❌ App uninstalled webhook error:", error);
    return new Response("Error processing webhook", { status: 500 });
  }
};
const route4 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$8
}, Symbol.toStringTag, { value: "Module" }));
const loader$g = async ({ request }) => {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get("type");
    const shop = url.searchParams.get("shop");
    console.log(`🎉 [BILLING-SUCCESS] Callback received - Type: ${type}, Shop: ${shop}`);
    console.log(`🔗 [BILLING-SUCCESS] Full URL: ${request.url}`);
    if (!type || !shop) {
      console.error("❌ [BILLING-SUCCESS] Missing type or shop parameter");
      return redirect("/app/billing?error=missing_params");
    }
    const successType = type === "subscription" ? "subscription_created" : "purchase_created";
    const redirectUrl = `https://${shop}/admin/apps/AI BULK SEO/app/billing?success=${successType}&embedded=1`;
    console.log(`✅ [BILLING-SUCCESS] Redirecting to Shopify admin: ${redirectUrl}`);
    return redirect(redirectUrl);
  } catch (error) {
    console.error("❌ [BILLING-SUCCESS] Error:", error);
    return redirect("/app/billing?error=callback_failed");
  }
};
function BillingSuccess() {
  return /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("h1", { children: "Processing payment..." }),
    /* @__PURE__ */ jsx("p", { children: "Please wait while we redirect you back to the app." })
  ] });
}
const route5 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: BillingSuccess,
  loader: loader$g
}, Symbol.toStringTag, { value: "Module" }));
const Polaris = /* @__PURE__ */ JSON.parse('{"ActionMenu":{"Actions":{"moreActions":"More actions"},"RollupActions":{"rollupButton":"View actions"}},"ActionList":{"SearchField":{"clearButtonLabel":"Clear","search":"Search","placeholder":"Search actions"}},"Avatar":{"label":"Avatar","labelWithInitials":"Avatar with initials {initials}"},"Autocomplete":{"spinnerAccessibilityLabel":"Loading","ellipsis":"{content}…"},"Badge":{"PROGRESS_LABELS":{"incomplete":"Incomplete","partiallyComplete":"Partially complete","complete":"Complete"},"TONE_LABELS":{"info":"Info","success":"Success","warning":"Warning","critical":"Critical","attention":"Attention","new":"New","readOnly":"Read-only","enabled":"Enabled"},"progressAndTone":"{toneLabel} {progressLabel}"},"Banner":{"dismissButton":"Dismiss notification"},"Button":{"spinnerAccessibilityLabel":"Loading"},"Common":{"checkbox":"checkbox","undo":"Undo","cancel":"Cancel","clear":"Clear","close":"Close","submit":"Submit","more":"More"},"ContextualSaveBar":{"save":"Save","discard":"Discard"},"DataTable":{"sortAccessibilityLabel":"sort {direction} by","navAccessibilityLabel":"Scroll table {direction} one column","totalsRowHeading":"Totals","totalRowHeading":"Total"},"DatePicker":{"previousMonth":"Show previous month, {previousMonthName} {showPreviousYear}","nextMonth":"Show next month, {nextMonth} {nextYear}","today":"Today ","start":"Start of range","end":"End of range","months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"days":{"monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday","sunday":"Sunday"},"daysAbbreviated":{"monday":"Mo","tuesday":"Tu","wednesday":"We","thursday":"Th","friday":"Fr","saturday":"Sa","sunday":"Su"}},"DiscardConfirmationModal":{"title":"Discard all unsaved changes","message":"If you discard changes, you’ll delete any edits you made since you last saved.","primaryAction":"Discard changes","secondaryAction":"Continue editing"},"DropZone":{"single":{"overlayTextFile":"Drop file to upload","overlayTextImage":"Drop image to upload","overlayTextVideo":"Drop video to upload","actionTitleFile":"Add file","actionTitleImage":"Add image","actionTitleVideo":"Add video","actionHintFile":"or drop file to upload","actionHintImage":"or drop image to upload","actionHintVideo":"or drop video to upload","labelFile":"Upload file","labelImage":"Upload image","labelVideo":"Upload video"},"allowMultiple":{"overlayTextFile":"Drop files to upload","overlayTextImage":"Drop images to upload","overlayTextVideo":"Drop videos to upload","actionTitleFile":"Add files","actionTitleImage":"Add images","actionTitleVideo":"Add videos","actionHintFile":"or drop files to upload","actionHintImage":"or drop images to upload","actionHintVideo":"or drop videos to upload","labelFile":"Upload files","labelImage":"Upload images","labelVideo":"Upload videos"},"errorOverlayTextFile":"File type is not valid","errorOverlayTextImage":"Image type is not valid","errorOverlayTextVideo":"Video type is not valid"},"EmptySearchResult":{"altText":"Empty search results"},"Frame":{"skipToContent":"Skip to content","navigationLabel":"Navigation","Navigation":{"closeMobileNavigationLabel":"Close navigation"}},"FullscreenBar":{"back":"Back","accessibilityLabel":"Exit fullscreen mode"},"Filters":{"moreFilters":"More filters","moreFiltersWithCount":"More filters ({count})","filter":"Filter {resourceName}","noFiltersApplied":"No filters applied","cancel":"Cancel","done":"Done","clearAllFilters":"Clear all filters","clear":"Clear","clearLabel":"Clear {filterName}","addFilter":"Add filter","clearFilters":"Clear all","searchInView":"in:{viewName}"},"FilterPill":{"clear":"Clear","unsavedChanges":"Unsaved changes - {label}"},"IndexFilters":{"searchFilterTooltip":"Search and filter","searchFilterTooltipWithShortcut":"Search and filter (F)","searchFilterAccessibilityLabel":"Search and filter results","sort":"Sort your results","addView":"Add a new view","newView":"Custom search","SortButton":{"ariaLabel":"Sort the results","tooltip":"Sort","title":"Sort by","sorting":{"asc":"Ascending","desc":"Descending","az":"A-Z","za":"Z-A"}},"EditColumnsButton":{"tooltip":"Edit columns","accessibilityLabel":"Customize table column order and visibility"},"UpdateButtons":{"cancel":"Cancel","update":"Update","save":"Save","saveAs":"Save as","modal":{"title":"Save view as","label":"Name","sameName":"A view with this name already exists. Please choose a different name.","save":"Save","cancel":"Cancel"}}},"IndexProvider":{"defaultItemSingular":"Item","defaultItemPlural":"Items","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} are selected","selected":"{selectedItemsCount} selected","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}"},"IndexTable":{"emptySearchTitle":"No {resourceNamePlural} found","emptySearchDescription":"Try changing the filters or search term","onboardingBadgeText":"New","resourceLoadingAccessibilityLabel":"Loading {resourceNamePlural}…","selectAllLabel":"Select all {resourceNamePlural}","selected":"{selectedItemsCount} selected","undo":"Undo","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural}","selectItem":"Select {resourceName}","selectButtonText":"Select","sortAccessibilityLabel":"sort {direction} by"},"Loading":{"label":"Page loading bar"},"Modal":{"iFrameTitle":"body markup","modalWarning":"These required properties are missing from Modal: {missingProps}"},"Page":{"Header":{"rollupActionsLabel":"View actions for {title}","pageReadyAccessibilityLabel":"{title}. This page is ready"}},"Pagination":{"previous":"Previous","next":"Next","pagination":"Pagination"},"ProgressBar":{"negativeWarningMessage":"Values passed to the progress prop shouldn’t be negative. Resetting {progress} to 0.","exceedWarningMessage":"Values passed to the progress prop shouldn’t exceed 100. Setting {progress} to 100."},"ResourceList":{"sortingLabel":"Sort by","defaultItemSingular":"item","defaultItemPlural":"items","showing":"Showing {itemsCount} {resource}","showingTotalCount":"Showing {itemsCount} of {totalItemsCount} {resource}","loading":"Loading {resource}","selected":"{selectedItemsCount} selected","allItemsSelected":"All {itemsLength}+ {resourceNamePlural} in your store are selected","allFilteredItemsSelected":"All {itemsLength}+ {resourceNamePlural} in this filter are selected","selectAllItems":"Select all {itemsLength}+ {resourceNamePlural} in your store","selectAllFilteredItems":"Select all {itemsLength}+ {resourceNamePlural} in this filter","emptySearchResultTitle":"No {resourceNamePlural} found","emptySearchResultDescription":"Try changing the filters or search term","selectButtonText":"Select","a11yCheckboxDeselectAllSingle":"Deselect {resourceNameSingular}","a11yCheckboxSelectAllSingle":"Select {resourceNameSingular}","a11yCheckboxDeselectAllMultiple":"Deselect all {itemsLength} {resourceNamePlural}","a11yCheckboxSelectAllMultiple":"Select all {itemsLength} {resourceNamePlural}","Item":{"actionsDropdownLabel":"Actions for {accessibilityLabel}","actionsDropdown":"Actions dropdown","viewItem":"View details for {itemName}"},"BulkActions":{"actionsActivatorLabel":"Actions","moreActionsActivatorLabel":"More actions"}},"SkeletonPage":{"loadingLabel":"Page loading"},"Tabs":{"newViewAccessibilityLabel":"Create new view","newViewTooltip":"Create view","toggleTabsLabel":"More views","Tab":{"rename":"Rename view","duplicate":"Duplicate view","edit":"Edit view","editColumns":"Edit columns","delete":"Delete view","copy":"Copy of {name}","deleteModal":{"title":"Delete view?","description":"This can’t be undone. {viewName} view will no longer be available in your admin.","cancel":"Cancel","delete":"Delete view"}},"RenameModal":{"title":"Rename view","label":"Name","cancel":"Cancel","create":"Save","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"DuplicateModal":{"title":"Duplicate view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}},"CreateViewModal":{"title":"Create new view","label":"Name","cancel":"Cancel","create":"Create view","errors":{"sameName":"A view with this name already exists. Please choose a different name."}}},"Tag":{"ariaLabel":"Remove {children}"},"TextField":{"characterCount":"{count} characters","characterCountWithMaxLength":"{count} of {limit} characters used"},"TooltipOverlay":{"accessibilityLabel":"Tooltip: {label}"},"TopBar":{"toggleMenuLabel":"Toggle menu","SearchField":{"clearButtonLabel":"Clear","search":"Search"}},"MediaCard":{"dismissButton":"Dismiss","popoverButton":"Actions"},"VideoThumbnail":{"playButtonA11yLabel":{"default":"Play video","defaultWithDuration":"Play video of length {duration}","duration":{"hours":{"other":{"only":"{hourCount} hours","andMinutes":"{hourCount} hours and {minuteCount} minutes","andMinute":"{hourCount} hours and {minuteCount} minute","minutesAndSeconds":"{hourCount} hours, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hours, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hours, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hours, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hours and {secondCount} seconds","andSecond":"{hourCount} hours and {secondCount} second"},"one":{"only":"{hourCount} hour","andMinutes":"{hourCount} hour and {minuteCount} minutes","andMinute":"{hourCount} hour and {minuteCount} minute","minutesAndSeconds":"{hourCount} hour, {minuteCount} minutes, and {secondCount} seconds","minutesAndSecond":"{hourCount} hour, {minuteCount} minutes, and {secondCount} second","minuteAndSeconds":"{hourCount} hour, {minuteCount} minute, and {secondCount} seconds","minuteAndSecond":"{hourCount} hour, {minuteCount} minute, and {secondCount} second","andSeconds":"{hourCount} hour and {secondCount} seconds","andSecond":"{hourCount} hour and {secondCount} second"}},"minutes":{"other":{"only":"{minuteCount} minutes","andSeconds":"{minuteCount} minutes and {secondCount} seconds","andSecond":"{minuteCount} minutes and {secondCount} second"},"one":{"only":"{minuteCount} minute","andSeconds":"{minuteCount} minute and {secondCount} seconds","andSecond":"{minuteCount} minute and {secondCount} second"}},"seconds":{"other":"{secondCount} seconds","one":"{secondCount} second"}}}}}');
const polarisTranslations = {
  Polaris
};
const polarisStyles = "/assets/styles-DVPoaA-_.css";
function loginErrorMessage(loginErrors) {
  if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.MissingShop) {
    return { shop: "Please enter your shop domain to log in" };
  } else if ((loginErrors == null ? void 0 : loginErrors.shop) === LoginErrorType.InvalidShop) {
    return { shop: "Please enter a valid shop domain to log in" };
  }
  return {};
}
const links$1 = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$f = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return { errors, polarisTranslations };
};
const action$7 = async ({ request }) => {
  const errors = loginErrorMessage(await login(request));
  return {
    errors
  };
};
function Auth() {
  const loaderData = useLoaderData();
  const actionData = useActionData();
  const [shop, setShop] = useState("");
  const { errors } = actionData || loaderData;
  return /* @__PURE__ */ jsx(AppProvider, { i18n: loaderData.polarisTranslations, children: /* @__PURE__ */ jsx(Page, { children: /* @__PURE__ */ jsx(Card$1, { children: /* @__PURE__ */ jsx(Form, { method: "post", children: /* @__PURE__ */ jsxs(FormLayout, { children: [
    /* @__PURE__ */ jsx(Text, { variant: "headingMd", as: "h2", children: "Log in" }),
    /* @__PURE__ */ jsx(
      TextField,
      {
        type: "text",
        name: "shop",
        label: "Shop domain",
        helpText: "example.myshopify.com",
        value: shop,
        onChange: setShop,
        autoComplete: "on",
        error: errors.shop
      }
    ),
    /* @__PURE__ */ jsx(Button$1, { submit: true, children: "Log in" })
  ] }) }) }) }) });
}
const route6 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$7,
  default: Auth,
  links: links$1,
  loader: loader$f
}, Symbol.toStringTag, { value: "Module" }));
const loader$e = async ({ request }) => {
  await authenticate.admin(request);
  return new Response(
    `<!DOCTYPE html>
    <html>
      <head>
        <script src="https://unpkg.com/@shopify/app-bridge@3"><\/script>
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            if (window.top !== window.self) {
              window.parent.location.href = window.location.href;
            }
          });
        <\/script>
      </head>
      <body>
        <p>Redirecting...</p>
      </body>
    </html>`,
    {
      status: 200,
      headers: {
        "Content-Type": "text/html"
      }
    }
  );
};
const route7 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$e
}, Symbol.toStringTag, { value: "Module" }));
async function bulletproofAuthenticate(request) {
  return await authenticate.admin(request);
}
function withBulletproofAuth(loaderFn) {
  return async (args) => {
    const auth = await bulletproofAuthenticate(args.request);
    return await loaderFn({ ...args, auth });
  };
}
function withBulletproofAction(actionFn) {
  return async (args) => {
    const auth = await bulletproofAuthenticate(args.request);
    return await actionFn({ ...args, auth });
  };
}
function requireAuth(handlerFn) {
  return async (args) => {
    await bulletproofAuthenticate(args.request);
    return await handlerFn(args);
  };
}
const loader$d = requireAuth(async () => {
  return null;
});
const route8 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$d
}, Symbol.toStringTag, { value: "Module" }));
const loader$c = async ({ request }) => {
  const url = new URL(request.url);
  if (url.searchParams.get("shop")) {
    throw redirect(`/app?${url.searchParams.toString()}`);
  }
  return { showForm: Boolean(login) };
};
function App$1() {
  const { showForm } = useLoaderData();
  return /* @__PURE__ */ jsx(
    "div",
    {
      style: {
        backgroundColor: "#000000",
        // Pure black background
        color: "#ffffff",
        // Pure white text
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
        minHeight: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: "2rem"
      },
      children: /* @__PURE__ */ jsxs(
        "div",
        {
          style: {
            maxWidth: "640px",
            width: "100%",
            textAlign: "center",
            display: "flex",
            flexDirection: "column",
            gap: "1rem"
          },
          children: [
            /* @__PURE__ */ jsxs(
              "h1",
              {
                style: {
                  fontSize: "3rem",
                  fontWeight: "800",
                  color: "#ffffff",
                  // Pure white for heading
                  letterSpacing: "-0.025em",
                  lineHeight: "1.1",
                  margin: "0",
                  display: "flex",
                  // Enable flexbox for centering children
                  flexDirection: "column",
                  // Stack children vertically
                  alignItems: "center",
                  // Center horizontally
                  justifyContent: "center"
                  // Center vertically (if needed for the h1 itself)
                },
                children: [
                  /* @__PURE__ */ jsx(
                    "img",
                    {
                      src: "/logo.png",
                      alt: "AI BULK SEO Logo",
                      className: "w-16 h-16 mb-6 rounded-2xl shadow-2xl",
                      style: { filter: "brightness(1.1) contrast(1.1)" }
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    "div",
                    {
                      className: "text-sm font-semibold tracking-widest uppercase mb-4",
                      style: { color: "#ffffff" },
                      children: "AI BULK SEO"
                    }
                  ),
                  "Automate Your Shopify SEO with AI BULK SEO 🚀"
                ]
              }
            ),
            /* @__PURE__ */ jsx(
              "p",
              {
                style: {
                  fontSize: "1.125rem",
                  color: "#e0e0e0",
                  // Slightly off-white for body text for readability
                  lineHeight: "1.75",
                  maxWidth: "560px",
                  margin: "0 auto 1.5rem auto"
                },
                children: "Effortlessly optimize your entire product catalog for search engines in minutes, not months. Let our AI handle the heavy lifting."
              }
            ),
            showForm && /* @__PURE__ */ jsxs(
              Form,
              {
                style: {
                  display: "flex",
                  flexDirection: "column",
                  gap: "1rem",
                  width: "100%",
                  maxWidth: "384px",
                  margin: "0 auto"
                },
                method: "post",
                action: "/auth/login",
                children: [
                  /* @__PURE__ */ jsxs(
                    "label",
                    {
                      style: {
                        display: "flex",
                        flexDirection: "column",
                        textAlign: "left",
                        gap: "0.5rem"
                      },
                      children: [
                        /* @__PURE__ */ jsx(
                          "span",
                          {
                            style: {
                              fontSize: "0.875rem",
                              fontWeight: "500",
                              color: "#ffffff"
                              // Pure white for label
                            },
                            children: "Shop Domain"
                          }
                        ),
                        /* @__PURE__ */ jsx(
                          "input",
                          {
                            style: {
                              backgroundColor: "#000000",
                              // Pure black input background
                              border: "1px solid #404040",
                              // Dark grey border for subtle definition
                              color: "#ffffff",
                              // Pure white input text
                              borderRadius: "0.5rem",
                              padding: "0.75rem 1rem",
                              fontSize: "1rem",
                              outline: "none"
                            },
                            type: "text",
                            name: "shop",
                            placeholder: "your-store-name.myshopify.com"
                          }
                        )
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    "button",
                    {
                      style: {
                        backgroundColor: "#ffffff",
                        // Pure white button background
                        color: "#000000",
                        // Pure black button text
                        fontWeight: "600",
                        padding: "0.75rem 1rem",
                        borderRadius: "0.5rem",
                        border: "none",
                        cursor: "pointer",
                        fontSize: "1rem"
                      },
                      type: "submit",
                      children: "Install & Log In"
                    }
                  )
                ]
              }
            ),
            /* @__PURE__ */ jsxs(
              "ul",
              {
                style: {
                  listStyle: "none",
                  padding: "0",
                  marginTop: "3rem",
                  display: "grid",
                  gap: "1rem",
                  textAlign: "left",
                  width: "100%"
                },
                children: [
                  /* @__PURE__ */ jsxs(
                    "li",
                    {
                      style: {
                        backgroundColor: "#1a1a1a",
                        // Dark grey for card background, close to black but with some differentiation
                        padding: "1.5rem",
                        borderRadius: "0.75rem",
                        border: "1px solid #404040"
                        // Dark grey border for definition
                      },
                      children: [
                        /* @__PURE__ */ jsx(
                          "strong",
                          {
                            style: {
                              color: "#ffffff",
                              // Pure white for strong text
                              display: "block",
                              marginBottom: "0.25rem",
                              fontWeight: "600",
                              fontSize: "1rem"
                            },
                            children: "🤖 AI-Powered Bulk Optimization"
                          }
                        ),
                        /* @__PURE__ */ jsx("span", { style: { color: "#e0e0e0", fontSize: "0.875rem" }, children: "Revamp thousands of product titles, descriptions, and meta tags in a single click. Our AI analyzes your products and generates SEO-friendly content that converts." })
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsxs(
                    "li",
                    {
                      style: {
                        backgroundColor: "#1a1a1a",
                        // Dark grey for card background
                        padding: "1.5rem",
                        borderRadius: "0.75rem",
                        border: "1px solid #404040"
                        // Dark grey border for definition
                      },
                      children: [
                        /* @__PURE__ */ jsx(
                          "strong",
                          {
                            style: {
                              color: "#ffffff",
                              // Pure white for strong text
                              display: "block",
                              marginBottom: "0.25rem",
                              fontWeight: "600",
                              fontSize: "1rem"
                            },
                            children: "🔑 Smart Keyword Suggestions"
                          }
                        ),
                        /* @__PURE__ */ jsx("span", { style: { color: "#e0e0e0", fontSize: "0.875rem" }, children: "Discover high-intent, low-competition keywords tailored to each product. Stop guessing and start ranking for terms your customers are actually searching for." })
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsxs(
                    "li",
                    {
                      style: {
                        backgroundColor: "#1a1a1a",
                        // Dark grey for card background
                        padding: "1.5rem",
                        borderRadius: "0.75rem",
                        border: "1px solid #404040"
                        // Dark grey border for definition
                      },
                      children: [
                        /* @__PURE__ */ jsx(
                          "strong",
                          {
                            style: {
                              color: "#ffffff",
                              // Pure white for strong text
                              display: "block",
                              marginBottom: "0.25rem",
                              fontWeight: "600",
                              fontSize: "1rem"
                            },
                            children: "📈 Real-time Performance Tracking"
                          }
                        ),
                        /* @__PURE__ */ jsx("span", { style: { color: "#e0e0e0", fontSize: "0.875rem" }, children: "Monitor your SEO progress with a clear, intuitive dashboard. Watch your products climb the search rankings and see the direct impact of your optimizations." })
                      ]
                    }
                  )
                ]
              }
            )
          ]
        }
      )
    }
  );
}
const route9 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: App$1,
  loader: loader$c
}, Symbol.toStringTag, { value: "Module" }));
const InstantNavigationContext = React.createContext(null);
function InstantNavigationProvider({ children }) {
  const navigate = useNavigate();
  const location = useLocation();
  const [isNavigating, setIsNavigating] = React.useState(false);
  const [preloadedRoutes, setPreloadedRoutes] = React.useState(/* @__PURE__ */ new Set());
  const preloadRoute = React.useCallback((path) => {
    if (preloadedRoutes.has(path)) return;
    const link = document.createElement("link");
    link.rel = "prefetch";
    link.href = path;
    document.head.appendChild(link);
    setPreloadedRoutes((prev) => /* @__PURE__ */ new Set([...prev, path]));
    setTimeout(() => {
      document.head.removeChild(link);
    }, 3e4);
  }, [preloadedRoutes]);
  const instantNavigate = React.useCallback((path) => {
    if (location.pathname === path) return;
    setIsNavigating(true);
    try {
      requestAnimationFrame(() => {
        navigate(path);
        setTimeout(() => setIsNavigating(false), 100);
      });
    } catch (error) {
      console.error("Navigation error:", error);
      navigate(path);
      setIsNavigating(false);
    }
  }, [navigate, location.pathname]);
  React.useEffect(() => {
    const commonRoutes = [
      "/app",
      "/app/seo-dashboard",
      "/app/settings"
    ];
    commonRoutes.forEach((route) => {
      if (route !== location.pathname) {
        setTimeout(() => preloadRoute(route), 1e3);
      }
    });
  }, [location.pathname, preloadRoute]);
  const value = React.useMemo(() => ({
    navigate: instantNavigate,
    isNavigating,
    preloadRoute
  }), [instantNavigate, isNavigating, preloadRoute]);
  return /* @__PURE__ */ jsxs(InstantNavigationContext.Provider, { value, children: [
    /* @__PURE__ */ jsx(
      motion.div,
      {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.2, ease: "easeInOut" },
        children
      },
      location.pathname
    ),
    /* @__PURE__ */ jsx(AnimatePresence, { children: isNavigating && /* @__PURE__ */ jsx(
      motion.div,
      {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.1 },
        className: "fixed inset-0 bg-black/20 backdrop-blur-sm z-50 pointer-events-none",
        children: /* @__PURE__ */ jsx("div", { className: "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2", children: /* @__PURE__ */ jsx("div", { className: "w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin" }) })
      }
    ) })
  ] });
}
function convertToNumber(value) {
  if (value === null || value === void 0) return 0;
  if (typeof value === "bigint") return Number(value);
  if (typeof value === "number") return value;
  if (typeof value === "string") return parseFloat(value) || 0;
  return 0;
}
function convertToDate(value) {
  if (value === null || value === void 0) return /* @__PURE__ */ new Date();
  if (value instanceof Date) return value;
  if (typeof value === "string") return new Date(value);
  return /* @__PURE__ */ new Date();
}
class EnhancedRateLimiter {
  constructor() {
    __publicField(this, "inMemoryStore", /* @__PURE__ */ new Map());
  }
  /**
   * Check if request is within rate limit
   */
  async checkLimit(config) {
    const now = Date.now();
    const resetTime = now + config.windowMs;
    try {
      if (process.env.NODE_ENV === "production") {
        return await this.checkDatabaseLimit(config, now, resetTime);
      } else {
        return this.checkInMemoryLimit(config, now, resetTime);
      }
    } catch (error) {
      console.error("❌ Rate limiting error, falling back to in-memory:", error);
      return this.checkInMemoryLimit(config, now, resetTime);
    }
  }
  /**
   * Database-based rate limiting for production
   */
  async checkDatabaseLimit(config, now, resetTime) {
    const existing = await db.rateLimitEntry.findUnique({
      where: { identifier: config.identifier }
    });
    if (existing) {
      const resetTime2 = convertToDate(existing.resetTime);
      const existingResetTime = resetTime2.getTime();
      const count = convertToNumber(existing.count);
      if (now < existingResetTime) {
        if (count >= config.maxRequests) {
          return {
            allowed: false,
            remaining: 0,
            resetTime: resetTime2,
            totalHits: count
          };
        }
        const updated = await db.rateLimitEntry.update({
          where: { identifier: config.identifier },
          data: { count: count + 1 }
        });
        return {
          allowed: true,
          remaining: config.maxRequests - convertToNumber(updated.count),
          resetTime: convertToDate(updated.resetTime),
          totalHits: convertToNumber(updated.count)
        };
      } else {
        const updated = await db.rateLimitEntry.update({
          where: { identifier: config.identifier },
          data: {
            count: 1,
            resetTime: new Date(resetTime2)
          }
        });
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: convertToDate(updated.resetTime),
          totalHits: 1
        };
      }
    } else {
      const created = await db.rateLimitEntry.create({
        data: {
          identifier: config.identifier,
          count: 1,
          resetTime: new Date(resetTime)
        }
      });
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: created.resetTime,
        totalHits: 1
      };
    }
  }
  /**
   * In-memory rate limiting for development
   */
  checkInMemoryLimit(config, now, resetTime) {
    const entry2 = this.inMemoryStore.get(config.identifier);
    if (entry2) {
      if (now < entry2.resetTime) {
        if (entry2.count >= config.maxRequests) {
          return {
            allowed: false,
            remaining: 0,
            resetTime: new Date(entry2.resetTime),
            totalHits: entry2.count
          };
        }
        entry2.count++;
        return {
          allowed: true,
          remaining: config.maxRequests - entry2.count,
          resetTime: new Date(entry2.resetTime),
          totalHits: entry2.count
        };
      } else {
        this.inMemoryStore.set(config.identifier, { count: 1, resetTime });
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: new Date(resetTime),
          totalHits: 1
        };
      }
    } else {
      this.inMemoryStore.set(config.identifier, { count: 1, resetTime });
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: new Date(resetTime),
        totalHits: 1
      };
    }
  }
  /**
   * Clean up expired entries
   */
  async cleanup() {
    try {
      if (process.env.NODE_ENV === "production") {
        const deleted = await db.rateLimitEntry.deleteMany({
          where: {
            resetTime: {
              lt: /* @__PURE__ */ new Date()
            }
          }
        });
        if (deleted.count > 0) {
          console.log(`🧹 Cleaned up ${deleted.count} expired rate limit entries`);
        }
      } else {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, data] of this.inMemoryStore.entries()) {
          if (now > data.resetTime) {
            this.inMemoryStore.delete(key);
            cleaned++;
          }
        }
        if (cleaned > 0) {
          console.log(`🧹 Cleaned up ${cleaned} expired in-memory rate limit entries`);
        }
      }
    } catch (error) {
      console.error("❌ Error cleaning up expired rate limits:", error);
    }
  }
}
const globalRateLimiter = new EnhancedRateLimiter();
async function applyRateLimit(config, context) {
  const result = await globalRateLimiter.checkLimit(config);
  if (!result.allowed) {
    const retryAfter = Math.ceil((result.resetTime.getTime() - Date.now()) / 1e3);
    throw createError("RATE_LIMIT_EXCEEDED", {
      ...context,
      metadata: {
        identifier: config.identifier,
        retryAfter
      }
    });
  }
}
const RATE_LIMITERS = {
  // Authentication attempts
  AUTH_LOGIN: (ip) => ({
    identifier: `auth:login:${ip}`,
    windowMs: 15 * 60 * 1e3,
    // 15 minutes
    maxRequests: 5
  }),
  // CSRF token generation
  CSRF_GENERATION: (shop) => ({
    identifier: `csrf:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 20
  }),
  // Billing operations
  BILLING_OPERATIONS: (shop) => ({
    identifier: `billing:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 10
  }),
  // SEO dashboard loading (more permissive)
  SEO_DASHBOARD: (shop) => ({
    identifier: `seo_dashboard:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 20
    // Allow more dashboard loads
  }),
  // SEO optimization requests (stricter)
  SEO_OPTIMIZATION: (shop) => ({
    identifier: `seo_opt:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 10
    // Allow more optimizations
  }),
  // SEO progress polling (very permissive)
  SEO_PROGRESS: (shop) => ({
    identifier: `seo_progress:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 50
    // Allow frequent polling
  }),
  // General API requests
  API_GENERAL: (shop) => ({
    identifier: `api:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 100
  }),
  // API Billing requests
  API_BILLING: (shop) => ({
    identifier: `api_billing:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 10
  }),
  // Webhook processing
  WEBHOOK_PROCESSING: (shop) => ({
    identifier: `webhook:${shop}`,
    windowMs: 60 * 1e3,
    // 1 minute
    maxRequests: 50
  }),
  // Additional rate limiters for services
  AUTH_ATTEMPTS: (ip) => ({
    identifier: `auth_attempts:${ip}`,
    windowMs: 15 * 60 * 1e3,
    // 15 minutes
    maxRequests: 5
  })
};
if (typeof setInterval !== "undefined") {
  setInterval(() => {
    globalRateLimiter.cleanup();
  }, 5 * 60 * 1e3);
}
const _CSRFService = class _CSRFService {
  constructor() {
    __publicField(this, "inMemoryTokens", /* @__PURE__ */ new Map());
  }
  static getInstance() {
    if (!_CSRFService.instance) {
      _CSRFService.instance = new _CSRFService();
    }
    return _CSRFService.instance;
  }
  /**
   * Generate a secure CSRF token for a specific shop
   */
  async generateToken(shop, context) {
    try {
      await applyRateLimit(RATE_LIMITERS.CSRF_GENERATION(shop), context);
      const timestamp = Date.now();
      const nonce = randomBytes(16).toString("hex");
      const randomData = randomBytes(32).toString("hex");
      const secret = process.env.SESSION_SECRET || "fallback-secret";
      const tokenData = {
        token: randomData,
        timestamp,
        shop,
        nonce
      };
      const payload = JSON.stringify(tokenData);
      const signature = createHash("sha256").update(payload + secret + nonce).digest("hex");
      const csrfToken = Buffer.from(payload).toString("base64") + "." + signature;
      const expires = new Date(timestamp + 60 * 60 * 1e3);
      try {
        await db.cSRFToken.create({
          data: {
            token: csrfToken,
            shop,
            expires
          }
        });
      } catch (dbError) {
        console.warn("⚠️ Failed to store CSRF token in database, using memory fallback");
        this.inMemoryTokens.set(csrfToken, { shop, expires: expires.getTime() });
      }
      console.log(`🔐 Generated CSRF token for shop: ${shop}`);
      return csrfToken;
    } catch (error) {
      console.error("❌ CSRF token generation failed:", error);
      throw createError("INTERNAL_SERVER_ERROR", {
        ...context,
        shop,
        action: "generate_csrf_token"
      });
    }
  }
  /**
   * Verify a CSRF token
   */
  async verifyToken(token, shop, context) {
    try {
      if (!token || typeof token !== "string") {
        return { isValid: false, error: "Invalid token format" };
      }
      const parts = token.split(".");
      if (parts.length !== 2) {
        return { isValid: false, error: "Invalid token structure" };
      }
      const [payloadBase64, signature] = parts;
      let tokenData;
      try {
        const payload2 = Buffer.from(payloadBase64, "base64").toString("utf8");
        tokenData = JSON.parse(payload2);
      } catch {
        return { isValid: false, error: "Invalid payload" };
      }
      if (tokenData.shop !== shop) {
        return { isValid: false, error: "Shop mismatch" };
      }
      let tokenExists = false;
      try {
        const dbToken = await db.cSRFToken.findUnique({
          where: { token }
        });
        if (dbToken) {
          if (/* @__PURE__ */ new Date() > dbToken.expires) {
            await db.cSRFToken.delete({ where: { token } });
            return { isValid: false, error: "Token expired" };
          }
          tokenExists = true;
          await db.cSRFToken.delete({ where: { token } });
        }
      } catch (dbError) {
        const memoryToken = this.inMemoryTokens.get(token);
        if (memoryToken) {
          if (Date.now() > memoryToken.expires) {
            this.inMemoryTokens.delete(token);
            return { isValid: false, error: "Token expired" };
          }
          if (memoryToken.shop === shop) {
            tokenExists = true;
            this.inMemoryTokens.delete(token);
          }
        }
      }
      if (!tokenExists) {
        return { isValid: false, error: "Token not found or already used" };
      }
      const secret = process.env.SESSION_SECRET || "fallback-secret";
      const payload = Buffer.from(payloadBase64, "base64").toString("utf8");
      const expectedSignature = createHash("sha256").update(payload + secret + tokenData.nonce).digest("hex");
      if (signature !== expectedSignature) {
        return { isValid: false, error: "Invalid signature" };
      }
      console.log(`✅ CSRF token verified for shop: ${shop}`);
      return { isValid: true };
    } catch (error) {
      console.error("❌ CSRF verification error:", error);
      return { isValid: false, error: "Verification failed" };
    }
  }
  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens() {
    try {
      const deleted = await db.cSRFToken.deleteMany({
        where: {
          expires: {
            lt: /* @__PURE__ */ new Date()
          }
        }
      });
      if (deleted.count > 0) {
        console.log(`🧹 Cleaned up ${deleted.count} expired CSRF tokens from database`);
      }
      const now = Date.now();
      let cleaned = 0;
      for (const [token, data] of this.inMemoryTokens.entries()) {
        if (now > data.expires) {
          this.inMemoryTokens.delete(token);
          cleaned++;
        }
      }
      if (cleaned > 0) {
        console.log(`🧹 Cleaned up ${cleaned} expired CSRF tokens from memory`);
      }
    } catch (error) {
      console.error("❌ Error cleaning up expired CSRF tokens:", error);
    }
  }
  /**
   * Add CSRF token to loader data
   */
  async addTokenToData(shop, data, context) {
    const csrfToken = await this.generateToken(shop, context);
    return {
      ...data,
      csrfToken
    };
  }
  /**
   * Validate CSRF token from form data
   */
  async validateFromForm(formData, shop, context) {
    var _a2;
    const token = (_a2 = formData.get("csrfToken")) == null ? void 0 : _a2.toString();
    console.log("🔐 CSRF validation - Shop:", shop);
    console.log("🔐 CSRF validation - Token present:", !!token);
    if (!token) {
      console.error("❌ CSRF validation failed: No token provided");
      return false;
    }
    const result = await this.verifyToken(token, shop, context);
    console.log("🔐 CSRF validation result:", result.isValid);
    return result.isValid;
  }
};
__publicField(_CSRFService, "instance");
let CSRFService = _CSRFService;
const csrfService = CSRFService.getInstance();
if (typeof setInterval !== "undefined") {
  setInterval(() => {
    csrfService.cleanupExpiredTokens();
  }, 60 * 60 * 1e3);
}
async function generateCSRFToken(shop) {
  return await csrfService.generateToken(shop);
}
async function addCSRFToken(shop, data) {
  return await csrfService.addTokenToData(shop, data);
}
async function validateCSRFFromForm(formData, shop) {
  return await csrfService.validateFromForm(formData, shop);
}
const links = () => [{ rel: "stylesheet", href: polarisStyles }];
const loader$b = withBulletproofAuth(async ({ auth }) => {
  const { admin, session } = auth;
  console.log(`🔐 [APP-LAYOUT] Loading app layout for shop: ${session.shop}`);
  const csrfToken = generateCSRFToken(session.shop);
  console.log(`✅ [APP-LAYOUT] Session validated for shop: ${session.shop}`);
  return {
    apiKey: process.env.SHOPIFY_API_KEY || "",
    shop: session.shop,
    csrfToken,
    sessionValid: true
  };
});
function App() {
  const { apiKey } = useLoaderData();
  return /* @__PURE__ */ jsxs(AppProvider$1, { isEmbeddedApp: true, apiKey, children: [
    /* @__PURE__ */ jsxs(NavMenu, { children: [
      /* @__PURE__ */ jsx(Link, { to: "/app", rel: "home", children: "AI BULK SEO Dashboard" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/seo-dashboard", children: "SEO Optimizer" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/billing", children: "Billing" }),
      /* @__PURE__ */ jsx(Link, { to: "/app/settings", children: "Settings" })
    ] }),
    /* @__PURE__ */ jsx(InstantNavigationProvider, { children: /* @__PURE__ */ jsx(Outlet, {}) })
  ] });
}
function ErrorBoundary() {
  const error = useRouteError();
  console.error("App route error:", error);
  return /* @__PURE__ */ jsx("div", { className: "min-h-screen flex items-center justify-center bg-gray-50", children: /* @__PURE__ */ jsxs("div", { className: "max-w-md w-full bg-white rounded-lg shadow-lg p-6", children: [
    /* @__PURE__ */ jsx("h1", { className: "text-xl font-bold text-red-600 mb-4", children: "Something went wrong" }),
    /* @__PURE__ */ jsx("p", { className: "text-gray-600 mb-4", children: "We encountered an error while loading the application. Please try refreshing the page." }),
    /* @__PURE__ */ jsx(
      "button",
      {
        onClick: () => window.location.reload(),
        className: "w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700",
        children: "Refresh Page"
      }
    )
  ] }) });
}
const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
const route10 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  ErrorBoundary,
  default: App,
  headers,
  links,
  loader: loader$b
}, Symbol.toStringTag, { value: "Module" }));
class EnvironmentValidationError extends Error {
  constructor(message) {
    super(message);
    this.name = "EnvironmentValidationError";
  }
}
function validateEnvironment() {
  const requiredVars = [
    "SHOPIFY_API_KEY",
    "SHOPIFY_API_SECRET",
    "SHOPIFY_APP_URL",
    "SCOPES",
    "DATABASE_URL",
    "SESSION_SECRET"
  ];
  const missingVars = requiredVars.filter((varName) => !process.env[varName]);
  if (missingVars.length > 0) {
    throw new EnvironmentValidationError(
      `Missing required environment variables: ${missingVars.join(", ")}`
    );
  }
  const appUrl = process.env.SHOPIFY_APP_URL;
  if (!appUrl.startsWith("https://")) {
    throw new EnvironmentValidationError(
      "SHOPIFY_APP_URL must be a valid HTTPS URL"
    );
  }
  try {
    new URL(appUrl);
  } catch {
    throw new EnvironmentValidationError(
      "SHOPIFY_APP_URL must be a valid URL"
    );
  }
  const scopes = process.env.SCOPES;
  const requiredScopes = [
    "read_products",
    "write_products"
  ];
  const missingScopes = requiredScopes.filter(
    (scope) => !scopes.includes(scope)
  );
  if (missingScopes.length > 0) {
    console.warn(
      `⚠️  Missing required scopes: ${missingScopes.join(", ")}`
    );
  }
  const billingEnabled = process.env.BILLING_ENABLED || "true";
  const trialDays = process.env.BILLING_TRIAL_DAYS || "1";
  const currency = process.env.BILLING_CURRENCY || "USD";
  if (!["true", "false"].includes(billingEnabled)) {
    throw new EnvironmentValidationError(
      'BILLING_ENABLED must be "true" or "false"'
    );
  }
  if (isNaN(parseInt(trialDays)) || parseInt(trialDays) < 0) {
    throw new EnvironmentValidationError(
      "BILLING_TRIAL_DAYS must be a non-negative number"
    );
  }
  if (!/^[A-Z]{3}$/.test(currency)) {
    throw new EnvironmentValidationError(
      "BILLING_CURRENCY must be a valid 3-letter currency code (e.g., USD)"
    );
  }
  return {
    SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY,
    SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET,
    SHOPIFY_APP_URL: appUrl,
    SCOPES: scopes,
    DATABASE_URL: process.env.DATABASE_URL,
    SESSION_SECRET: process.env.SESSION_SECRET,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    NODE_ENV: process.env.NODE_ENV || "development",
    BILLING_ENABLED: billingEnabled,
    BILLING_TRIAL_DAYS: trialDays,
    BILLING_CURRENCY: currency
  };
}
function getEnvironmentConfig() {
  try {
    return validateEnvironment();
  } catch (error) {
    console.error("❌ Environment validation failed:", error);
    throw error;
  }
}
class MemoryCache {
  constructor() {
    __publicField(this, "cache", /* @__PURE__ */ new Map());
    __publicField(this, "cleanupInterval");
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1e3);
  }
  /**
   * Set a cache entry with TTL
   */
  set(key, data, ttlMs = 5 * 60 * 1e3) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
    console.log(`📦 Cache SET: ${key} (TTL: ${ttlMs}ms)`);
  }
  /**
   * Get a cache entry if it exists and hasn't expired
   */
  get(key) {
    const entry2 = this.cache.get(key);
    if (!entry2) {
      console.log(`📦 Cache MISS: ${key}`);
      return null;
    }
    const now = Date.now();
    const age = now - entry2.timestamp;
    if (age > entry2.ttl) {
      console.log(`📦 Cache EXPIRED: ${key} (age: ${age}ms, ttl: ${entry2.ttl}ms)`);
      this.cache.delete(key);
      return null;
    }
    console.log(`📦 Cache HIT: ${key} (age: ${age}ms)`);
    return entry2.data;
  }
  /**
   * Delete a specific cache entry
   */
  delete(key) {
    const deleted = this.cache.delete(key);
    if (deleted) {
      console.log(`📦 Cache DELETE: ${key}`);
    }
    return deleted;
  }
  /**
   * Clear all cache entries for a shop
   */
  clearShop(shop) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(shop)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
    console.log(`📦 Cache CLEAR SHOP: ${shop} (${keysToDelete.length} entries)`);
  }
  /**
   * Clear all cache entries
   */
  clear() {
    const size = this.cache.size;
    this.cache.clear();
    console.log(`📦 Cache CLEAR ALL: ${size} entries`);
  }
  /**
   * Clean up expired entries
   */
  cleanup() {
    const now = Date.now();
    const keysToDelete = [];
    for (const [key, entry2] of this.cache.entries()) {
      const age = now - entry2.timestamp;
      if (age > entry2.ttl) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
    if (keysToDelete.length > 0) {
      console.log(`📦 Cache CLEANUP: Removed ${keysToDelete.length} expired entries`);
    }
  }
  /**
   * Get cache statistics
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
  /**
   * Destroy the cache and cleanup interval
   */
  destroy() {
    clearInterval(this.cleanupInterval);
    this.clear();
  }
}
const billingCache = new MemoryCache();
const CacheKeys = {
  BILLING_STATUS: (shop) => `billing:status:${shop}`,
  SUBSCRIPTION_DATA: (shop) => `billing:subscription:${shop}`,
  PURCHASE_DATA: (shop) => `billing:purchases:${shop}`,
  BILLING_PLANS: () => `billing:plans`,
  USAGE_STATS: (shop, days) => `billing:usage:${shop}:${days}`,
  SHOPIFY_SUBSCRIPTION: (shop) => `shopify:subscription:${shop}`,
  SHOPIFY_PURCHASES: (shop) => `shopify:purchases:${shop}`,
  SUBSCRIPTION_UPDATE_FLAG: (shop) => `billing:update_flag:${shop}`
};
const CacheTTL = {
  BILLING_STATUS: 2 * 60 * 1e3,
  // 2 minutes
  SUBSCRIPTION_DATA: 5 * 60 * 1e3,
  // 5 minutes
  PURCHASE_DATA: 3 * 60 * 1e3
};
async function getCachedBillingStatus(shop, fetchFunction) {
  const cacheKey = CacheKeys.BILLING_STATUS(shop);
  const cached = billingCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }
  console.log(`🔄 Fetching fresh billing status for shop: ${shop}`);
  const data = await fetchFunction();
  billingCache.set(cacheKey, data, CacheTTL.BILLING_STATUS);
  return data;
}
async function getCachedSubscriptionData(shop, fetchFunction) {
  const cacheKey = CacheKeys.SUBSCRIPTION_DATA(shop);
  const cached = billingCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }
  console.log(`🔄 Fetching fresh subscription data for shop: ${shop}`);
  const data = await fetchFunction();
  billingCache.set(cacheKey, data, CacheTTL.SUBSCRIPTION_DATA);
  return data;
}
async function getCachedPurchaseData(shop, fetchFunction) {
  const cacheKey = CacheKeys.PURCHASE_DATA(shop);
  const cached = billingCache.get(cacheKey);
  if (cached !== null) {
    return cached;
  }
  console.log(`🔄 Fetching fresh purchase data for shop: ${shop}`);
  const data = await fetchFunction();
  billingCache.set(cacheKey, data, CacheTTL.PURCHASE_DATA);
  return data;
}
function invalidateBillingCache(shop, type) {
  console.log(`🗑️ Invalidating billing cache for shop: ${shop}, type: ${type || "all"}`);
  switch (type) {
    case "subscription":
      billingCache.delete(CacheKeys.BILLING_STATUS(shop));
      billingCache.delete(CacheKeys.SUBSCRIPTION_DATA(shop));
      billingCache.delete(CacheKeys.SHOPIFY_SUBSCRIPTION(shop));
      break;
    case "purchase":
      billingCache.delete(CacheKeys.BILLING_STATUS(shop));
      billingCache.delete(CacheKeys.PURCHASE_DATA(shop));
      billingCache.delete(CacheKeys.SHOPIFY_PURCHASES(shop));
      break;
    default:
      billingCache.clearShop(shop);
      break;
  }
}
function setSubscriptionUpdateFlag(shop) {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  const timestamp = Date.now();
  billingCache.set(cacheKey, timestamp, 5 * 60 * 1e3);
  console.log(`🚩 Subscription update flag set for shop: ${shop} at ${new Date(timestamp).toISOString()}`);
}
function getSubscriptionUpdateFlag(shop) {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  const timestamp = billingCache.get(cacheKey);
  if (timestamp !== null) {
    console.log(`🚩 Subscription update flag found for shop: ${shop} at ${new Date(timestamp).toISOString()}`);
  }
  return timestamp;
}
function clearSubscriptionUpdateFlag(shop) {
  const cacheKey = CacheKeys.SUBSCRIPTION_UPDATE_FLAG(shop);
  billingCache.delete(cacheKey);
  console.log(`🚩 Subscription update flag cleared for shop: ${shop}`);
}
const cache_server = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  CacheKeys,
  CacheTTL,
  clearSubscriptionUpdateFlag,
  getCachedBillingStatus,
  getCachedPurchaseData,
  getCachedSubscriptionData,
  getSubscriptionUpdateFlag,
  invalidateBillingCache,
  setSubscriptionUpdateFlag
}, Symbol.toStringTag, { value: "Module" }));
const BILLING_PLANS = [
  {
    id: "annual",
    name: "Annual Subscription",
    type: "annual",
    price: 199.99,
    interval: "ANNUAL",
    description: "Best Value - Save $40/year",
    features: [
      "Unlimited product optimizations",
      "Priority support",
      "Advanced analytics",
      "Bulk operations"
    ],
    recommended: true
  },
  {
    id: "monthly",
    name: "Monthly Subscription",
    type: "monthly",
    price: 19.99,
    interval: "EVERY_30_DAYS",
    description: "Flexible monthly billing",
    features: [
      "Unlimited product optimizations",
      "Standard support",
      "Basic analytics",
      "Bulk operations"
    ]
  },
  {
    id: "pay_per_use",
    name: "Pay-Per-Use",
    type: "pay_per_use",
    price: 0.1,
    description: "Pay only for what you use",
    features: [
      "$0.10 per product optimized",
      "No monthly commitment",
      "Pre-payment required",
      "Standard support",
      "Basic analytics"
    ]
  }
];
class BillingService {
  constructor(admin, shop) {
    __publicField(this, "admin");
    __publicField(this, "shop");
    if (!admin) {
      throw new Error("Admin GraphQL client is required for billing operations");
    }
    if (!shop || typeof shop !== "string" || shop.trim() === "") {
      throw new Error("Valid shop domain is required for billing operations");
    }
    this.admin = admin;
    this.shop = shop.trim();
    this.validateEnvironment();
    console.log(`🏪 BillingService initialized for shop: ${this.shop}`);
  }
  validateEnvironment() {
    try {
      getEnvironmentConfig();
      if (!this.admin.graphql) {
        throw new Error("Admin client must have GraphQL capability");
      }
      console.log(`✅ BillingService environment validated for ${this.shop}`);
    } catch (error) {
      console.error(`❌ BillingService environment validation failed for ${this.shop}:`, error);
      throw error;
    }
  }
  /**
   * Execute GraphQL query with retry logic, rate limiting, and proper error handling
   */
  async executeGraphQLWithRetry(query, variables, maxRetries = 3, context) {
    var _a2;
    await applyRateLimit(RATE_LIMITERS.API_BILLING(this.shop), context);
    let lastError = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 GraphQL attempt ${attempt}/${maxRetries} for shop: ${this.shop}`);
        const response = await this.admin.graphql(query, { variables });
        const result = await response.json();
        console.log(`📋 GraphQL Response (attempt ${attempt}):`, JSON.stringify(result, null, 2));
        if (result.errors && result.errors.length > 0) {
          const error = result.errors[0];
          throw new Error(`GraphQL Error: ${error.message} (Code: ${((_a2 = error.extensions) == null ? void 0 : _a2.code) || "UNKNOWN"})`);
        }
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`❌ GraphQL attempt ${attempt} failed:`, lastError.message);
        if (lastError.message.includes("Invalid") || lastError.message.includes("Unauthorized") || lastError.message.includes("Forbidden")) {
          throw lastError;
        }
        await logError(lastError, {
          ...context,
          shop: this.shop,
          action: "graphql_query",
          metadata: {
            attempt: attempt + 1,
            maxRetries,
            query: query.substring(0, 100) + "...",
            variables: JSON.stringify(variables).substring(0, 200)
          }
        });
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1e3;
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    const enhancedError = createError("API_SHOPIFY_ERROR", {
      ...context,
      shop: this.shop,
      action: "graphql_query_final_failure",
      metadata: {
        maxRetries,
        query: query.substring(0, 100) + "...",
        lastErrorMessage: lastError == null ? void 0 : lastError.message
      }
    }, lastError || void 0);
    throw enhancedError;
  }
  // Create annual or monthly subscription
  async createSubscription(planId, returnUrl) {
    var _a2, _b, _c, _d, _e, _f, _g;
    console.log(`🔄 Creating subscription for shop: ${this.shop}, plan: ${planId}`);
    if (!planId || typeof planId !== "string") {
      throw new Error("Valid plan ID is required");
    }
    if (!returnUrl || typeof returnUrl !== "string") {
      throw new Error("Valid return URL is required");
    }
    try {
      new URL(returnUrl);
    } catch {
      throw new Error("Return URL must be a valid URL");
    }
    const plan = BILLING_PLANS.find((p) => p.id === planId);
    if (!plan || plan.type === "pay_per_use") {
      throw new Error(`Invalid subscription plan: ${planId}. Available plans: ${BILLING_PLANS.filter((p) => p.type !== "pay_per_use").map((p) => p.id).join(", ")}`);
    }
    if (!this.shop) {
      throw new Error("Shop is required for subscription creation");
    }
    console.log("🔄 Creating subscription with variables (TEST MODE ENABLED):", {
      name: `AI BULK SEO - ${plan.name}`,
      returnUrl,
      test: process.env.NODE_ENV !== "production",
      lineItems: [{
        plan: {
          appRecurringPricingDetails: {
            price: {
              amount: plan.price,
              currencyCode: "USD"
            },
            interval: plan.interval
          }
        }
      }]
    });
    const result = await this.executeGraphQLWithRetry(`
      mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $test: Boolean) {
        appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, test: $test) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
            name
            currentPeriodEnd
            lineItems {
              id
              plan {
                pricingDetails {
                  __typename
                }
              }
            }
          }
          confirmationUrl
        }
      }
    `, {
      name: `AI BULK SEO - ${plan.name}`,
      returnUrl,
      test: true,
      // Enable Shopify test transactions
      lineItems: [{
        plan: {
          appRecurringPricingDetails: {
            price: {
              amount: plan.price,
              currencyCode: "USD"
            },
            interval: plan.interval
          }
        }
      }]
    }, 3, { shop: this.shop, action: "create_subscription" });
    console.log("📋 GraphQL Response:", JSON.stringify(result, null, 2));
    if (((_c = (_b = (_a2 = result.data) == null ? void 0 : _a2.appSubscriptionCreate) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
      const error = result.data.appSubscriptionCreate.userErrors[0];
      console.error("❌ Subscription creation error:", error);
      throw new Error(`Subscription creation failed: ${error.message} (Field: ${error.field})`);
    }
    if (!((_e = (_d = result.data) == null ? void 0 : _d.appSubscriptionCreate) == null ? void 0 : _e.appSubscription)) {
      throw new Error("Subscription creation failed: No subscription returned from Shopify");
    }
    if (!result.data.appSubscriptionCreate.confirmationUrl) {
      throw new Error("Subscription creation failed: No confirmation URL returned from Shopify");
    }
    if (((_g = (_f = result.data) == null ? void 0 : _f.appSubscriptionCreate) == null ? void 0 : _g.appSubscription) && this.shop) {
      const subscription = result.data.appSubscriptionCreate.appSubscription;
      try {
        await db.$transaction(async (tx) => {
          const billingSubscription = await tx.billingSubscription.create({
            data: {
              shop: this.shop,
              subscriptionId: subscription.id,
              planId,
              status: subscription.status,
              trialDays: 0,
              trialEndsAt: null,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              priceAmount: plan.price,
              priceCurrency: "USD"
            }
          });
          await tx.billingEvent.create({
            data: {
              shop: this.shop,
              eventType: "subscription_created",
              referenceId: subscription.id,
              eventData: JSON.stringify(subscription)
            }
          });
          await tx.session.update({
            where: { shop: this.shop },
            data: {
              subscriptionId: subscription.id,
              subscriptionStatus: subscription.status,
              billingPlanId: planId,
              trialEndsAt: null,
              lastBillingCheck: /* @__PURE__ */ new Date()
            }
          });
          return billingSubscription;
        });
        console.log("✅ Subscription stored in database for shop:", this.shop);
      } catch (dbError) {
        console.error("Failed to store subscription in database:", dbError);
      }
    }
    return result;
  }
  /**
   * Create one-time purchase for pay-per-use with proper validation
   */
  async createOneTimePurchase(productCount, returnUrl) {
    var _a2, _b, _c, _d, _e, _f, _g;
    console.log(`🔄 Creating one-time purchase for shop: ${this.shop}, products: ${productCount}`);
    if (!productCount || typeof productCount !== "number" || productCount <= 0) {
      throw new Error("Product count must be a positive number");
    }
    if (productCount > 1e3) {
      throw new Error("Product count cannot exceed 1000 per purchase");
    }
    if (!returnUrl || typeof returnUrl !== "string") {
      throw new Error("Valid return URL is required");
    }
    try {
      new URL(returnUrl);
    } catch {
      throw new Error("Return URL must be a valid URL");
    }
    if (!this.shop) {
      throw new Error("Shop is required for purchase creation");
    }
    const amount = (productCount * 0.1).toFixed(2);
    console.log(`💰 Creating purchase (TEST MODE ENABLED): ${productCount} products at $0.10 each = $${amount}`);
    const result = await this.executeGraphQLWithRetry(`
      mutation AppPurchaseOneTimeCreate($name: String!, $price: MoneyInput!, $returnUrl: URL!, $test: Boolean) {
        appPurchaseOneTimeCreate(name: $name, price: $price, returnUrl: $returnUrl, test: $test) {
          userErrors {
            field
            message
          }
          appPurchaseOneTime {
            id
            status
            name
            price {
              amount
              currencyCode
            }
            createdAt
          }
          confirmationUrl
        }
      }
    `, {
      name: `AI BULK SEO - ${productCount} Product${productCount > 1 ? "s" : ""} Optimization`,
      price: {
        amount: parseFloat(amount),
        currencyCode: "USD"
      },
      returnUrl,
      test: true
      // Enable Shopify test transactions
    }, 3, { shop: this.shop, action: "create_one_time_purchase" });
    if (((_c = (_b = (_a2 = result.data) == null ? void 0 : _a2.appPurchaseOneTimeCreate) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
      const error = result.data.appPurchaseOneTimeCreate.userErrors[0];
      throw new Error(`Purchase creation failed: ${error.message} (Field: ${error.field})`);
    }
    if (!((_e = (_d = result.data) == null ? void 0 : _d.appPurchaseOneTimeCreate) == null ? void 0 : _e.appPurchaseOneTime)) {
      throw new Error("Purchase creation failed: No purchase returned from Shopify");
    }
    if (!result.data.appPurchaseOneTimeCreate.confirmationUrl) {
      throw new Error("Purchase creation failed: No confirmation URL returned from Shopify");
    }
    if (((_g = (_f = result.data) == null ? void 0 : _f.appPurchaseOneTimeCreate) == null ? void 0 : _g.appPurchaseOneTime) && this.shop) {
      const purchase = result.data.appPurchaseOneTimeCreate.appPurchaseOneTime;
      try {
        await db.$transaction(async (tx) => {
          const billingPurchase = await tx.billingPurchase.create({
            data: {
              shop: this.shop,
              purchaseId: purchase.id,
              status: purchase.status,
              productCount,
              amount: parseFloat(amount),
              currency: "USD",
              description: purchase.name
            }
          });
          await tx.billingEvent.create({
            data: {
              shop: this.shop,
              eventType: "purchase_created",
              referenceId: purchase.id,
              eventData: JSON.stringify(purchase)
            }
          });
          return billingPurchase;
        });
        console.log("✅ Purchase stored in database for shop:", this.shop);
      } catch (dbError) {
        console.error("Failed to store purchase in database:", dbError);
      }
      invalidateBillingCache(this.shop, "purchase");
    }
    console.log(`✅ Pay-per-use purchase created successfully for shop: ${this.shop}`);
    return result;
  }
  // Get current subscription status with caching
  async getCurrentSubscription() {
    return getCachedSubscriptionData(this.shop, async () => {
      try {
        const response = await this.admin.graphql(`
          query {
            currentAppInstallation {
              activeSubscriptions {
                id
                name
                status
                trialDays
                currentPeriodEnd
                createdAt
                lineItems {
                  id
                  plan {
                    pricingDetails {
                      __typename
                      price {
                        amount
                        currencyCode
                      }
                      interval
                    }
                  }
                }
              }
            }
          }
        `);
        return response.json();
      } catch (error) {
        console.log(`⚠️ Enhanced query failed, falling back to basic query:`, error instanceof Error ? error.message : String(error));
        const response = await this.admin.graphql(`
          query {
            currentAppInstallation {
              activeSubscriptions {
                id
                name
                status
                trialDays
                currentPeriodEnd
                createdAt
                lineItems {
                  id
                  plan {
                    pricingDetails {
                      __typename
                    }
                  }
                }
              }
            }
          }
        `);
        return response.json();
      }
    });
  }
  // Get recent one-time purchases with caching
  async getOneTimePurchases() {
    return getCachedPurchaseData(this.shop, async () => {
      const response = await this.admin.graphql(`
        query {
          currentAppInstallation {
            oneTimePurchases(first: 10) {
              edges {
                node {
                  id
                  name
                  status
                  price {
                    amount
                    currencyCode
                  }
                  createdAt
                }
              }
            }
          }
        }
      `);
      return response.json();
    });
  }
  /**
   * Cancel subscription with proper validation and error handling
   */
  async cancelSubscription(subscriptionId) {
    console.log(`🔄 Cancelling subscription for shop: ${this.shop}, subscription: ${subscriptionId}`);
    if (!subscriptionId || typeof subscriptionId !== "string") {
      throw new Error("Valid subscription ID is required");
    }
    if (!subscriptionId.startsWith("gid://shopify/AppSubscription/")) {
      throw new Error("Invalid subscription ID format");
    }
    const response = await this.executeGraphQLWithRetry(`
      mutation AppSubscriptionCancel($id: ID!) {
        appSubscriptionCancel(id: $id) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
            name
            currentPeriodEnd
          }
        }
      }
    `, {
      id: subscriptionId
    }, 3, { shop: this.shop, action: "cancel_subscription" });
    console.log(`✅ Subscription cancellation completed for shop: ${this.shop}`);
    return response;
  }
  /**
   * Check if user has active billing with comprehensive logic and caching
   * Handles subscriptions and pay-per-use purchases
   */
  async hasActiveBilling() {
    console.log(`🔍 Checking billing status for shop: ${this.shop}`);
    return getCachedBillingStatus(this.shop, async () => {
      var _a2, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
      try {
        const subscriptionData = await this.getCurrentSubscription();
        const allSubscriptions = ((_b = (_a2 = subscriptionData.data) == null ? void 0 : _a2.currentAppInstallation) == null ? void 0 : _b.activeSubscriptions) || [];
        const activeSubscriptions = allSubscriptions.filter(
          (sub) => sub.status === "ACTIVE" || sub.status === "PENDING"
        );
        console.log(`📊 Found ${allSubscriptions.length} total subscriptions, ${activeSubscriptions.length} truly active`);
        const dbSubscription = await db.billingSubscription.findFirst({
          where: {
            shop: this.shop,
            status: { in: ["ACTIVE", "PENDING"] }
          },
          orderBy: { updatedAt: "desc" }
        });
        const dbSession = await db.session.findUnique({
          where: { shop: this.shop },
          select: {
            subscriptionId: true,
            subscriptionStatus: true,
            billingPlanId: true,
            trialEndsAt: true
          }
        });
        console.log(`📋 Database subscription:`, dbSubscription ? {
          id: dbSubscription.subscriptionId,
          status: dbSubscription.status,
          planId: dbSubscription.planId
        } : "none");
        console.log(`📋 Database session:`, dbSession ? {
          subscriptionId: dbSession.subscriptionId,
          status: dbSession.subscriptionStatus,
          planId: dbSession.billingPlanId
        } : "none");
        if (activeSubscriptions.length > 0) {
          const subscription = activeSubscriptions[0];
          console.log(`📋 Subscription status: ${subscription.status}`);
          let plan;
          const lineItem = (_c = subscription.lineItems) == null ? void 0 : _c[0];
          console.log(`🔍 Subscription lineItems:`, JSON.stringify(subscription.lineItems, null, 2));
          let planDetected = false;
          if ((_e = (_d = lineItem == null ? void 0 : lineItem.plan) == null ? void 0 : _d.pricingDetails) == null ? void 0 : _e.interval) {
            const interval = lineItem.plan.pricingDetails.interval;
            const amount = parseFloat(((_f = lineItem.plan.pricingDetails.price) == null ? void 0 : _f.amount) || "0");
            console.log(`📋 Plan details from API: interval=${interval}, amount=${amount}`);
            if (interval === "ANNUAL") {
              plan = BILLING_PLANS.find((p) => p.id === "annual");
              planDetected = true;
            } else if (interval === "EVERY_30_DAYS") {
              plan = BILLING_PLANS.find((p) => p.id === "monthly");
              planDetected = true;
            }
            console.log(`💰 Detected plan from API: ${(plan == null ? void 0 : plan.name) || "unknown"} (${interval}, $${amount})`);
          }
          if (!planDetected && subscription.name) {
            const name = subscription.name.toLowerCase();
            console.log(`📋 Analyzing subscription name: "${subscription.name}"`);
            if (name.includes("annual") || name.includes("yearly") || name.includes("year")) {
              plan = BILLING_PLANS.find((p) => p.id === "annual");
              planDetected = true;
              console.log(`💰 Detected annual plan from name: ${subscription.name}`);
            } else if (name.includes("monthly") || name.includes("month")) {
              plan = BILLING_PLANS.find((p) => p.id === "monthly");
              planDetected = true;
              console.log(`💰 Detected monthly plan from name: ${subscription.name}`);
            }
          }
          if (!planDetected) {
            console.log(`❌ No plan detected from API, trying database fallback`);
            if (dbSubscription == null ? void 0 : dbSubscription.planId) {
              plan = BILLING_PLANS.find((p) => p.id === dbSubscription.planId);
              planDetected = true;
              console.log(`💰 Using plan from database: ${(plan == null ? void 0 : plan.name) || "unknown"} (${dbSubscription.planId})`);
            } else if ((dbSession == null ? void 0 : dbSession.billingPlanId) && dbSession.billingPlanId !== "pay_per_use") {
              plan = BILLING_PLANS.find((p) => p.id === dbSession.billingPlanId);
              planDetected = true;
              console.log(`💰 Using plan from session: ${(plan == null ? void 0 : plan.name) || "unknown"} (${dbSession.billingPlanId})`);
            }
          }
          if (!planDetected && subscription.status === "ACTIVE") {
            console.log(`⚠️ Active subscription found but no plan detected, defaulting to monthly`);
            plan = BILLING_PLANS.find((p) => p.id === "monthly");
          }
          const hasAccess = subscription.status === "ACTIVE";
          if (!plan && subscription.status === "ACTIVE") {
            console.log(`⚠️ Creating fallback plan for active subscription`);
            const lineItem2 = (_g = subscription.lineItems) == null ? void 0 : _g[0];
            if ((_h = lineItem2 == null ? void 0 : lineItem2.plan) == null ? void 0 : _h.pricingDetails) {
              const interval = lineItem2.plan.pricingDetails.interval;
              const amount = parseFloat(((_i = lineItem2.plan.pricingDetails.price) == null ? void 0 : _i.amount) || "0");
              plan = {
                id: interval === "ANNUAL" ? "annual" : "monthly",
                name: interval === "ANNUAL" ? "Annual Plan" : "Monthly Plan",
                description: interval === "ANNUAL" ? "Annual subscription with unlimited access" : "Monthly subscription with unlimited access",
                price: amount,
                type: interval === "ANNUAL" ? "annual" : "monthly",
                features: [
                  "Unlimited product optimizations",
                  "Advanced SEO analysis",
                  "Bulk optimization tools",
                  "Priority support"
                ]
              };
              console.log(`✅ Created fallback plan: ${plan == null ? void 0 : plan.name} ($${plan == null ? void 0 : plan.price})`);
            }
          }
          console.log(`✅ Billing check result: hasAccess=${hasAccess}, plan=${(plan == null ? void 0 : plan.name) || "none"}`);
          return {
            hasAccess,
            plan,
            subscription
          };
        }
        console.log(`🔍 No active subscriptions, checking pay-per-use purchases...`);
        try {
          const purchaseData = await this.getOneTimePurchases();
          const purchases = ((_l = (_k = (_j = purchaseData.data) == null ? void 0 : _j.currentAppInstallation) == null ? void 0 : _k.oneTimePurchases) == null ? void 0 : _l.edges) || [];
          console.log(`💳 Found ${purchases.length} one-time purchases`);
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1e3);
          const recentPurchases = purchases.filter((edge) => {
            const purchase = edge.node;
            const createdAt = new Date(purchase.createdAt);
            return (purchase.status === "ACCEPTED" || purchase.status === "PENDING") && createdAt > thirtyDaysAgo;
          });
          if (recentPurchases.length > 0) {
            const payPerUsePlan2 = BILLING_PLANS.find((p) => p.id === "pay_per_use");
            console.log(`✅ Found ${recentPurchases.length} recent pay-per-use purchases`);
            console.log("📋 Recent purchases:", recentPurchases.map((edge) => ({
              status: edge.node.status,
              createdAt: edge.node.createdAt,
              amount: edge.node.amount
            })));
            console.log("🎯 Returning pay-per-use plan:", payPerUsePlan2);
            return {
              hasAccess: true,
              plan: payPerUsePlan2
            };
          }
        } catch (purchaseError) {
          console.error("❌ Error checking pay-per-use purchases:", purchaseError);
        }
        if (dbSubscription && dbSubscription.status === "ACTIVE") {
          console.log(`🔄 Found active subscription in database: ${dbSubscription.subscriptionId}`);
          const plan = BILLING_PLANS.find((p) => p.id === dbSubscription.planId);
          const hasAccess = dbSubscription.status === "ACTIVE";
          console.log(`✅ Using database subscription data: plan=${plan == null ? void 0 : plan.name}, hasAccess=${hasAccess}`);
          return {
            hasAccess: Boolean(hasAccess),
            plan: plan || BILLING_PLANS.find((p) => p.id === "monthly"),
            subscription: {
              id: dbSubscription.subscriptionId,
              status: dbSubscription.status,
              name: `AI BULK SEO - ${(plan == null ? void 0 : plan.name) || "Subscription"}`,
              currentPeriodEnd: dbSubscription.currentPeriodEnd instanceof Date ? dbSubscription.currentPeriodEnd.toISOString() : typeof dbSubscription.currentPeriodEnd === "string" ? dbSubscription.currentPeriodEnd : void 0,
              lineItems: []
            }
          };
        }
        console.log(`❌ No active billing found for shop: ${this.shop}`);
        const payPerUsePlan = BILLING_PLANS.find((p) => p.id === "pay_per_use");
        console.log("🎯 Returning default pay-per-use plan for credit purchases");
        return {
          hasAccess: false,
          plan: payPerUsePlan
        };
      } catch (error) {
        console.error(`❌ Error checking billing status for shop ${this.shop}:`, error);
        return { hasAccess: false };
      }
    });
  }
  /**
   * Calculate pay-per-use cost with validation
   */
  calculatePayPerUseCost(productCount) {
    if (!productCount || productCount <= 0) {
      throw new Error("Product count must be greater than 0");
    }
    if (productCount > 1e3) {
      throw new Error("Product count cannot exceed 1000 per purchase");
    }
    const costPerProduct = 0.1;
    const totalCost = productCount * costPerProduct;
    console.log(`💰 Pay-per-use cost calculation: ${productCount} products × $${costPerProduct} = $${totalCost.toFixed(2)}`);
    return parseFloat(totalCost.toFixed(2));
  }
  /**
   * Track usage for billing purposes
   */
  async trackUsage(billingType, productsOptimized, billingReferenceId, batchId) {
    var _a2;
    try {
      console.log(`📊 Tracking usage: ${billingType}, ${productsOptimized} products optimized`);
      if (db && typeof ((_a2 = db.billingUsage) == null ? void 0 : _a2.create) === "function") {
        await db.billingUsage.create({
          data: {
            shop: this.shop,
            billingType,
            billingReferenceId,
            productsOptimized,
            batchId,
            optimizationDate: /* @__PURE__ */ new Date()
          }
        });
      } else {
        console.warn("⚠️ Database models not available, skipping usage tracking");
      }
      console.log(`✅ Usage tracked for shop: ${this.shop}`);
    } catch (error) {
      console.error(`❌ Failed to track usage for shop ${this.shop}:`, error);
    }
  }
  /**
   * Get usage statistics for the shop
   */
  async getUsageStats(days = 30) {
    var _a2;
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1e3);
      if (db && typeof ((_a2 = db.billingUsage) == null ? void 0 : _a2.findMany) === "function") {
        const usage = await db.billingUsage.findMany({
          where: {
            shop: this.shop,
            optimizationDate: {
              gte: startDate
            }
          }
        });
        const totalOptimized = usage.reduce((sum, record) => sum + record.productsOptimized, 0);
        const byType = usage.reduce((acc, record) => {
          acc[record.billingType] = (acc[record.billingType] || 0) + record.productsOptimized;
          return acc;
        }, {});
        console.log(`📈 Usage stats for ${days} days: ${totalOptimized} total products optimized`);
        return { totalOptimized, byType };
      } else {
        console.warn("⚠️ Database models not available, returning empty usage stats");
        return { totalOptimized: 0, byType: {} };
      }
    } catch (error) {
      console.error(`❌ Failed to get usage stats for shop ${this.shop}:`, error);
      return { totalOptimized: 0, byType: {} };
    }
  }
  /**
   * Get all billing plans
   */
  getAllBillingPlans() {
    return BILLING_PLANS;
  }
  /**
   * Get billing plan by ID with validation
   */
  getBillingPlan(planId) {
    if (!planId || typeof planId !== "string") {
      return void 0;
    }
    return BILLING_PLANS.find((p) => p.id === planId);
  }
}
const action$6 = withBulletproofAction(async ({ request, auth }) => {
  var _a2, _b;
  try {
    const { admin, session } = auth;
    if (!(session == null ? void 0 : session.shop)) {
      return json({ error: "No shop found in session" }, { status: 401 });
    }
    console.log(`🔄 Manual subscription sync requested for shop: ${session.shop}`);
    invalidateBillingCache(session.shop);
    console.log(`🔄 All billing cache cleared for shop: ${session.shop}`);
    const billingService = new BillingService(admin, session.shop);
    const subscriptionData = await billingService.getCurrentSubscription();
    const allSubscriptions = ((_b = (_a2 = subscriptionData.data) == null ? void 0 : _a2.currentAppInstallation) == null ? void 0 : _b.activeSubscriptions) || [];
    console.log(`📊 Found ${allSubscriptions.length} subscriptions from Shopify API`);
    if (allSubscriptions.length > 0) {
      const subscription = allSubscriptions[0];
      console.log(`📋 Processing subscription: ${subscription.id}, status: ${subscription.status}`);
      const determinePlanId2 = (sub) => {
        if (!sub.name) return "monthly";
        const name = sub.name.toLowerCase();
        if (name.includes("annual") || name.includes("yearly")) return "annual";
        return "monthly";
      };
      await db.$transaction(async (tx) => {
        await tx.billingSubscription.upsert({
          where: { subscriptionId: subscription.id },
          update: {
            status: subscription.status,
            planId: determinePlanId2(subscription),
            trialDays: subscription.trialDays || 0,
            currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
            updatedAt: /* @__PURE__ */ new Date()
          },
          create: {
            shop: session.shop,
            subscriptionId: subscription.id,
            status: subscription.status,
            planId: determinePlanId2(subscription),
            trialDays: subscription.trialDays || 0,
            trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
            currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
            priceAmount: 29.99,
            // Default price
            priceCurrency: "USD"
          }
        });
        await tx.session.updateMany({
          where: { shop: session.shop },
          data: {
            subscriptionId: subscription.id,
            subscriptionStatus: subscription.status,
            billingPlanId: determinePlanId2(subscription),
            trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
            lastBillingCheck: /* @__PURE__ */ new Date()
          }
        });
        await tx.billingEvent.create({
          data: {
            shop: session.shop,
            eventType: "subscription_manual_sync",
            referenceId: subscription.id,
            eventData: JSON.stringify({
              subscriptionId: subscription.id,
              status: subscription.status,
              planId: determinePlanId2(subscription),
              syncTimestamp: (/* @__PURE__ */ new Date()).toISOString()
            })
          }
        });
      });
      console.log(`✅ Subscription synced successfully for shop: ${session.shop}`);
      const billingStatus = await billingService.hasActiveBilling();
      return json({
        success: true,
        message: "Subscription synced successfully",
        subscription: {
          id: subscription.id,
          status: subscription.status,
          planId: determinePlanId2(subscription)
        },
        billingStatus
      });
    } else {
      console.log(`❌ No active subscriptions found for shop: ${session.shop}`);
      return json({
        success: false,
        message: "No active subscriptions found in Shopify",
        subscriptionsFound: 0
      });
    }
  } catch (error) {
    console.error("❌ Subscription sync error:", error);
    return json({
      error: error instanceof Error ? error.message : "Failed to sync subscription"
    }, { status: 500 });
  }
});
const loader$a = async () => {
  return json({ error: "Method not allowed" }, { status: 405 });
};
const route11 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$6,
  loader: loader$a
}, Symbol.toStringTag, { value: "Module" }));
const action$5 = withBulletproofAction(async ({ request, auth }) => {
  var _a2, _b;
  try {
    const { admin, session } = auth;
    if (!(session == null ? void 0 : session.shop)) {
      return json({ error: "No shop found in session" }, { status: 401 });
    }
    console.log(`🔄 Manual billing refresh requested for shop: ${session.shop}`);
    invalidateBillingCache(session.shop);
    console.log(`🔄 Billing cache invalidated for shop: ${session.shop}`);
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    console.log(`✅ Fresh billing status retrieved for shop: ${session.shop}`, {
      hasAccess: billingStatus.hasAccess,
      plan: (_a2 = billingStatus.plan) == null ? void 0 : _a2.id,
      subscription: (_b = billingStatus.subscription) == null ? void 0 : _b.id
    });
    return json({
      success: true,
      billingStatus,
      message: "Billing status refreshed successfully"
    });
  } catch (error) {
    console.error("❌ Billing refresh error:", error);
    return json({
      error: error instanceof Error ? error.message : "Failed to refresh billing status"
    }, { status: 500 });
  }
});
const loader$9 = async () => {
  return json({ error: "Method not allowed" }, { status: 405 });
};
const route12 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$5,
  loader: loader$9
}, Symbol.toStringTag, { value: "Module" }));
const loader$8 = async ({ request }) => {
  try {
    const { admin, session } = await authenticate.admin(request);
    const { getSubscriptionUpdateFlag: getSubscriptionUpdateFlag2, clearSubscriptionUpdateFlag: clearSubscriptionUpdateFlag2, invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
    const subscriptionUpdateFlag = getSubscriptionUpdateFlag2(session.shop);
    if (subscriptionUpdateFlag) {
      console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh via API`);
      invalidateBillingCache2(session.shop);
      clearSubscriptionUpdateFlag2(session.shop);
    }
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    return json({
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan,
      subscription: billingStatus.subscription,
      isLoading: false
    });
  } catch (error) {
    console.error("Billing status API error:", error);
    return json({
      hasAccess: false,
      isLoading: false,
      error: error instanceof Error ? error.message : "Failed to check billing status"
    }, { status: 500 });
  }
};
const route13 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$8
}, Symbol.toStringTag, { value: "Module" }));
const ClientOnlyLottie = ({ animationData, loop = true }) => {
  const [Lottie, setLottie] = useState(null);
  useEffect(() => {
    import("lottie-react").then((LottieReact) => {
      setLottie(() => LottieReact.default);
    });
  }, []);
  if (!Lottie) {
    return /* @__PURE__ */ jsx("div", { className: "animate-pulse bg-gray-200 rounded w-full h-full" });
  }
  return /* @__PURE__ */ jsx(Lottie, { animationData, loop });
};
const SuccessAnimation = ({ size = 100, className = "" }) => {
  const successAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 60,
    "w": 200,
    "h": 200,
    "nm": "Success",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Check",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0, 0, 100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 20, "s": [120, 120, 100] },
              { "t": 40, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "ind": 0,
                "ty": "sh",
                "ks": {
                  "a": 1,
                  "k": [
                    { "i": { "x": 0.833, "y": 0.833 }, "o": { "x": 0.167, "y": 0.167 }, "t": 10, "s": [{ "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-20, 0], [-20, 0], [-20, 0]], "c": false }] },
                    { "t": 50, "s": [{ "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-20, 0], [0, 20], [30, -20]], "c": false }] }
                  ]
                }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0.2, 0.8, 0.2, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 8 },
                "lc": 2,
                "lj": 2,
                "ml": 4
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 60,
        "st": 0,
        "bm": 0
      },
      {
        "ddd": 0,
        "ind": 2,
        "ty": 4,
        "nm": "Circle",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0, 0, 100] },
              { "t": 30, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [80, 80] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0.2, 0.8, 0.2, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 4 },
                "lc": 1,
                "lj": 1,
                "ml": 4
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 60,
        "st": 0,
        "bm": 0
      }
    ]
  };
  return /* @__PURE__ */ jsx(
    motion.div,
    {
      initial: { opacity: 0, scale: 0.5 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.5 },
      className,
      style: { width: size, height: size },
      children: /* @__PURE__ */ jsx(ClientOnlyLottie, { animationData: successAnimation, loop: false })
    }
  );
};
const MorphingShapeAnimation = ({ size = 100, className = "" }) => {
  return /* @__PURE__ */ jsx(
    motion.div,
    {
      initial: { opacity: 0, scale: 0.8 },
      animate: { opacity: 1, scale: 1 },
      exit: { opacity: 0, scale: 0.8 },
      className,
      style: { width: size, height: size, display: "flex", alignItems: "center", justifyContent: "center" },
      children: /* @__PURE__ */ jsx(
        motion.div,
        {
          animate: {
            borderRadius: ["20%", "50%", "20%"],
            rotate: [0, 180, 360],
            scale: [1, 1.2, 1]
          },
          transition: {
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          },
          style: {
            width: size * 0.6,
            height: size * 0.6,
            background: "linear-gradient(45deg, #000000, #333333)",
            borderRadius: "20%"
          }
        }
      )
    }
  );
};
function cn(...inputs) {
  return twMerge(clsx(inputs));
}
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-bold transition-all duration-300 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-offset-2 focus-visible:ring-offset-black relative overflow-hidden group active:scale-95",
  {
    variants: {
      variant: {
        default: "bg-white text-black hover:bg-gray-100 shadow-lg hover:shadow-2xl border border-gray-200 hover:border-gray-300 transform hover:scale-[1.02]",
        destructive: "bg-black text-white hover:bg-gray-900 shadow-lg hover:shadow-2xl border border-gray-800 hover:border-gray-700 transform hover:scale-[1.02]",
        outline: "border-2 border-gray-300 bg-transparent text-black hover:bg-black hover:text-white hover:border-black shadow-md hover:shadow-xl transform hover:scale-[1.02]",
        secondary: "bg-gray-100 text-black hover:bg-gray-200 shadow-md hover:shadow-lg border border-gray-200 hover:border-gray-300 transform hover:scale-[1.02]",
        ghost: "text-black hover:bg-gray-100 rounded-full transform hover:scale-[1.02] hover:shadow-md",
        link: "text-black underline-offset-4 hover:underline hover:text-gray-700 transition-colors"
      },
      size: {
        default: "h-12 px-8 py-3 text-sm",
        sm: "h-10 px-6 py-2 text-xs",
        lg: "h-14 px-10 py-4 text-base",
        icon: "size-12"
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default"
    }
  }
);
function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}) {
  const Comp = asChild ? Slot : "button";
  return /* @__PURE__ */ jsx(
    Comp,
    {
      "data-slot": "button",
      className: cn(buttonVariants({ variant, size, className })),
      ...props
    }
  );
}
function Input({ className, type, ...props }) {
  return /* @__PURE__ */ jsx(
    "input",
    {
      type,
      "data-slot": "input",
      className: cn(
        "flex h-14 w-full rounded-2xl border-2 border-gray-300 bg-white px-6 py-4 text-base font-medium text-black placeholder:text-gray-400 transition-all duration-300 focus:border-black focus:ring-4 focus:ring-gray-100 focus:outline-none hover:border-gray-400 hover:shadow-lg focus:shadow-xl disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-gray-50 shadow-md",
        className
      ),
      ...props
    }
  );
}
function Select({
  ...props
}) {
  return /* @__PURE__ */ jsx(SelectPrimitive.Root, { "data-slot": "select", ...props });
}
function SelectValue({
  ...props
}) {
  return /* @__PURE__ */ jsx(SelectPrimitive.Value, { "data-slot": "select-value", ...props });
}
function SelectTrigger({
  className,
  size = "default",
  children,
  ...props
}) {
  return /* @__PURE__ */ jsxs(
    SelectPrimitive.Trigger,
    {
      "data-slot": "select-trigger",
      "data-size": size,
      className: cn(
        "border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className
      ),
      ...props,
      children: [
        children,
        /* @__PURE__ */ jsx(SelectPrimitive.Icon, { asChild: true, children: /* @__PURE__ */ jsx(ChevronDownIcon, { className: "size-4 opacity-50" }) })
      ]
    }
  );
}
function SelectContent({
  className,
  children,
  position = "popper",
  ...props
}) {
  return /* @__PURE__ */ jsx(SelectPrimitive.Portal, { children: /* @__PURE__ */ jsxs(
    SelectPrimitive.Content,
    {
      "data-slot": "select-content",
      className: cn(
        "bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",
        position === "popper" && "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",
        className
      ),
      position,
      ...props,
      children: [
        /* @__PURE__ */ jsx(SelectScrollUpButton, {}),
        /* @__PURE__ */ jsx(
          SelectPrimitive.Viewport,
          {
            className: cn(
              "p-1",
              position === "popper" && "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"
            ),
            children
          }
        ),
        /* @__PURE__ */ jsx(SelectScrollDownButton, {})
      ]
    }
  ) });
}
function SelectItem({
  className,
  children,
  ...props
}) {
  return /* @__PURE__ */ jsxs(
    SelectPrimitive.Item,
    {
      "data-slot": "select-item",
      className: cn(
        "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
        className
      ),
      ...props,
      children: [
        /* @__PURE__ */ jsx("span", { className: "absolute right-2 flex size-3.5 items-center justify-center", children: /* @__PURE__ */ jsx(SelectPrimitive.ItemIndicator, { children: /* @__PURE__ */ jsx(CheckIcon, { className: "size-4" }) }) }),
        /* @__PURE__ */ jsx(SelectPrimitive.ItemText, { children })
      ]
    }
  );
}
function SelectScrollUpButton({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    SelectPrimitive.ScrollUpButton,
    {
      "data-slot": "select-scroll-up-button",
      className: cn(
        "flex cursor-default items-center justify-center py-1",
        className
      ),
      ...props,
      children: /* @__PURE__ */ jsx(ChevronUpIcon, { className: "size-4" })
    }
  );
}
function SelectScrollDownButton({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    SelectPrimitive.ScrollDownButton,
    {
      "data-slot": "select-scroll-down-button",
      className: cn(
        "flex cursor-default items-center justify-center py-1",
        className
      ),
      ...props,
      children: /* @__PURE__ */ jsx(ChevronDownIcon, { className: "size-4" })
    }
  );
}
const badgeVariants = cva(
  "inline-flex items-center justify-center rounded-full px-4 py-2 text-xs font-bold w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-2 [&>svg]:pointer-events-none transition-all duration-300 transform hover:scale-110 shadow-lg uppercase tracking-wider",
  {
    variants: {
      variant: {
        default: "bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",
        secondary: "bg-gray-100 text-black hover:bg-gray-200 shadow-gray-200 hover:shadow-gray-300 border border-gray-300",
        destructive: "bg-white text-black border-2 border-black hover:bg-black hover:text-white shadow-black/20 hover:shadow-black/40",
        outline: "border-2 border-black bg-white text-black hover:bg-black hover:text-white shadow-black/10 hover:shadow-black/30",
        success: "bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",
        warning: "bg-white text-black border border-gray-400 hover:bg-gray-100 shadow-gray-200 hover:shadow-gray-300"
      }
    },
    defaultVariants: {
      variant: "default"
    }
  }
);
function Badge({
  className,
  variant,
  asChild = false,
  ...props
}) {
  const Comp = asChild ? Slot : "span";
  return /* @__PURE__ */ jsx(
    Comp,
    {
      "data-slot": "badge",
      className: cn(badgeVariants({ variant }), className),
      ...props
    }
  );
}
function Progress({
  className,
  value,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    ProgressPrimitive.Root,
    {
      "data-slot": "progress",
      className: cn(
        "bg-gray-200 relative h-3 w-full overflow-hidden rounded-full shadow-inner",
        className
      ),
      ...props,
      children: /* @__PURE__ */ jsx(
        ProgressPrimitive.Indicator,
        {
          "data-slot": "progress-indicator",
          className: "bg-black h-full w-full flex-1 transition-all duration-500 ease-out rounded-full shadow-lg",
          style: { transform: `translateX(-${100 - (value || 0)}%)` }
        }
      )
    }
  );
}
function Separator({
  className,
  orientation = "horizontal",
  decorative = true,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    SeparatorPrimitive.Root,
    {
      "data-slot": "separator",
      decorative,
      orientation,
      className: cn(
        "bg-gray-200 shrink-0 data-[orientation=horizontal]:h-[2px] data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-[2px] rounded-full",
        className
      ),
      ...props
    }
  );
}
function Checkbox({
  className,
  ...props
}) {
  return /* @__PURE__ */ jsx(
    CheckboxPrimitive.Root,
    {
      "data-slot": "checkbox",
      className: cn(
        "peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        className
      ),
      ...props,
      children: /* @__PURE__ */ jsx(
        CheckboxPrimitive.Indicator,
        {
          "data-slot": "checkbox-indicator",
          className: "flex items-center justify-center text-current transition-none",
          children: /* @__PURE__ */ jsx(CheckIcon, { className: "size-3.5" })
        }
      )
    }
  );
}
class GeminiService {
  constructor(apiKey) {
    __publicField(this, "apiKey");
    __publicField(this, "baseUrl", "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent");
    this.apiKey = apiKey;
  }
  async comprehensiveSeoOptimization(products, options) {
    const results = [];
    const batchSize = 10;
    console.log(`🚀 Starting comprehensive SEO optimization for ${products.length} products`);
    console.log(`📊 Processing options:`, options);
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)} (${batch.length} products)`);
      try {
        const startTime = Date.now();
        const batchResults = await this.optimizeBatchComprehensive(batch, options);
        const processingTime = Date.now() - startTime;
        batchResults.forEach((result) => {
          result.processingTime = processingTime / batch.length;
        });
        results.push(...batchResults);
        if (i + batchSize < products.length) {
          console.log("⏳ Waiting 3 seconds before next batch...");
          await new Promise((resolve) => setTimeout(resolve, 3e3));
        }
      } catch (error) {
        console.error(`❌ Error processing batch starting at index ${i}:`, error);
        const fallbackResults = batch.map((product) => ({
          productId: product.id,
          originalProductTitle: product.title,
          originalProductDescription: product.description,
          originalSeoTitle: product.seoTitle || "",
          originalSeoDescription: product.seoDescription || "",
          originalHandle: product.handle || "",
          optimizedProductTitle: product.title,
          optimizedProductDescription: product.description,
          optimizedSeoTitle: product.seoTitle || product.title,
          optimizedSeoDescription: product.seoDescription || product.description,
          optimizedHandle: product.handle || "",
          targetKeywords: [],
          viralKeyword: "",
          seoScore: 0,
          competitorAnalysis: "",
          imageAltTexts: [],
          processingTime: 0,
          error: error instanceof Error ? error.message : "Batch processing failed"
        }));
        results.push(...fallbackResults);
      }
    }
    console.log(`✅ Comprehensive SEO optimization completed for ${results.length} products`);
    return results;
  }
  async optimizeBatchComprehensive(products, options, retryCount = 0) {
    const batchPrompt = this.createComprehensiveSeoPrompt(products, options);
    try {
      const response = await this.generateBatchContent(batchPrompt);
      return this.parseComprehensiveSeoResponse(response, products);
    } catch (error) {
      console.error(`❌ Comprehensive SEO optimization failed (attempt ${retryCount + 1}):`, error);
      if (error instanceof Error && error.message.includes("JSON parsing failed") && products.length > 1 && retryCount < 2) {
        console.log(`🔄 Retrying with smaller batch size: ${Math.ceil(products.length / 2)} products`);
        const midpoint = Math.ceil(products.length / 2);
        const batch1 = products.slice(0, midpoint);
        const batch2 = products.slice(midpoint);
        const results1 = await this.optimizeBatchComprehensive(batch1, options, retryCount + 1);
        const results2 = await this.optimizeBatchComprehensive(batch2, options, retryCount + 1);
        return [...results1, ...results2];
      }
      throw error;
    }
  }
  createComprehensiveSeoPrompt(products, options) {
    const prompt = `You are an expert SEO analyst and content strategist. Perform comprehensive SEO optimization for ${products.length} products.

CRITICAL INSTRUCTIONS:
1. Analyze each product deeply to identify viral keywords and ranking opportunities
2. Research trending search terms and competitor strategies
3. Create compelling, search-optimized content that drives conversions
4. Focus on user intent and search behavior patterns

For each product, provide a JSON object with this EXACT structure:
{
  "productId": "the_product_id",
  "optimizedProductTitle": "compelling product title with viral keywords",
  "optimizedProductDescription": "detailed product description optimized for conversions",
  "optimizedSeoTitle": "search-optimized page title (max 70 chars)",
  "optimizedSeoDescription": "meta description with call-to-action (max 160 chars)",
  "optimizedHandle": "seo-friendly-url-handle",
  "targetKeywords": ["keyword1", "keyword2", "keyword3"],
  "viralKeyword": "main viral keyword to rank for",
  "seoScore": 85,
  "competitorAnalysis": "brief analysis of competitive landscape",
  "imageAltTexts": ["alt text 1", "alt text 2", "alt text 3"]
}

OPTIMIZATION REQUIREMENTS:
- Product Title: Include viral keywords, benefits, and emotional triggers
- Product Description: Detailed, benefit-focused, includes social proof elements
- SEO Title: Keyword-rich, under 70 characters, includes power words
- Meta Description: Compelling, includes CTA, under 160 characters
- URL Handle: Clean, keyword-rich, SEO-friendly
- Keywords: Mix of high-volume and long-tail keywords
- Viral Keyword: The ONE keyword with highest ranking potential
- SEO Score: Rate optimization quality (1-100)
- Competitor Analysis: Brief market positioning insights
- Image Alt Texts: Descriptive, keyword-rich alt texts for product images

Products to optimize:
${products.map((product, index) => `
${index + 1}. Product ID: ${product.id}
   Current Title: ${product.title}
   Current Description: ${product.description}
   Product Type: ${product.type}
   Vendor: ${product.vendor}
   Current SEO Title: ${product.seoTitle || "Not set"}
   Current SEO Description: ${product.seoDescription || "Not set"}
   Current Handle: ${product.handle || "Not set"}
`).join("")}

CRITICAL: Respond with ONLY a complete, valid JSON array containing exactly ${products.length} objects.
- Start with [ and end with ]
- Ensure all strings are properly quoted and escaped
- Do not truncate any responses
- Do not add any text before or after the JSON
- Each object must be complete with all required fields

JSON array:`;
    return prompt;
  }
  async generateBatchContent(prompt) {
    var _a2;
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        // Lower temperature for more consistent JSON
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 8192
        // Significantly increased to prevent truncation
      },
      safetySettings: [
        {
          category: "HARM_CATEGORY_HARASSMENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_HATE_SPEECH",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
          category: "HARM_CATEGORY_DANGEROUS_CONTENT",
          threshold: "BLOCK_MEDIUM_AND_ABOVE"
        }
      ]
    };
    const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(requestBody)
    });
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`Gemini API error: ${response.status} - ${((_a2 = errorData.error) == null ? void 0 : _a2.message) || response.statusText}`);
    }
    const data = await response.json();
    if (!data.candidates || data.candidates.length === 0) {
      throw new Error("No response generated from Gemini API");
    }
    const generatedText = data.candidates[0].content.parts[0].text;
    return generatedText.trim();
  }
  parseComprehensiveSeoResponse(response, products) {
    try {
      console.log("🔍 Parsing comprehensive SEO response...");
      let jsonStr = response;
      jsonStr = jsonStr.replace(/```json\s*/g, "").replace(/```\s*/g, "");
      const jsonMatch = jsonStr.match(/\[[\s\S]*\]/);
      if (jsonMatch) {
        jsonStr = jsonMatch[0];
      }
      jsonStr = this.fixTruncatedJson(jsonStr);
      let parsedResults;
      try {
        parsedResults = JSON.parse(jsonStr);
      } catch (parseError) {
        console.error("❌ Failed to parse JSON:", parseError);
        console.error("Raw JSON string (first 500 chars):", jsonStr.substring(0, 500));
        console.error("Raw JSON string (last 500 chars):", jsonStr.substring(Math.max(0, jsonStr.length - 500)));
        const fixedJson = this.attemptJsonFix(jsonStr);
        try {
          parsedResults = JSON.parse(fixedJson);
          console.log("✅ Successfully parsed after JSON fix");
        } catch (secondError) {
          throw new Error(`JSON parsing failed: ${parseError instanceof Error ? parseError.message : "Unknown error"}`);
        }
      }
      if (!Array.isArray(parsedResults)) {
        throw new Error("Response is not an array");
      }
      console.log(`✅ Successfully parsed ${parsedResults.length} SEO optimization results`);
      const results = products.map((product) => {
        const aiResult = parsedResults.find((r) => r.productId === product.id);
        if (aiResult) {
          return {
            productId: product.id,
            // Original data
            originalProductTitle: product.title,
            originalProductDescription: product.description,
            originalSeoTitle: product.seoTitle || "",
            originalSeoDescription: product.seoDescription || "",
            originalHandle: product.handle || "",
            // Optimized content
            optimizedProductTitle: aiResult.optimizedProductTitle || product.title,
            optimizedProductDescription: aiResult.optimizedProductDescription || product.description,
            optimizedSeoTitle: aiResult.optimizedSeoTitle || product.title,
            optimizedSeoDescription: aiResult.optimizedSeoDescription || product.description,
            optimizedHandle: aiResult.optimizedHandle || product.handle || "",
            // SEO analysis
            targetKeywords: aiResult.targetKeywords || [],
            viralKeyword: aiResult.viralKeyword || "",
            seoScore: aiResult.seoScore || 0,
            competitorAnalysis: aiResult.competitorAnalysis || "",
            imageAltTexts: aiResult.imageAltTexts || [],
            processingTime: 0
            // Will be set by caller
          };
        } else {
          return {
            productId: product.id,
            originalProductTitle: product.title,
            originalProductDescription: product.description,
            originalSeoTitle: product.seoTitle || "",
            originalSeoDescription: product.seoDescription || "",
            originalHandle: product.handle || "",
            optimizedProductTitle: product.title,
            optimizedProductDescription: product.description,
            optimizedSeoTitle: product.seoTitle || product.title,
            optimizedSeoDescription: product.seoDescription || product.description,
            optimizedHandle: product.handle || "",
            targetKeywords: [],
            viralKeyword: "",
            seoScore: 0,
            competitorAnalysis: "",
            imageAltTexts: [],
            processingTime: 0,
            error: "AI did not provide optimization for this product"
          };
        }
      });
      return results;
    } catch (error) {
      console.error("❌ Failed to parse comprehensive SEO response:", error);
      console.error("Raw response:", response.substring(0, 500) + "...");
      return products.map((product) => ({
        productId: product.id,
        originalProductTitle: product.title,
        originalProductDescription: product.description,
        originalSeoTitle: product.seoTitle || "",
        originalSeoDescription: product.seoDescription || "",
        originalHandle: product.handle || "",
        optimizedProductTitle: product.title,
        optimizedProductDescription: product.description,
        optimizedSeoTitle: product.seoTitle || product.title,
        optimizedSeoDescription: product.seoDescription || product.description,
        optimizedHandle: product.handle || "",
        targetKeywords: [],
        viralKeyword: "",
        seoScore: 0,
        competitorAnalysis: "",
        imageAltTexts: [],
        processingTime: 0,
        error: "Failed to parse AI response"
      }));
    }
  }
  static validateApiKey(apiKey) {
    return apiKey.length > 20 && apiKey.startsWith("AIza");
  }
  async testConnection() {
    var _a2;
    try {
      const testProduct = {
        id: "test",
        title: "Test Product",
        description: "This is a test product",
        type: "Test",
        vendor: "Test Vendor"
      };
      const results = await this.comprehensiveSeoOptimization([testProduct], {
        updateProductTitle: true,
        updateProductDescription: true,
        updateSeoFields: true,
        updateHandle: true,
        updateImageAlts: true
      });
      if (results.length > 0 && !results[0].error) {
        return { success: true };
      } else {
        return { success: false, error: ((_a2 = results[0]) == null ? void 0 : _a2.error) || "Test failed" };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Connection test failed"
      };
    }
  }
  fixTruncatedJson(jsonStr) {
    let cleaned = jsonStr.trim();
    if (!cleaned.endsWith("]") && !cleaned.endsWith("}")) {
      console.log("🔧 Detected truncated JSON, attempting to fix...");
      const lastClosingBrace = cleaned.lastIndexOf("}");
      if (lastClosingBrace !== -1) {
        cleaned = cleaned.substring(0, lastClosingBrace + 1);
        if (cleaned.trim().startsWith("[")) {
          if (cleaned.trim().endsWith(",")) {
            cleaned = cleaned.trim().slice(0, -1);
          }
          cleaned = cleaned + "\n]";
        }
      }
    }
    cleaned = cleaned.replace(/:\s*"[^"]*$/, ': ""');
    cleaned = cleaned.replace(/,\s*"[^"]*$/, "");
    cleaned = cleaned.replace(/:\s*\[[^\]]*$/, ": []");
    if (cleaned.startsWith("[") && !cleaned.endsWith("]")) {
      cleaned = cleaned.replace(/,\s*$/, "");
      cleaned = cleaned + "\n]";
    }
    return cleaned;
  }
  attemptJsonFix(jsonStr) {
    let fixed = jsonStr.trim();
    console.log("🔧 Attempting advanced JSON fix...");
    fixed = fixed.replace(/:\s*"[^"]*$/, ': "incomplete_value_removed"');
    fixed = fixed.replace(/:\s*\d+\.?\d*[^\d\s,}\]]*$/, ": 0");
    fixed = fixed.replace(/:\s*\[[^\]]*$/, ": []");
    fixed = fixed.replace(/:\s*\{[^}]*$/, ": {}");
    fixed = fixed.replace(/,\s*"[^"]*$/, "");
    fixed = fixed.replace(/,\s*$/, "");
    if (fixed.startsWith("[")) {
      const lastClosingBrace = fixed.lastIndexOf("}");
      if (lastClosingBrace !== -1) {
        fixed = fixed.substring(0, lastClosingBrace + 1);
        if (fixed.trim().endsWith(",")) {
          fixed = fixed.trim().slice(0, -1);
        }
        if (!fixed.endsWith("]")) {
          fixed += "\n]";
        }
      } else {
        fixed = "[]";
      }
    }
    fixed = fixed.replace(/\n/g, "\\n");
    fixed = fixed.replace(/\r/g, "\\r");
    fixed = fixed.replace(/\t/g, "\\t");
    if (!fixed.startsWith("[") && !fixed.startsWith("{")) {
      console.log("⚠️ JSON doesn't start with [ or {, wrapping in array");
      fixed = "[" + fixed + "]";
    }
    console.log("✅ JSON fix completed");
    return fixed;
  }
}
class CreditsService {
  constructor(shop) {
    __publicField(this, "shop");
    this.shop = shop;
  }
  convertToNumber(value) {
    if (value === null || value === void 0) return 0;
    if (typeof value === "bigint") return Number(value);
    if (typeof value === "number") return value;
    if (typeof value === "string") return parseFloat(value) || 0;
    return 0;
  }
  convertToString(value) {
    if (value === null || value === void 0) return void 0;
    return String(value);
  }
  convertToDate(value) {
    if (value === null || value === void 0) return /* @__PURE__ */ new Date();
    if (value instanceof Date) return value;
    if (typeof value === "string") return new Date(value);
    return /* @__PURE__ */ new Date();
  }
  /**
   * Get current credit balance for the shop
   */
  async getCreditBalance() {
    try {
      const transactions = await db.billingUsage.findMany({
        where: { shop: this.shop },
        orderBy: { optimizationDate: "desc" }
      });
      const purchases = await db.billingPurchase.findMany({
        where: {
          shop: this.shop,
          status: { in: ["ACCEPTED", "PENDING"] }
          // Include pending purchases for immediate credit display
        }
      });
      const totalCredits = purchases.reduce((sum, purchase) => {
        const count = this.convertToNumber(purchase.productCount);
        return sum + count;
      }, 0);
      const usedCredits = transactions.reduce((sum, usage) => {
        const optimized = this.convertToNumber(usage.productsOptimized);
        return sum + optimized;
      }, 0);
      const remainingCredits = Math.max(0, totalCredits - usedCredits);
      return {
        totalCredits,
        usedCredits,
        remainingCredits,
        lastUpdated: /* @__PURE__ */ new Date()
      };
    } catch (error) {
      console.error("Error getting credit balance:", error);
      return {
        totalCredits: 0,
        usedCredits: 0,
        remainingCredits: 0,
        lastUpdated: /* @__PURE__ */ new Date()
      };
    }
  }
  /**
   * Add credits when a purchase is made
   */
  async addCredits(amount, purchaseId, _description) {
    try {
      console.log(`💳 Adding ${amount} credits for shop ${this.shop} from purchase ${purchaseId}`);
      console.log(`✅ Credits added successfully for shop ${this.shop}`);
    } catch (error) {
      console.error("Error adding credits:", error);
      throw error;
    }
  }
  /**
   * Use credits for optimization
   */
  async useCredits(amount, _description, batchId) {
    try {
      const balance = await this.getCreditBalance();
      if (balance.remainingCredits < amount) {
        console.log(`❌ Insufficient credits for shop ${this.shop}. Required: ${amount}, Available: ${balance.remainingCredits}`);
        return false;
      }
      await db.billingUsage.create({
        data: {
          shop: this.shop,
          billingType: "pay_per_use",
          productsOptimized: amount,
          optimizationDate: /* @__PURE__ */ new Date(),
          batchId: batchId || `batch_${Date.now()}`
        }
      });
      console.log(`✅ Used ${amount} credits for shop ${this.shop}. Remaining: ${balance.remainingCredits - amount}`);
      return true;
    } catch (error) {
      console.error("Error using credits:", error);
      throw error;
    }
  }
  /**
   * Check if shop has enough credits for optimization
   */
  async hasEnoughCredits(requiredAmount) {
    try {
      const balance = await this.getCreditBalance();
      return balance.remainingCredits >= requiredAmount;
    } catch (error) {
      console.error("Error checking credit balance:", error);
      return false;
    }
  }
  /**
   * Get credit transaction history
   */
  async getCreditHistory() {
    try {
      const history = [];
      const purchases = await db.billingPurchase.findMany({
        where: { shop: this.shop },
        orderBy: { createdAt: "desc" }
      });
      purchases.forEach((purchase) => {
        const statusText = purchase.status === "PENDING" ? " (Processing)" : "";
        const amount = this.convertToNumber(purchase.productCount);
        history.push({
          type: "PURCHASE",
          amount,
          description: `Purchased ${amount} optimization credits${statusText}`,
          date: this.convertToDate(purchase.createdAt),
          referenceId: this.convertToString(purchase.purchaseId)
        });
      });
      const usage = await db.billingUsage.findMany({
        where: { shop: this.shop },
        orderBy: { optimizationDate: "desc" }
      });
      usage.forEach((use) => {
        const optimized = this.convertToNumber(use.productsOptimized);
        history.push({
          type: "USAGE",
          amount: -optimized,
          description: `Optimized ${optimized} products`,
          date: this.convertToDate(use.optimizationDate),
          referenceId: this.convertToString(use.batchId) || void 0
        });
      });
      return history.sort((a, b) => b.date.getTime() - a.date.getTime());
    } catch (error) {
      console.error("Error getting credit history:", error);
      return [];
    }
  }
  /**
   * Get subscription status and unlimited access
   */
  async hasUnlimitedAccess() {
    try {
      const activeSubscription = await db.billingSubscription.findFirst({
        where: {
          shop: this.shop,
          status: "ACTIVE"
        }
      });
      return !!activeSubscription;
    } catch (error) {
      console.error("Error checking subscription status:", error);
      return false;
    }
  }
  /**
   * Check if user can optimize products (either has subscription or credits)
   */
  async canOptimizeProducts(productCount) {
    try {
      const hasSubscription = await this.hasUnlimitedAccess();
      if (hasSubscription) {
        return {
          canOptimize: true,
          hasSubscription: true
        };
      }
      const balance = await this.getCreditBalance();
      const hasEnoughCredits = balance.remainingCredits >= productCount;
      return {
        canOptimize: hasEnoughCredits,
        hasSubscription: false,
        creditsAvailable: balance.remainingCredits,
        reason: hasEnoughCredits ? void 0 : `Insufficient credits. You need ${productCount} credits but only have ${balance.remainingCredits}.`
      };
    } catch (error) {
      console.error("Error checking optimization access:", error);
      return {
        canOptimize: false,
        reason: "Error checking access permissions"
      };
    }
  }
}
class GraphQLService {
  constructor(admin, shop) {
    __publicField(this, "admin");
    __publicField(this, "shop");
    __publicField(this, "maxQueryCost", 1e3);
    // Shopify's default limit
    __publicField(this, "maxRetries", 3);
    this.admin = admin;
    this.shop = shop;
  }
  /**
   * Execute a single GraphQL query with comprehensive error handling
   */
  async query(query, options = {}, context) {
    var _a2;
    const { variables, retries = this.maxRetries, timeout = 3e4, skipRateLimit = false } = options;
    if (!skipRateLimit) {
      try {
        await applyRateLimit(RATE_LIMITERS.API_GENERAL(this.shop), context);
      } catch (error) {
        throw createError("RATE_LIMIT_EXCEEDED", { ...context, shop: this.shop });
      }
    }
    let lastError = null;
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔍 GraphQL Query (attempt ${attempt}/${retries}) for shop: ${this.shop}`);
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error("GraphQL query timeout")), timeout);
        });
        const queryPromise = this.admin.graphql(query, variables ? { variables } : void 0);
        const response = await Promise.race([queryPromise, timeoutPromise]);
        const result = await response.json();
        if (result.errors && result.errors.length > 0) {
          const errorMessages = result.errors.map((err) => err.message).join(", ");
          console.warn(`⚠️ GraphQL errors: ${errorMessages}`);
          const rateLimitError = result.errors.find(
            (err) => {
              var _a3;
              return ((_a3 = err.extensions) == null ? void 0 : _a3.code) === "THROTTLED" || err.message.includes("throttled") || err.message.includes("rate limit");
            }
          );
          if (rateLimitError && attempt < retries) {
            const delay = Math.pow(2, attempt) * 1e3;
            console.log(`⏳ Rate limited, waiting ${delay}ms before retry...`);
            await new Promise((resolve) => setTimeout(resolve, delay));
            continue;
          }
          const costError = result.errors.find(
            (err) => err.message.includes("query cost") || err.message.includes("complexity")
          );
          if (costError) {
            throw createError("GRAPHQL_QUERY_TOO_COMPLEX", {
              ...context,
              shop: this.shop,
              metadata: { error: costError.message }
            });
          }
        }
        if ((_a2 = result.extensions) == null ? void 0 : _a2.cost) {
          const cost = result.extensions.cost;
          console.log(`📊 Query cost: ${cost.actualQueryCost}/${cost.requestedQueryCost} (available: ${cost.throttleStatus.currentlyAvailable}/${cost.throttleStatus.maximumAvailable})`);
          if (cost.throttleStatus.currentlyAvailable < 100) {
            console.warn(`⚠️ GraphQL rate limit approaching: ${cost.throttleStatus.currentlyAvailable} points remaining`);
          }
        }
        console.log(`✅ GraphQL query successful for shop: ${this.shop}`);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error("GraphQL query failed");
        console.error(`❌ GraphQL query failed (attempt ${attempt}/${retries}):`, error);
        if (error instanceof Error) {
          if (error.message.includes("authentication") || error.message.includes("unauthorized") || error.message.includes("forbidden")) {
            break;
          }
        }
        if (attempt < retries) {
          const delay = Math.pow(2, attempt) * 1e3;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    await logError(lastError, {
      ...context,
      shop: this.shop,
      action: "graphql_query",
      metadata: { query: query.substring(0, 200) }
      // Log first 200 chars of query
    });
    throw lastError;
  }
  /**
   * Execute multiple GraphQL operations in batches to avoid query cost limits
   */
  async batchQuery(operations, batchSize = 5, context) {
    var _a2;
    const results = [];
    console.log(`🔄 Executing ${operations.length} GraphQL operations in batches of ${batchSize}`);
    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(operations.length / batchSize)}`);
      const batchPromises = batch.map(
        (operation) => this.query(operation.query, {
          variables: operation.variables,
          skipRateLimit: true
          // We apply rate limiting at batch level
        }, context)
      );
      try {
        const batchResults = await Promise.allSettled(batchPromises);
        for (const result of batchResults) {
          if (result.status === "fulfilled") {
            results.push(result.value);
          } else {
            console.error("❌ Batch operation failed:", result.reason);
            results.push({
              errors: [{ message: ((_a2 = result.reason) == null ? void 0 : _a2.message) || "Batch operation failed" }]
            });
          }
        }
        if (i + batchSize < operations.length) {
          await new Promise((resolve) => setTimeout(resolve, 1e3));
        }
      } catch (error) {
        console.error("❌ Batch execution failed:", error);
        await logError(
          error instanceof Error ? error : new Error("Batch GraphQL execution failed"),
          { ...context, shop: this.shop, action: "graphql_batch_query" }
        );
        throw error;
      }
    }
    console.log(`✅ Completed ${operations.length} GraphQL operations`);
    return results;
  }
  /**
   * Get products with cursor-based pagination to handle large datasets
   */
  async getAllProducts(searchQuery, maxProducts = 1e3, context) {
    var _a2, _b;
    const allProducts = [];
    let hasNextPage = true;
    let cursor = null;
    let batchCount = 0;
    const maxBatches = Math.ceil(maxProducts / 50);
    console.log(`📦 Fetching products (max: ${maxProducts}) for shop: ${this.shop}`);
    while (hasNextPage && batchCount < maxBatches) {
      batchCount++;
      const query = `
        query getProducts($first: Int!, $after: String, $query: String) {
          products(first: $first, after: $after, query: $query) {
            edges {
              node {
                id
                title
                description
                handle
                status
                seo {
                  title
                  description
                }
                images(first: 5) {
                  edges {
                    node {
                      id
                      altText
                      url
                    }
                  }
                }
              }
              cursor
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;
      try {
        const result = await this.query(query, {
          variables: {
            first: Math.min(50, maxProducts - allProducts.length),
            after: cursor,
            query: searchQuery
          }
        }, context);
        if ((_b = (_a2 = result.data) == null ? void 0 : _a2.products) == null ? void 0 : _b.edges) {
          const products = result.data.products.edges.map((edge) => edge.node);
          allProducts.push(...products);
          hasNextPage = result.data.products.pageInfo.hasNextPage;
          cursor = result.data.products.pageInfo.endCursor;
          console.log(`📦 Fetched batch ${batchCount}: ${products.length} products (total: ${allProducts.length})`);
        } else {
          console.warn("⚠️ No products data in response");
          break;
        }
        if (allProducts.length >= maxProducts) {
          console.log(`🛑 Reached maximum product limit: ${maxProducts}`);
          break;
        }
      } catch (error) {
        console.error(`❌ Failed to fetch product batch ${batchCount}:`, error);
        break;
      }
    }
    console.log(`✅ Fetched ${allProducts.length} products total`);
    return allProducts.slice(0, maxProducts);
  }
  /**
   * Update products in batches with proper error handling
   */
  async updateProductsBatch(updates, batchSize = 10, context) {
    var _a2, _b, _c, _d;
    const results = { success: 0, failed: 0, errors: [] };
    console.log(`🔄 Updating ${updates.length} products in batches of ${batchSize}`);
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      const operations = batch.map((update) => ({
        query: `
          mutation updateProduct($input: ProductInput!) {
            productUpdate(input: $input) {
              product {
                id
                title
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: { input: { id: update.id, ...update.input } }
      }));
      try {
        const batchResults = await this.batchQuery(operations, batchSize, context);
        for (const result of batchResults) {
          if ((_b = (_a2 = result.data) == null ? void 0 : _a2.productUpdate) == null ? void 0 : _b.product) {
            results.success++;
          } else {
            results.failed++;
            const errors = ((_d = (_c = result.data) == null ? void 0 : _c.productUpdate) == null ? void 0 : _d.userErrors) || result.errors || [];
            const errorMessages = errors.map((err) => err.message).join(", ");
            results.errors.push(errorMessages);
          }
        }
      } catch (error) {
        console.error(`❌ Batch update failed:`, error);
        results.failed += batch.length;
        results.errors.push(error instanceof Error ? error.message : "Batch update failed");
      }
    }
    console.log(`✅ Product updates completed: ${results.success} success, ${results.failed} failed`);
    return results;
  }
}
function createGraphQLService(admin, shop) {
  return new GraphQLService(admin, shop);
}
const _SEOOptimizationService = class _SEOOptimizationService {
  constructor(admin, shop) {
    __publicField(this, "admin");
    __publicField(this, "shop");
    __publicField(this, "requestStore", /* @__PURE__ */ new Map());
    this.admin = admin;
    this.shop = shop;
  }
  static getInstance(admin, shop) {
    if (!this.instances.has(shop)) {
      this.instances.set(shop, new _SEOOptimizationService(admin, shop));
    }
    const instance = this.instances.get(shop);
    instance.admin = admin;
    return instance;
  }
  /**
   * Start SEO optimization with proper request deduplication
   */
  async startOptimization(productIds, settings, context) {
    try {
      await applyRateLimit(RATE_LIMITERS.SEO_OPTIMIZATION(this.shop), context);
      const existingRequest = await this.getActiveRequest();
      if (existingRequest) {
        throw createError("SEO_OPTIMIZATION_IN_PROGRESS", {
          ...context,
          shop: this.shop,
          metadata: { existingRequestId: existingRequest.id }
        });
      }
      const requestId = `seo_${this.shop}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const totalProducts = productIds.length;
      const totalBatches = Math.ceil(totalProducts / 10);
      const optimizationRequest = {
        id: requestId,
        shop: this.shop,
        productIds,
        settings,
        status: "pending",
        progress: {
          currentBatch: 0,
          totalBatches,
          processedProducts: 0,
          totalProducts,
          successCount: 0,
          errorCount: 0
        },
        createdAt: /* @__PURE__ */ new Date(),
        updatedAt: /* @__PURE__ */ new Date()
      };
      await this.storeOptimizationRequest(optimizationRequest);
      this.processOptimization(requestId).catch((error) => {
        console.error(`❌ SEO optimization failed for request ${requestId}:`, error);
        this.updateRequestStatus(requestId, "failed", error.message);
      });
      console.log(`🚀 Started SEO optimization request: ${requestId}`);
      return {
        requestId,
        message: `Started optimization for ${totalProducts} products`
      };
    } catch (error) {
      console.error("❌ Failed to start SEO optimization:", error);
      await logError(
        error instanceof Error ? error : new Error("SEO optimization start failed"),
        { ...context, shop: this.shop, action: "start_seo_optimization" }
      );
      throw error;
    }
  }
  /**
   * Get optimization request status and progress
   */
  async getOptimizationStatus(requestId) {
    try {
      return await this.getOptimizationRequest(requestId);
    } catch (error) {
      console.error(`❌ Failed to get optimization status for ${requestId}:`, error);
      return null;
    }
  }
  /**
   * Process SEO optimization in batches
   */
  async processOptimization(requestId) {
    var _a2, _b;
    try {
      const request = await this.getOptimizationRequest(requestId);
      if (!request) {
        throw new Error(`Optimization request ${requestId} not found`);
      }
      await this.updateRequestStatus(requestId, "processing");
      const graphqlService = createGraphQLService(this.admin, this.shop);
      const envConfig = getEnvironmentConfig();
      const geminiService = new GeminiService(envConfig.GEMINI_API_KEY || "");
      const results = [];
      const batchSize = 10;
      for (let i = 0; i < request.productIds.length; i += batchSize) {
        const batchIds = request.productIds.slice(i, i + batchSize);
        const currentBatch = Math.floor(i / batchSize) + 1;
        console.log(`📦 Processing batch ${currentBatch}/${request.progress.totalBatches}`);
        try {
          const products = await this.fetchProductsBatch(batchIds, graphqlService);
          for (const product of products) {
            try {
              const result = await this.optimizeProduct(product, geminiService, request.settings);
              results.push(result);
              await this.updateProgress(requestId, {
                currentBatch,
                processedProducts: results.length,
                successCount: results.filter((r) => !r.error).length,
                errorCount: results.filter((r) => r.error).length
              });
            } catch (error) {
              console.error(`❌ Failed to optimize product ${product.id}:`, error);
              results.push({
                productId: product.id,
                originalProductTitle: product.title,
                originalProductDescription: product.description,
                originalSeoTitle: ((_a2 = product.seo) == null ? void 0 : _a2.title) || "",
                originalSeoDescription: ((_b = product.seo) == null ? void 0 : _b.description) || "",
                originalHandle: product.handle,
                error: error instanceof Error ? error.message : "Optimization failed"
              });
            }
          }
          if (request.settings.autoApply) {
            await this.applyOptimizations(results.filter((r) => !r.error), graphqlService);
          }
          if (i + batchSize < request.productIds.length) {
            await new Promise((resolve) => setTimeout(resolve, 1e3));
          }
        } catch (error) {
          console.error(`❌ Failed to process batch ${currentBatch}:`, error);
        }
      }
      await this.completeOptimization(requestId, results);
    } catch (error) {
      console.error(`❌ SEO optimization processing failed for ${requestId}:`, error);
      await this.updateRequestStatus(requestId, "failed", error instanceof Error ? error.message : "Processing failed");
    }
  }
  /**
   * Fetch products in batch using GraphQL service
   */
  async fetchProductsBatch(productIds, graphqlService) {
    const operations = productIds.map((id) => ({
      query: `
        query getProduct {
          product(id: "gid://shopify/Product/${id}") {
            id
            title
            description
            productType
            vendor
            handle
            seo {
              title
              description
            }
            images(first: 5) {
              edges {
                node {
                  id
                  altText
                }
              }
            }
          }
        }
      `
    }));
    const results = await graphqlService.batchQuery(operations, 5);
    return results.map((result) => {
      var _a2;
      return (_a2 = result.data) == null ? void 0 : _a2.product;
    }).filter((product) => product !== null);
  }
  /**
   * Optimize a single product using Gemini
   */
  async optimizeProduct(product, geminiService, settings) {
    var _a2, _b;
    const result = {
      productId: product.id.replace("gid://shopify/Product/", ""),
      originalProductTitle: product.title,
      originalProductDescription: product.description || "",
      originalSeoTitle: ((_a2 = product.seo) == null ? void 0 : _a2.title) || "",
      originalSeoDescription: ((_b = product.seo) == null ? void 0 : _b.description) || "",
      originalHandle: product.handle
    };
    try {
      const optimizations = await geminiService.comprehensiveSeoOptimization([{
        id: product.id,
        title: product.title,
        description: product.description || "",
        type: product.productType || "",
        vendor: product.vendor || ""
      }], settings);
      const optimization = optimizations[0];
      if (settings.updateProductTitle && optimization.optimizedProductTitle) {
        result.optimizedProductTitle = optimization.optimizedProductTitle;
      }
      if (settings.updateProductDescription && optimization.optimizedProductDescription) {
        result.optimizedProductDescription = optimization.optimizedProductDescription;
      }
      if (settings.updateSeoFields) {
        if (optimization.optimizedSeoTitle) {
          result.optimizedSeoTitle = optimization.optimizedSeoTitle;
        }
        if (optimization.optimizedSeoDescription) {
          result.optimizedSeoDescription = optimization.optimizedSeoDescription;
        }
      }
      if (settings.updateHandle && optimization.optimizedHandle) {
        result.optimizedHandle = optimization.optimizedHandle;
      }
      result.viralKeyword = optimization.viralKeyword;
      result.seoScore = optimization.seoScore;
    } catch (error) {
      result.error = error instanceof Error ? error.message : "Optimization failed";
    }
    return result;
  }
  /**
   * Apply optimizations using GraphQL service
   */
  async applyOptimizations(results, graphqlService) {
    const updates = results.map((result) => ({
      id: `gid://shopify/Product/${result.productId}`,
      input: {
        ...result.optimizedProductTitle && { title: result.optimizedProductTitle },
        ...result.optimizedProductDescription && { descriptionHtml: result.optimizedProductDescription },
        ...result.optimizedHandle && { handle: result.optimizedHandle },
        ...(result.optimizedSeoTitle || result.optimizedSeoDescription) && {
          seo: {
            ...result.optimizedSeoTitle && { title: result.optimizedSeoTitle },
            ...result.optimizedSeoDescription && { description: result.optimizedSeoDescription }
          }
        }
      }
    }));
    await graphqlService.updateProductsBatch(updates, 5);
  }
  // Database operations (simplified - would need proper implementation)
  async storeOptimizationRequest(request) {
    console.log(`💾 Storing optimization request: ${request.id}`);
    console.log(`📊 Current store size: ${this.requestStore.size}`);
    this.requestStore.set(request.id, request);
    console.log(`📊 Store size after adding: ${this.requestStore.size}`);
  }
  async getOptimizationRequest(requestId) {
    console.log(`📖 Getting optimization request: ${requestId}`);
    console.log(`📊 Current store size: ${this.requestStore.size}`);
    console.log(`📊 Available request IDs: ${Array.from(this.requestStore.keys()).join(", ")}`);
    const request = this.requestStore.get(requestId);
    console.log(`📊 Found request: ${request ? "YES" : "NO"}`);
    return request || null;
  }
  async getActiveRequest() {
    console.log(`🔍 Checking for active requests for shop: ${this.shop}`);
    return null;
  }
  async updateRequestStatus(requestId, status, error) {
    console.log(`📝 Updating request ${requestId} status to: ${status}`);
    const request = this.requestStore.get(requestId);
    if (request) {
      request.status = status;
      if (error) {
        request.error = error;
      }
      this.requestStore.set(requestId, request);
    }
  }
  async updateProgress(requestId, progress) {
    console.log(`📊 Updating progress for request ${requestId}:`, progress);
    const request = this.requestStore.get(requestId);
    if (request) {
      request.progress = { ...request.progress, ...progress };
      this.requestStore.set(requestId, request);
    }
  }
  async completeOptimization(requestId, _results) {
    console.log(`✅ Completing optimization request: ${requestId}`);
    await this.updateRequestStatus(requestId, "completed");
  }
};
__publicField(_SEOOptimizationService, "instances", /* @__PURE__ */ new Map());
let SEOOptimizationService = _SEOOptimizationService;
function createSEOOptimizationService(admin, shop) {
  return SEOOptimizationService.getInstance(admin, shop);
}
function SeoOptimizationHeader({
  totalProducts,
  selectedCount,
  averageSeoScore,
  onOptimizeSelected,
  onSelectAll,
  isOptimizing
}) {
  return /* @__PURE__ */ jsx("div", { className: "bg-black text-white", children: /* @__PURE__ */ jsx("div", { className: "bg-black py-20 px-6", children: /* @__PURE__ */ jsxs("div", { className: "max-w-6xl mx-auto text-center", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center mb-12", children: [
      /* @__PURE__ */ jsx(
        "img",
        {
          src: "/logo.png",
          alt: "AI BULK SEO Logo",
          className: "w-16 h-16 mb-6 rounded-2xl shadow-2xl",
          style: { filter: "brightness(1.1) contrast(1.1)" }
        }
      ),
      /* @__PURE__ */ jsx("div", { className: "text-sm font-semibold tracking-widest uppercase text-white/70 mb-4", children: "AI BULK SEO" }),
      /* @__PURE__ */ jsx("h1", { style: {
        fontSize: "clamp(3rem, 8vw, 6rem)",
        fontWeight: 900,
        lineHeight: 0.9,
        letterSpacing: "-0.05em",
        marginBottom: "1rem",
        color: "white"
      }, children: "OPTIMIZE" }),
      /* @__PURE__ */ jsx("p", { style: {
        fontSize: "clamp(1.25rem, 3vw, 1.75rem)",
        fontWeight: 300,
        color: "#cbd5e1",
        maxWidth: "40rem",
        margin: "0 auto"
      }, children: "AI-powered SEO optimization for your Shopify products" })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-12", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2", children: totalProducts.toLocaleString() }),
        /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Total Products" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2", children: selectedCount.toLocaleString() }),
        /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Selected" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-4xl font-bold mb-2", children: [
          Math.round(averageSeoScore),
          "%"
        ] }),
        /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Avg SEO Score" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row gap-4 justify-center", children: [
      /* @__PURE__ */ jsx(
        Button,
        {
          onClick: onOptimizeSelected,
          disabled: selectedCount === 0 || isOptimizing,
          className: "bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl",
          children: isOptimizing ? "Optimizing..." : `Optimize ${selectedCount} Selected`
        }
      ),
      /* @__PURE__ */ jsx(
        Button,
        {
          onClick: onSelectAll,
          className: "bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl",
          children: "Select All Products"
        }
      )
    ] })
  ] }) }) });
}
function BillingStatus({
  subscription,
  plan,
  hasAccess,
  monthlyUsage
}) {
  const getStatusIcon = () => {
    if (!subscription) {
      return /* @__PURE__ */ jsx(CreditCard, { className: "w-5 h-5 text-gray-500" });
    }
    switch (subscription.status) {
      case "ACTIVE":
        return /* @__PURE__ */ jsx(CheckCircle, { className: "w-5 h-5 text-green-500" });
      case "PENDING":
        return /* @__PURE__ */ jsx(AlertTriangle, { className: "w-5 h-5 text-red-500" });
      default:
        return /* @__PURE__ */ jsx(CreditCard, { className: "w-5 h-5 text-gray-500" });
    }
  };
  const getStatusBadge = () => {
    if (!subscription) {
      return /* @__PURE__ */ jsx(Badge, { variant: "secondary", children: "No Plan" });
    }
    switch (subscription.status) {
      case "ACTIVE":
        return /* @__PURE__ */ jsx(Badge, { variant: "default", className: "bg-green-500", children: "Active" });
      case "PENDING":
        return /* @__PURE__ */ jsx(Badge, { variant: "destructive", children: "Payment Required" });
      case "CANCELLED":
        return /* @__PURE__ */ jsx(Badge, { variant: "secondary", children: "Cancelled" });
      default:
        return /* @__PURE__ */ jsx(Badge, { variant: "secondary", children: subscription.status });
    }
  };
  if (!hasAccess) {
    return /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
          /* @__PURE__ */ jsx(Star, { className: "w-5 h-5 text-white" }),
          /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-white", children: "Upgrade Required" })
        ] }),
        getStatusBadge()
      ] }),
      /* @__PURE__ */ jsx("p", { className: "text-white/70 mb-6", children: "Choose a plan to start optimizing your products with AI-powered SEO tools." }),
      /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 text-center", children: [
          /* @__PURE__ */ jsxs("div", { className: "p-3 bg-white/10 border border-white/20 rounded-2xl", children: [
            /* @__PURE__ */ jsx("div", { className: "text-lg font-bold text-white", children: "$199.99/year" }),
            /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Annual Plan" }),
            /* @__PURE__ */ jsx("div", { className: "text-xs text-green-400", children: "Best Value" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "p-3 bg-white/10 border border-white/20 rounded-2xl", children: [
            /* @__PURE__ */ jsx("div", { className: "text-lg font-bold text-white", children: "$19.99/month" }),
            /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Monthly Plan" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "p-3 bg-white/10 border border-white/20 rounded-2xl", children: [
            /* @__PURE__ */ jsx("div", { className: "text-lg font-bold text-white", children: "$0.10" }),
            /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Per Product" })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex gap-3", children: [
          /* @__PURE__ */ jsx(Button, { asChild: true, className: "flex-1 bg-white text-black hover:bg-gray-100", children: /* @__PURE__ */ jsx(Link, { to: "/app/billing/pricing", children: "Choose Plan" }) }),
          /* @__PURE__ */ jsx(Button, { asChild: true, className: "border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black", children: /* @__PURE__ */ jsx(Link, { to: "/app/billing", children: "View Details" }) })
        ] })
      ] })
    ] });
  }
  return /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
    /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-4", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
        getStatusIcon(),
        /* @__PURE__ */ jsx("h3", { className: "text-lg font-bold text-white", children: (plan == null ? void 0 : plan.name) || "Current Plan" })
      ] }),
      getStatusBadge()
    ] }),
    /* @__PURE__ */ jsx("p", { className: "text-white/70 mb-6", children: (plan == null ? void 0 : plan.description) || "Your billing plan and status" }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-4", children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Plan Type" }),
          /* @__PURE__ */ jsx("div", { className: "font-semibold text-white", children: (plan == null ? void 0 : plan.type) === "pay_per_use" ? "Pay-Per-Use" : (plan == null ? void 0 : plan.type) === "annual" ? "Annual Subscription" : "Monthly Subscription" })
        ] }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Cost" }),
          /* @__PURE__ */ jsx("div", { className: "font-semibold text-white", children: (plan == null ? void 0 : plan.type) === "pay_per_use" ? `$${plan.price} per product` : `$${plan == null ? void 0 : plan.price}/${(plan == null ? void 0 : plan.type) === "annual" ? "year" : "month"}` })
        ] })
      ] }),
      monthlyUsage && /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 gap-4 pt-2 border-t border-white/20", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-blue-400", children: monthlyUsage.productsOptimized }),
          /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: "Products Optimized" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsxs("div", { className: "text-2xl font-bold text-green-400", children: [
            "$",
            monthlyUsage.totalSpent.toFixed(2)
          ] }),
          /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70", children: (plan == null ? void 0 : plan.type) === "pay_per_use" ? "Total Spent" : "Plan Value" })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex gap-2 pt-2", children: [
        /* @__PURE__ */ jsx(Button, { asChild: true, className: "flex-1 h-10 px-6 py-2 text-xs border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black", children: /* @__PURE__ */ jsx(Link, { to: "/app/billing", children: "Manage Billing" }) }),
        (plan == null ? void 0 : plan.type) !== "annual" && /* @__PURE__ */ jsx(Button, { asChild: true, className: "h-10 px-6 py-2 text-xs bg-white text-black hover:bg-gray-100", children: /* @__PURE__ */ jsx(Link, { to: "/app/billing/pricing", children: "Upgrade" }) })
      ] })
    ] })
  ] });
}
function PayPerUseConfirmation({
  productCount,
  selectedProducts,
  onConfirm,
  onCancel,
  isProcessing = false,
  csrfToken
}) {
  const totalCost = productCount * 0.1;
  const fetcher = useFetcher();
  React.useEffect(() => {
    console.log("🔄 PayPerUse fetcher state:", fetcher.state);
    console.log("📋 PayPerUse fetcher data:", fetcher.data);
    if (fetcher.data && typeof fetcher.data === "object") {
      const data = fetcher.data;
      if (data.error) {
        console.error("❌ Pay-per-use billing error:", data.error);
        alert(`Payment Error: ${data.error}`);
      } else if (data.success && data.confirmationUrl) {
        console.log("✅ Pay-per-use purchase created successfully, redirecting...");
        try {
          if (window.top) {
            window.top.location.href = data.confirmationUrl;
          } else {
            window.location.href = data.confirmationUrl;
          }
        } catch (error) {
          console.error("❌ Failed to redirect to confirmation URL:", error);
          alert("Purchase created successfully, but failed to redirect. Please refresh the page.");
        }
      }
    }
  }, [fetcher.data, fetcher.state]);
  const handlePayAndOptimize = () => {
    if (!productCount || productCount <= 0) {
      alert("Invalid product count");
      return;
    }
    if (!selectedProducts || selectedProducts.length === 0) {
      alert("No products selected");
      return;
    }
    if (selectedProducts.length !== productCount) {
      alert("Selected products count does not match product count");
      return;
    }
    if (fetcher.state === "submitting" || fetcher.state === "loading") {
      console.log("⏳ Already processing purchase request...");
      return;
    }
    console.log("🔄 Creating pay-per-use purchase:", {
      productCount,
      selectedProductsCount: selectedProducts.length,
      totalCost
    });
    const formattedProductIds = selectedProducts.map(
      (id) => id.startsWith("gid://shopify/Product/") ? id : `gid://shopify/Product/${id}`
    );
    console.log("🔄 Converting product IDs to GID format:", {
      original: selectedProducts.slice(0, 3),
      formatted: formattedProductIds.slice(0, 3)
    });
    const formData = new FormData();
    formData.append("action", "create_pay_per_use_purchase");
    formData.append("productCount", productCount.toString());
    formData.append("selectedProducts", JSON.stringify(formattedProductIds));
    formData.append("csrfToken", csrfToken);
    console.log("🔐 PayPerUseConfirmation - Submitting with CSRF token");
    console.log("🔐 CSRF token length:", (csrfToken == null ? void 0 : csrfToken.length) || 0);
    console.log("📋 Pay-per-use form data:", {
      action: "create_pay_per_use_purchase",
      productCount: productCount.toString(),
      selectedProducts: JSON.stringify(selectedProducts),
      csrfTokenPresent: !!csrfToken
    });
    fetcher.submit(formData, {
      method: "POST",
      action: "/app/billing/pay-per-use"
    });
  };
  return /* @__PURE__ */ jsx("div", { className: "fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4", children: /* @__PURE__ */ jsxs(
    motion.div,
    {
      initial: { opacity: 0, scale: 0.95, y: 10 },
      animate: { opacity: 1, scale: 1, y: 0 },
      exit: { opacity: 0, scale: 0.95, y: 10 },
      transition: { duration: 0.2, ease: "easeOut" },
      className: "bg-black border border-white/20 rounded-3xl shadow-2xl w-full max-w-sm",
      children: [
        /* @__PURE__ */ jsx("div", { className: "px-6 py-5 border-b border-white/20", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-3", children: [
            /* @__PURE__ */ jsx("div", { className: "w-10 h-10 bg-white rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsx(Zap, { className: "w-5 h-5 text-black" }) }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-white", children: "Pay & Optimize" }),
              /* @__PURE__ */ jsxs("p", { className: "text-sm text-white/70", children: [
                productCount,
                " product",
                productCount > 1 ? "s" : ""
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx(
            "button",
            {
              onClick: onCancel,
              className: "w-8 h-8 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors",
              children: /* @__PURE__ */ jsx(X, { className: "w-4 h-4 text-white/70" })
            }
          )
        ] }) }),
        /* @__PURE__ */ jsxs("div", { className: "px-6 py-5 space-y-5", children: [
          /* @__PURE__ */ jsxs("div", { className: "bg-black border border-white/20 rounded-2xl p-4", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-3", children: [
              /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-white/70", children: "Total Cost" }),
              /* @__PURE__ */ jsxs("div", { className: "text-right", children: [
                /* @__PURE__ */ jsxs("div", { className: "text-2xl font-bold text-white", children: [
                  "$",
                  totalCost.toFixed(2)
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "text-xs text-white/70", children: [
                  "$",
                  productCount,
                  " × $0.10"
                ] })
              ] })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-white/70", children: [
              /* @__PURE__ */ jsx(CheckCircle, { className: "w-3 h-3 mr-1" }),
              "Instant optimization • No subscription required"
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-3 p-3 bg-white/10 border border-white/20 rounded-2xl", children: [
            /* @__PURE__ */ jsx(Shield, { className: "w-4 h-4 text-white/70" }),
            /* @__PURE__ */ jsxs("div", { className: "text-xs text-white/70", children: [
              /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Secure payment" }),
              " via Shopify billing"
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
            /* @__PURE__ */ jsx("h4", { className: "text-sm font-medium text-white", children: "What's included:" }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-1.5 text-xs text-white/70", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsx(CheckCircle, { className: "w-3 h-3 text-green-400" }),
                /* @__PURE__ */ jsx("span", { children: "AI-powered SEO optimization" })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsx(CheckCircle, { className: "w-3 h-3 text-green-400" }),
                /* @__PURE__ */ jsx("span", { children: "Meta descriptions & titles" })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
                /* @__PURE__ */ jsx(CheckCircle, { className: "w-3 h-3 text-green-400" }),
                /* @__PURE__ */ jsx("span", { children: "Instant results" })
              ] })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "px-6 py-4 border-t border-white/20 space-y-3", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex space-x-3", children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: onCancel,
                disabled: isProcessing || fetcher.state === "submitting",
                className: "flex-1 h-10 bg-white/10 hover:bg-white/20 text-white border-0",
                children: "Cancel"
              }
            ),
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: handlePayAndOptimize,
                disabled: isProcessing || fetcher.state === "submitting",
                className: "flex-1 h-10 bg-white hover:bg-gray-100 text-black border-0",
                children: fetcher.state === "submitting" ? /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
                  /* @__PURE__ */ jsx("div", { className: "w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin" }),
                  /* @__PURE__ */ jsx("span", { children: "Processing..." })
                ] }) : /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
                  /* @__PURE__ */ jsx(DollarSign, { className: "w-4 h-4" }),
                  /* @__PURE__ */ jsxs("span", { children: [
                    "Pay $",
                    totalCost.toFixed(2)
                  ] })
                ] })
              }
            )
          ] }),
          /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70 text-center", children: "Secure payment via Shopify • Charges are non-refundable" })
        ] })
      ]
    }
  ) });
}
function usePayPerUseBilling() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastPurchase, setLastPurchase] = useState(null);
  const createPurchase = async (productCount, selectedProducts) => {
    setIsProcessing(true);
    try {
      const formData = new FormData();
      formData.append("action", "create_pay_per_use_purchase");
      formData.append("productCount", productCount.toString());
      formData.append("selectedProducts", JSON.stringify(selectedProducts));
      const response = await fetch("/app/billing/pay-per-use", {
        method: "POST",
        body: formData
      });
      if (response.ok) {
        window.location.href = response.url;
      } else {
        const error = await response.json();
        throw new Error(error.error || "Failed to create purchase");
      }
    } catch (error) {
      console.error("Pay-per-use purchase error:", error);
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };
  const checkPurchaseStatus = async (purchaseId) => {
    try {
      const formData = new FormData();
      formData.append("action", "check_purchase_status");
      formData.append("purchaseId", purchaseId);
      const response = await fetch("/app/billing/pay-per-use", {
        method: "POST",
        body: formData
      });
      if (response.ok) {
        const data = await response.json();
        setLastPurchase({
          id: purchaseId,
          amount: data.amount,
          productCount: data.productCount,
          status: data.status
        });
        return data;
      } else {
        throw new Error("Failed to check purchase status");
      }
    } catch (error) {
      console.error("Purchase status check error:", error);
      throw error;
    }
  };
  return {
    isProcessing,
    lastPurchase,
    createPurchase,
    checkPurchaseStatus
  };
}
const loader$7 = withBulletproofAuth(async ({ request, auth }) => {
  const { admin, session } = auth;
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const search = url.searchParams.get("search") || "";
  try {
    await applyRateLimit(RATE_LIMITERS.SEO_DASHBOARD(session.shop), {
      action: "seo_dashboard_load",
      shop: session.shop
    });
    const graphqlService = createGraphQLService(admin, session.shop);
    console.log("📦 Fetching products using enhanced GraphQL service...");
    const searchQuery = search ? `title:*${search}* OR description:*${search}*` : void 0;
    const fetchedProducts = await graphqlService.getAllProducts(searchQuery, 1e3, {
      action: "fetch_products_for_seo",
      shop: session.shop
    });
    const allProducts = fetchedProducts.map((product) => {
      var _a2, _b;
      return {
        id: product.id.replace("gid://shopify/Product/", ""),
        title: product.title,
        description: product.description || "",
        type: product.productType || "Uncategorized",
        vendor: product.vendor || "Unknown",
        handle: product.handle,
        seoTitle: ((_a2 = product.seo) == null ? void 0 : _a2.title) || "",
        seoDescription: ((_b = product.seo) == null ? void 0 : _b.description) || "",
        seoScore: calculateSeoScore$1(product),
        status: "pending",
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      };
    });
    console.log(`✅ Successfully fetched ${allProducts.length} products using GraphQL service`);
    const products = allProducts;
    const productTypes = [...new Set(products.map((p) => p.type))].filter(Boolean);
    const { getSubscriptionUpdateFlag: getSubscriptionUpdateFlag2, clearSubscriptionUpdateFlag: clearSubscriptionUpdateFlag2, invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
    const subscriptionUpdateFlag = getSubscriptionUpdateFlag2(session.shop);
    if (subscriptionUpdateFlag) {
      console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh`);
      invalidateBillingCache2(session.shop);
      clearSubscriptionUpdateFlag2(session.shop);
    }
    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    const creditsService = new CreditsService(session.shop);
    const creditBalance = await creditsService.getCreditBalance();
    const csrfToken = generateCSRFToken(session.shop);
    console.log("🔐 Generated CSRF token for SEO dashboard:", session.shop);
    return json({
      products,
      productTypes,
      pagination: {
        currentPage: page,
        hasNextPage: false,
        // We fetched all available products
        hasPreviousPage: false
      },
      totalProducts: products.length,
      billing: {
        hasAccess: billingStatus.hasAccess,
        plan: billingStatus.plan,
        subscription: billingStatus.subscription
      },
      creditBalance,
      csrfToken
    });
  } catch (error) {
    console.error("Error loading products:", error);
    return json({
      products: [],
      productTypes: [],
      pagination: { currentPage: 1, hasNextPage: false, hasPreviousPage: false },
      totalProducts: 0,
      billing: {
        hasAccess: false,
        plan: void 0,
        subscription: void 0
      },
      error: "Failed to load products"
    });
  }
});
function calculateSeoScore$1(product) {
  var _a2, _b;
  let score = 0;
  if (product.title) {
    score += Math.min(30, product.title.length > 10 ? 30 : 15);
  }
  if (product.description && product.description.length > 50) {
    score += 25;
  } else if (product.description) {
    score += 10;
  }
  if ((_a2 = product.seo) == null ? void 0 : _a2.title) {
    score += product.seo.title.length <= 70 ? 25 : 15;
  }
  if ((_b = product.seo) == null ? void 0 : _b.description) {
    score += product.seo.description.length <= 160 ? 20 : 10;
  }
  return Math.min(100, score);
}
const activeRequests = /* @__PURE__ */ new Map();
const action$4 = withBulletproofAction(async ({ request, auth }) => {
  var _a2, _b, _c, _d, _e, _f;
  const { admin } = auth;
  const formData = await request.formData();
  const action2 = formData.get("action");
  if (action2 === "optimize_comprehensive") {
    const productIds = JSON.parse(((_a2 = formData.get("productIds")) == null ? void 0 : _a2.toString()) || "[]");
    const settings = JSON.parse(((_b = formData.get("settings")) == null ? void 0 : _b.toString()) || "{}");
    const apiKey = process.env.GEMINI_API_KEY;
    const requestId = ((_c = formData.get("requestId")) == null ? void 0 : _c.toString()) || `${Date.now()}-${Math.random()}`;
    if (activeRequests.has(requestId)) {
      console.log(`⚠️ Duplicate request detected: ${requestId}`);
      return json({
        error: "Request already in progress. Please wait for the current optimization to complete.",
        isDuplicate: true
      }, { status: 429 });
    }
    activeRequests.set(requestId, true);
    try {
      if (!apiKey) {
        activeRequests.delete(requestId);
        return json({ error: "Gemini API key not configured on server" }, { status: 500 });
      }
      if (productIds.length === 0) {
        activeRequests.delete(requestId);
        return json({ error: "No products selected" }, { status: 400 });
      }
      try {
        console.log(`🚀 Starting comprehensive SEO optimization for ${productIds.length} products`);
        const batchSize = 10;
        const allProducts = [];
        for (let i = 0; i < productIds.length; i += batchSize) {
          const batchIds = productIds.slice(i, i + batchSize);
          console.log(`📦 Fetching batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(productIds.length / batchSize)} (${batchIds.length} products)`);
          const productQueries = batchIds.map((id, index) => `
          product${index}: product(id: "gid://shopify/Product/${id}") {
            id
            title
            description
            productType
            vendor
            handle
            seo {
              title
              description
            }
            images(first: 3) {
              edges {
                node {
                  id
                  altText
                }
              }
            }
          }
        `).join("\n");
          const response = await admin.graphql(`
          query getProductsForOptimization {
            ${productQueries}
          }
        `);
          const responseJson = await response.json();
          const batchProducts = Object.values(responseJson.data || {});
          allProducts.push(...batchProducts);
          if (i + batchSize < productIds.length) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }
        }
        const products = allProducts.map((product) => {
          var _a3, _b2, _c2, _d2;
          return {
            id: product.id.replace("gid://shopify/Product/", ""),
            title: product.title,
            description: product.description || "",
            type: product.productType || "Uncategorized",
            vendor: product.vendor || "Unknown",
            handle: product.handle,
            seoTitle: ((_a3 = product.seo) == null ? void 0 : _a3.title) || "",
            seoDescription: ((_b2 = product.seo) == null ? void 0 : _b2.description) || "",
            images: ((_d2 = (_c2 = product.images) == null ? void 0 : _c2.edges) == null ? void 0 : _d2.map((img) => ({
              id: img.node.id,
              altText: img.node.altText || ""
            }))) || []
          };
        });
        const geminiService = new GeminiService(apiKey);
        const optimizationResults = await geminiService.comprehensiveSeoOptimization(products, {
          updateProductTitle: settings.updateProductTitle || false,
          updateProductDescription: settings.updateProductDescription || false,
          updateSeoFields: settings.updateSeoFields || true,
          updateHandle: settings.updateHandle || false,
          updateImageAlts: settings.updateImageAlts || false
        });
        if (settings.autoApply) {
          const mutations = optimizationResults.filter((result) => !result.error).map(async (result) => {
            const mutations2 = [];
            if (settings.updateProductTitle || settings.updateProductDescription || settings.updateHandle) {
              const productInput = {
                id: `gid://shopify/Product/${result.productId}`
              };
              if (settings.updateProductTitle && result.optimizedProductTitle) {
                productInput.title = result.optimizedProductTitle;
              }
              if (settings.updateProductDescription && result.optimizedProductDescription) {
                productInput.descriptionHtml = result.optimizedProductDescription;
              }
              if (settings.updateHandle && result.optimizedHandle) {
                productInput.handle = result.optimizedHandle;
              }
              mutations2.push(
                admin.graphql(`
                  mutation updateProduct($input: ProductInput!) {
                    productUpdate(input: $input) {
                      product {
                        id
                        title
                        description
                        handle
                      }
                      userErrors {
                        field
                        message
                      }
                    }
                  }
                `, {
                  variables: {
                    input: productInput
                  }
                })
              );
            }
            if (settings.updateSeoFields && (result.optimizedSeoTitle || result.optimizedSeoDescription)) {
              const seoInput = {};
              if (result.optimizedSeoTitle) {
                seoInput.title = result.optimizedSeoTitle;
              }
              if (result.optimizedSeoDescription) {
                seoInput.description = result.optimizedSeoDescription;
              }
              mutations2.push(
                admin.graphql(`
                  mutation updateProductSeo($input: ProductInput!) {
                    productUpdate(input: $input) {
                      product {
                        id
                        seo {
                          title
                          description
                        }
                      }
                      userErrors {
                        field
                        message
                      }
                    }
                  }
                `, {
                  variables: {
                    input: {
                      id: `gid://shopify/Product/${result.productId}`,
                      seo: seoInput
                    }
                  }
                })
              );
            }
            if (settings.updateImageAlts && result.optimizedSeoTitle) {
              const productData = products.find((p) => p.id === result.productId);
              if (productData && productData.images) {
                productData.images.forEach((image) => {
                  mutations2.push(
                    admin.graphql(`
                      mutation updateProductImage($productId: ID!, $image: ProductImageInput!) {
                        productImageUpdate(productId: $productId, image: $image) {
                          image {
                            id
                            altText
                          }
                          userErrors {
                            field
                            message
                          }
                        }
                      }
                    `, {
                      variables: {
                        productId: `gid://shopify/Product/${result.productId}`,
                        image: {
                          id: image.id,
                          altText: result.optimizedSeoTitle
                        }
                      }
                    })
                  );
                });
              }
            }
            return Promise.all(mutations2);
          });
          const mutationResults = await Promise.allSettled(mutations);
          mutationResults.forEach((result, index) => {
            if (result.status === "rejected") {
              console.error(`❌ Mutation ${index + 1} failed:`, result.reason);
            } else {
              console.log(`✅ Mutation ${index + 1} completed successfully`);
            }
          });
          const successCount = optimizationResults.filter((r) => !r.error).length;
          const errorCount = optimizationResults.filter((r) => r.error).length;
          const mutationSuccessCount = mutationResults.filter((r) => r.status === "fulfilled").length;
          const mutationErrorCount = mutationResults.filter((r) => r.status === "rejected").length;
          console.log(`📊 Optimization Summary: ${successCount} optimized, ${errorCount} failed`);
          console.log(`📊 Mutation Summary: ${mutationSuccessCount} applied, ${mutationErrorCount} failed`);
          return json({
            success: true,
            message: `🎉 Successfully optimized ${successCount} products with viral keywords and comprehensive SEO improvements!`,
            results: optimizationResults.map((result) => ({
              ...result,
              improvements: {
                titleOptimized: result.optimizedProductTitle !== result.originalProductTitle,
                descriptionOptimized: result.optimizedProductDescription !== result.originalProductDescription,
                seoFieldsOptimized: result.optimizedSeoTitle || result.optimizedSeoDescription,
                viralKeywordsAdded: result.viralKeyword ? true : false,
                seoScoreImprovement: result.seoScore || 0
              }
            })),
            summary: {
              totalProcessed: optimizationResults.length,
              successCount,
              errorCount,
              averageImprovementScore: successCount > 0 ? Math.round(optimizationResults.filter((r) => !r.error && r.seoScore).reduce((sum, r) => sum + (r.seoScore || 0), 0) / successCount) : 0,
              topViralKeywords: [...new Set(optimizationResults.filter((r) => !r.error && r.viralKeyword).map((r) => r.viralKeyword).slice(0, 5))]
            },
            autoApplied: true
          });
        } else {
          return json({
            success: true,
            results: optimizationResults,
            autoApplied: false
          });
        }
      } catch (error) {
        console.error("❌ Comprehensive SEO optimization failed:", error);
        activeRequests.delete(requestId);
        return json({
          error: `Optimization failed: ${error instanceof Error ? error.message : "Unknown error"}`
        }, { status: 500 });
      } finally {
        activeRequests.delete(requestId);
      }
    } catch (error) {
      console.error("❌ Action error:", error);
      return json({ error: "Internal server error" }, { status: 500 });
    }
  }
  if (action2 === "optimize_with_credits") {
    console.log("🔄 Processing optimize_with_credits action");
    const { session } = auth;
    const productIds = JSON.parse(((_d = formData.get("productIds")) == null ? void 0 : _d.toString()) || "[]");
    const settings = JSON.parse(((_e = formData.get("settings")) == null ? void 0 : _e.toString()) || "{}");
    console.log(`📊 Credit optimization request: ${productIds.length} products for shop ${session.shop}`);
    try {
      const creditsService = new CreditsService(session.shop);
      const creditBalance = await creditsService.getCreditBalance();
      if (creditBalance.remainingCredits < productIds.length) {
        return json({
          error: `Insufficient credits. Required: ${productIds.length}, Available: ${creditBalance.remainingCredits}`
        }, { status: 400 });
      }
      const success = await creditsService.useCredits(
        productIds.length,
        `SEO optimization for ${productIds.length} products`,
        `batch_${Date.now()}`
      );
      if (!success) {
        return json({ error: "Failed to use credits" }, { status: 400 });
      }
      console.log("🚀 Starting SEO optimization service...");
      const seoService = createSEOOptimizationService(admin, session.shop);
      console.log("✅ SEO service created, starting optimization...");
      const result = await seoService.startOptimization(productIds, settings, {
        action: "optimize_with_credits",
        shop: session.shop
      });
      console.log("✅ SEO optimization started:", result);
      const response = {
        success: true,
        requestId: result.requestId,
        // Include the request ID for progress tracking
        message: `Successfully started optimization for ${productIds.length} products using credits`,
        creditsUsed: productIds.length,
        remainingCredits: creditBalance.remainingCredits - productIds.length
      };
      console.log("📤 Sending response:", response);
      return json(response);
    } catch (error) {
      console.error("❌ Credit optimization error:", error);
      return json({
        error: error instanceof Error ? error.message : "Failed to optimize with credits"
      }, { status: 500 });
    }
  }
  if (action2 === "check_progress") {
    try {
      const { admin: admin2, session } = auth;
      const requestId = (_f = formData.get("requestId")) == null ? void 0 : _f.toString();
      if (!requestId) {
        return json({ error: "Request ID is required" }, { status: 400 });
      }
      await applyRateLimit(RATE_LIMITERS.SEO_PROGRESS(session.shop), {
        action: "check_progress",
        shop: session.shop
      });
      const seoService = createSEOOptimizationService(admin2, session.shop);
      const status = await seoService.getOptimizationStatus(requestId);
      return json({
        success: true,
        status
      });
    } catch (error) {
      console.error("❌ Progress check error:", error);
      return json({
        error: error instanceof Error ? error.message : "Failed to check progress"
      }, { status: 500 });
    }
  }
  return json({ error: "Invalid action" }, { status: 400 });
});
function SeoOptimizationDashboard() {
  var _a2, _b, _c, _d, _e;
  const loaderData = useLoaderData();
  const { products, productTypes, totalProducts, billing } = loaderData;
  const initialCreditBalance = loaderData.creditBalance;
  const csrfToken = loaderData.csrfToken;
  const [creditBalance, setCreditBalance] = useState(initialCreditBalance);
  useEffect(() => {
    setCreditBalance(initialCreditBalance);
  }, [initialCreditBalance]);
  const fetcher = useFetcher();
  const progressFetcher = useFetcher();
  const navigate = useNavigate();
  const [shopify2, setShopify] = useState(null);
  useEffect(() => {
    if (typeof window !== "undefined") {
      import("@shopify/app-bridge-react").then(({ useAppBridge: useAppBridge2 }) => {
        try {
          setShopify(useAppBridge2());
        } catch (error) {
          console.warn("Failed to initialize app bridge:", error);
        }
      });
    }
  }, []);
  const progressSectionRef = useRef(null);
  const showToast = useCallback((message, options) => {
    try {
      if (shopify2 == null ? void 0 : shopify2.toast) {
        shopify2.toast.show(message, options);
      } else {
        console.log(`Toast: ${message}`, options);
      }
    } catch (error) {
      console.warn("Toast error:", error);
      console.log(`Toast: ${message}`, options);
    }
  }, [shopify2]);
  const payPerUseBilling = usePayPerUseBilling();
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [typeFilter, setTypeFilter] = useState("all");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isUsingCredits, setIsUsingCredits] = useState(false);
  const [requestId, setRequestId] = useState(null);
  const [progressPollingInterval, setProgressPollingInterval] = useState(null);
  const [progressPollingErrors, setProgressPollingErrors] = useState(0);
  const [completionResults, setCompletionResults] = useState(null);
  const [showResults, setShowResults] = useState(false);
  const [showPayPerUseModal, setShowPayPerUseModal] = useState(false);
  const [progressData, setProgressData] = useState(null);
  const [settings, setSettings] = useState({
    updateProductTitle: false,
    updateProductDescription: false,
    updateSeoFields: true,
    updateHandle: false,
    updateImageAlts: false,
    autoApply: true,
    batchSize: 10
  });
  useEffect(() => {
    if (isProcessing && progressData && !isUsingCredits) {
      const interval = setInterval(() => {
        setProgressData((prev) => {
          if (!prev) return null;
          const elapsed = Date.now() - prev.startTime;
          const estimatedTotal = prev.totalProducts * 3e3;
          const estimatedProgress = Math.min(95, elapsed / estimatedTotal * 100);
          const currentBatch = Math.floor(estimatedProgress / 100 * prev.totalBatches) + 1;
          const completedProducts = Math.floor(estimatedProgress / 100 * prev.totalProducts);
          return {
            ...prev,
            currentBatch: Math.min(currentBatch, prev.totalBatches),
            completedProducts: Math.min(completedProducts, prev.totalProducts),
            stage: currentBatch <= prev.totalBatches ? `Processing batch ${currentBatch}/${prev.totalBatches}...` : "Finalizing optimizations..."
          };
        });
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isProcessing, progressData, isUsingCredits]);
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data && !isUsingCredits) {
      const responseData = fetcher.data;
      if ((responseData.success || responseData.results && responseData.results.length > 0) && responseData.results) {
        const results = responseData.results || [];
        const successCount = results.filter((r) => !r.error).length;
        const errorCount = results.filter((r) => r.error).length;
        setCompletionResults({
          totalProcessed: results.length,
          successCount,
          errorCount,
          results,
          processingTime: Date.now() - (requestId ? parseInt(requestId) : Date.now()),
          timestamp: (/* @__PURE__ */ new Date()).toLocaleString(),
          summary: responseData.summary || {},
          message: responseData.message || `Optimized ${successCount} products successfully!`
        });
        setShowResults(true);
        setIsProcessing(false);
        setProgressData(null);
        if (successCount > 0) {
          if (errorCount > 0) {
            showToast(`✅ Optimized ${successCount} products successfully! ${errorCount} had issues.`, { duration: 5e3 });
          } else {
            showToast(`🎉 Successfully optimized ${successCount} products!`, { duration: 5e3 });
          }
        } else if (errorCount > 0) {
          showToast(`⚠️ Optimization completed with ${errorCount} errors. Check results for details.`, { isError: true, duration: 5e3 });
        }
        setSelectedProducts([]);
      } else if (responseData.error) {
        if (responseData.isDuplicate) {
          showToast("Optimization already in progress. Please wait...", { duration: 3e3 });
          return;
        }
        console.error("❌ Optimization error:", responseData.error);
        showToast(`Optimization failed: ${responseData.error}`, { isError: true, duration: 5e3 });
        setIsProcessing(false);
        if (progressData) {
          setProgressData({
            ...progressData,
            stage: "❌ Error occurred during optimization",
            currentProduct: "Process failed"
          });
          setTimeout(() => {
            setProgressData(null);
          }, 5e3);
        }
      }
    }
  }, [fetcher.state, fetcher.data, shopify2, requestId, isUsingCredits]);
  const getFilteredProducts = useCallback(() => {
    return products.filter((product) => {
      const matchesSearch = !searchValue || product.title.toLowerCase().includes(searchValue.toLowerCase()) || product.description.toLowerCase().includes(searchValue.toLowerCase());
      const matchesType = typeFilter === "all" || product.type === typeFilter;
      return matchesSearch && matchesType;
    });
  }, [products, searchValue, typeFilter]);
  const handleProductSelect = useCallback((productId) => {
    setSelectedProducts(
      (prev) => prev.includes(productId) ? prev.filter((id) => id !== productId) : [...prev, productId]
    );
  }, []);
  const handleSelectAll = useCallback(() => {
    console.log("🔵🔵🔵 handleSelectAll called (BLUE Select All button) 🔵🔵🔵");
    console.log("🔵 This should select ALL products or deselect all");
    const filteredProducts2 = getFilteredProducts();
    const allSelected = filteredProducts2.every((p) => selectedProducts.includes(p.id));
    if (allSelected) {
      console.log("🔵 Deselecting all products");
      setSelectedProducts([]);
    } else {
      console.log("🔵 Selecting all products:", filteredProducts2.length);
      setSelectedProducts(filteredProducts2.map((p) => p.id));
    }
  }, [selectedProducts, getFilteredProducts]);
  const handleSelectNonOptimized = useCallback(() => {
    console.log("🟢🟢🟢 handleSelectNonOptimized called (GREEN Select Needs Optimization button) 🟢🟢🟢");
    console.log("🟢 This should ONLY select products with SEO score < 80 or status pending/failed");
    const filteredProducts2 = getFilteredProducts();
    const seoScores = filteredProducts2.filter((p) => p != null).map((p) => p.seoScore).sort((a, b) => a - b);
    console.log("📊 SEO Score Distribution:", {
      total: filteredProducts2.length,
      min: seoScores.length > 0 ? Math.min(...seoScores) : 0,
      max: seoScores.length > 0 ? Math.max(...seoScores) : 0,
      avg: seoScores.length > 0 ? Math.round(seoScores.reduce((a, b) => a + b, 0) / seoScores.length) : 0,
      below80: seoScores.filter((s) => s < 80).length,
      below70: seoScores.filter((s) => s < 70).length,
      scores: seoScores.slice(0, 20)
      // Show first 20 scores only
    });
    console.log("🔍 Sample products (first 3):", filteredProducts2.slice(0, 3).map((p) => {
      var _a3;
      return {
        id: p == null ? void 0 : p.id,
        title: ((_a3 = p == null ? void 0 : p.title) == null ? void 0 : _a3.substring(0, 20)) + "...",
        status: p == null ? void 0 : p.status,
        seoScore: p == null ? void 0 : p.seoScore,
        seoScoreType: typeof (p == null ? void 0 : p.seoScore),
        needsOptimization: (p == null ? void 0 : p.seoScore) != null && p.seoScore < 80
        // FIXED: Only check SEO score
      };
    }));
    const nonOptimizedProducts = filteredProducts2.filter((p) => {
      if (!p) return false;
      const needsOptimization = Number(p.seoScore) < 80;
      if (filteredProducts2.indexOf(p) < 5) {
        console.log(`🔍 Product ${p.id}: status=${p.status}, seoScore=${p.seoScore} (${typeof p.seoScore}), needsOptimization=${needsOptimization} (ONLY checking score < 80)`);
      }
      return needsOptimization;
    });
    console.log("🎯 Products that need optimization:", nonOptimizedProducts.length, "out of", filteredProducts2.length);
    console.log("🎯 Should be selecting:", nonOptimizedProducts.length, "products");
    console.log("🎯 First 5 products needing optimization:", nonOptimizedProducts.slice(0, 5).filter((p) => p != null).map((p) => {
      var _a3;
      return {
        id: p.id,
        title: ((_a3 = p.title) == null ? void 0 : _a3.substring(0, 30)) + "...",
        status: p.status,
        seoScore: p.seoScore
      };
    }));
    const allNonOptimizedSelected = nonOptimizedProducts.every((p) => selectedProducts.includes(p.id));
    if (allNonOptimizedSelected && nonOptimizedProducts.length > 0) {
      const nonOptimizedIds = nonOptimizedProducts.map((p) => p.id);
      console.log("🔄 Deselecting products that need optimization:", nonOptimizedIds.length);
      setSelectedProducts((prev) => prev.filter((id) => !nonOptimizedIds.includes(id)));
    } else {
      const nonOptimizedIds = nonOptimizedProducts.map((p) => p.id);
      console.log("✅ Selecting products that need optimization:", nonOptimizedIds.length);
      console.log("✅ Product IDs being selected:", nonOptimizedIds.slice(0, 10));
      console.log("✅ About to call setSelectedProducts with", nonOptimizedIds.length, "product IDs");
      setSelectedProducts(nonOptimizedIds);
      console.log("✅ setSelectedProducts called successfully");
    }
  }, [selectedProducts, getFilteredProducts]);
  const handleOptimizeWithCredits = useCallback(() => {
    console.log("🎯 handleOptimizeWithCredits called with:", {
      selectedProductsLength: selectedProducts.length,
      isProcessing,
      isUsingCredits
    });
    if (selectedProducts.length === 0) {
      showToast("Please select products to optimize", { isError: true });
      return;
    }
    setIsProcessing(true);
    setIsUsingCredits(true);
    setProgressData({
      startTime: Date.now(),
      totalProducts: selectedProducts.length,
      completedProducts: 0,
      currentBatch: 1,
      totalBatches: 1,
      currentProduct: "Initializing...",
      stage: "Starting optimization with credits..."
    });
    const formData = new FormData();
    formData.append("action", "optimize_with_credits");
    formData.append("productIds", JSON.stringify(selectedProducts));
    formData.append("settings", JSON.stringify(settings));
    formData.append("csrfToken", csrfToken);
    console.log("📤 About to submit form with data:", {
      action: "optimize_with_credits",
      productIds: selectedProducts,
      settings,
      csrfToken
    });
    console.log("📤 FormData entries:", Array.from(formData.entries()));
    console.log("📤 Fetcher state before submit:", fetcher.state);
    fetcher.submit(formData, { method: "POST", action: "/app/seo-dashboard" });
    console.log("📤 Fetcher state after submit:", fetcher.state);
    setTimeout(() => {
      if (progressSectionRef.current) {
        progressSectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "center"
        });
      }
    }, 100);
  }, [selectedProducts, settings, csrfToken, showToast, fetcher]);
  const startProgressPolling = useCallback((optimizationRequestId) => {
    console.log(`🔄 Starting progress polling for request: ${optimizationRequestId}`);
    if (progressPollingInterval) {
      clearInterval(progressPollingInterval);
    }
    setProgressPollingErrors(0);
    const interval = setInterval(() => {
      try {
        if (progressFetcher.state === "idle") {
          const formData = new FormData();
          formData.append("action", "check_progress");
          formData.append("requestId", optimizationRequestId);
          console.log(`🔄 Polling progress for request: ${optimizationRequestId} (errors: ${progressPollingErrors})`);
          progressFetcher.submit(formData, { method: "POST", action: "/app/seo-dashboard" });
        } else {
          console.log(`⏳ Progress fetcher busy (${progressFetcher.state}), skipping poll`);
        }
      } catch (error) {
        console.error("❌ Progress polling error:", error);
        setProgressPollingErrors((prev) => prev + 1);
        if (progressPollingErrors >= 5) {
          console.error(`❌ Too many polling errors (${progressPollingErrors}), stopping`);
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }
          showToast("Progress tracking failed. The optimization is still running in the background.", { isError: true });
        }
      }
    }, 5e3);
    setProgressPollingInterval(interval);
  }, [progressPollingInterval, progressFetcher, showToast, progressPollingErrors, setProgressPollingErrors]);
  useEffect(() => {
    if (progressFetcher.state === "idle" && progressFetcher.data) {
      const result = progressFetcher.data;
      console.log(`📊 Progress polling response:`, result);
      if (result.success && result.status) {
        const status = result.status;
        console.log(`📊 Progress update:`, status);
        setProgressPollingErrors(0);
        setProgressData((prev) => prev ? {
          ...prev,
          currentBatch: status.progress.currentBatch,
          totalBatches: status.progress.totalBatches,
          completedProducts: status.progress.processedProducts,
          currentProduct: `Processing batch ${status.progress.currentBatch}/${status.progress.totalBatches}`,
          stage: status.status === "processing" ? "Optimizing products..." : status.status
        } : null);
        if (status.status === "completed" || status.status === "failed") {
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }
          if (status.status === "completed") {
            setProgressData((prev) => prev ? {
              ...prev,
              completedProducts: prev.totalProducts,
              currentProduct: `Successfully optimized ${prev.totalProducts} products!`,
              stage: "Optimization completed successfully!"
            } : null);
            setTimeout(() => {
              showToast(`Successfully optimized ${selectedProducts.length} products!`);
              setIsProcessing(false);
              setIsUsingCredits(false);
              setProgressData(null);
            }, 2e3);
          } else {
            showToast(`Optimization failed: ${status.error || "Unknown error"}`, { isError: true });
            setIsProcessing(false);
            setIsUsingCredits(false);
            setProgressData(null);
          }
        }
      } else if (result.error) {
        console.error("❌ Progress polling error response:", result.error);
        setProgressPollingErrors((prev) => prev + 1);
        if (result.error.includes("502") || result.error.includes("Bad Gateway")) {
          console.log("🔄 Network error detected, continuing to poll...");
        }
        if (progressPollingErrors >= 5) {
          console.error(`❌ Too many polling errors (${progressPollingErrors}), stopping`);
          if (progressPollingInterval) {
            clearInterval(progressPollingInterval);
            setProgressPollingInterval(null);
          }
          showToast("Progress tracking failed due to network issues. The optimization is still running.", { isError: true });
        }
      }
    }
    if (progressFetcher.state === "idle" && !progressFetcher.data && isProcessing && isUsingCredits) {
      console.log("⚠️ Progress fetcher returned no data, likely a network error");
    }
  }, [progressFetcher.state, progressFetcher.data, progressPollingInterval, showToast, selectedProducts.length, isProcessing, isUsingCredits]);
  useEffect(() => {
    if (!isUsingCredits) return;
    if (fetcher.state === "submitting") {
      setProgressData((prev) => prev ? {
        ...prev,
        stage: "Processing your request...",
        currentProduct: "Validating credits and permissions..."
      } : null);
    } else if (fetcher.state === "loading") {
      setProgressData((prev) => prev ? {
        ...prev,
        stage: "Optimizing products...",
        currentProduct: `Processing ${prev.totalProducts} selected products...`,
        completedProducts: Math.floor(prev.totalProducts * 0.3)
      } : null);
    } else if (fetcher.state === "idle" && fetcher.data) {
      setProgressData((prev) => prev ? {
        ...prev,
        stage: "Request completed, starting optimization...",
        currentProduct: "Initializing optimization process..."
      } : null);
    }
  }, [fetcher.state, isUsingCredits, fetcher.data]);
  useEffect(() => {
    console.log(`🔄 Fetcher state changed: ${fetcher.state}`, {
      data: fetcher.data,
      isUsingCredits,
      hasData: !!fetcher.data,
      dataKeys: fetcher.data ? Object.keys(fetcher.data) : []
    });
    if (fetcher.state === "idle" && fetcher.data && isUsingCredits) {
      const data = fetcher.data;
      console.log(`📊 Credit optimization response data:`, data);
      if (data.success && data.requestId) {
        console.log(`✅ Optimization started with requestId: ${data.requestId}`);
        setRequestId(data.requestId);
        if (data.remainingCredits !== void 0) {
          setCreditBalance((prev) => ({
            ...prev,
            remainingCredits: data.remainingCredits,
            usedCredits: prev.usedCredits + (data.creditsUsed || 0)
          }));
          console.log(`💰 Updated credit balance: ${data.remainingCredits} remaining`);
        }
        startProgressPolling(data.requestId);
        setProgressData((prev) => prev ? {
          ...prev,
          currentProduct: "Connecting to optimization process...",
          stage: "Starting real-time progress tracking..."
        } : null);
      } else if (data.error) {
        showToast(data.error, { isError: true });
        setIsProcessing(false);
        setIsUsingCredits(false);
        setProgressData(null);
      }
    } else if (fetcher.state === "idle" && isProcessing && !fetcher.data) {
      setIsProcessing(false);
      setIsUsingCredits(false);
      setProgressData(null);
    }
  }, [fetcher.state, fetcher.data, selectedProducts.length, showToast, isProcessing, isUsingCredits]);
  useEffect(() => {
    return () => {
      if (progressPollingInterval) {
        clearInterval(progressPollingInterval);
      }
    };
  }, [progressPollingInterval]);
  const handleStartOptimization = useCallback(() => {
    if (selectedProducts.length === 0) {
      showToast("Please select products to optimize", { isError: true });
      return;
    }
    if (isProcessing) {
      console.log("⚠️ Already processing - please wait");
      return;
    }
    const billingData = billing;
    console.log("🔍 Billing check - hasAccess:", billing.hasAccess);
    console.log("🔍 Billing check - plan:", billingData.plan);
    console.log("🔍 Billing check - subscription:", billingData.subscription);
    if (!billingData.plan) {
      console.log("💳 No plan selected, showing pay-per-use modal as default");
      setShowPayPerUseModal(true);
      return;
    }
    if (billingData.plan.id === "pay_per_use") {
      console.log("💳 Pay-per-use plan detected, checking credit balance...");
      console.log("💰 Credit balance:", creditBalance);
      console.log("💰 Credit balance details:", {
        remainingCredits: creditBalance == null ? void 0 : creditBalance.remainingCredits,
        totalCredits: creditBalance == null ? void 0 : creditBalance.totalCredits,
        usedCredits: creditBalance == null ? void 0 : creditBalance.usedCredits,
        selectedProductsLength: selectedProducts.length
      });
      if (creditBalance && creditBalance.remainingCredits >= selectedProducts.length) {
        console.log("✅ Sufficient credits available, proceeding with optimization");
        console.log("🎯 About to call handleOptimizeWithCredits...");
        handleOptimizeWithCredits();
        console.log("🎯 handleOptimizeWithCredits call completed");
        return;
      } else {
        console.log("❌ Insufficient credits, showing payment modal");
        console.log(`Required: ${selectedProducts.length}, Available: ${(creditBalance == null ? void 0 : creditBalance.remainingCredits) || 0}`);
        setShowPayPerUseModal(true);
        return;
      }
    }
    if (!billingData.subscription || billingData.subscription.status !== "ACTIVE") {
      console.log("💳 No active subscription, showing pay-per-use modal");
      setShowPayPerUseModal(true);
      return;
    }
    console.log("✅ User has active subscription, proceeding with optimization");
    const newRequestId = Date.now().toString();
    if (requestId && Date.now() - parseInt(requestId) < 1e4) {
      console.log("⚠️ Preventing duplicate request - please wait 10 seconds between requests");
      showToast("Please wait 10 seconds between optimization requests", { isError: true });
      return;
    }
    setRequestId(newRequestId);
    setIsProcessing(true);
    const totalProducts2 = selectedProducts.length;
    const totalBatches = Math.ceil(totalProducts2 / 10);
    setProgressData({
      currentBatch: 0,
      totalBatches,
      currentProduct: "",
      completedProducts: 0,
      totalProducts: totalProducts2,
      stage: "Initializing...",
      startTime: Date.now()
    });
    setTimeout(() => {
      if (progressSectionRef.current) {
        progressSectionRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start"
        });
      }
    }, 100);
    const formData = new FormData();
    formData.append("action", "optimize_comprehensive");
    formData.append("productIds", JSON.stringify(selectedProducts));
    formData.append("settings", JSON.stringify(settings));
    formData.append("requestId", newRequestId);
    fetcher.submit(formData, { method: "POST" });
  }, [selectedProducts, settings, fetcher, shopify2, navigate, isProcessing, requestId, billing]);
  const handlePayPerUseConfirm = useCallback(() => {
    setShowPayPerUseModal(false);
  }, []);
  const handlePayPerUseCancel = useCallback(() => {
    setShowPayPerUseModal(false);
  }, []);
  const dashboardMetrics = {
    totalProducts,
    selectedCount: selectedProducts.length,
    averageSeoScore: products.length > 0 ? Math.round(products.reduce((sum, p) => sum + ((p == null ? void 0 : p.seoScore) || 0), 0) / products.length) : 0,
    needsOptimization: products.filter((p) => (p == null ? void 0 : p.seoScore) < 80).length,
    optimizedCount: products.filter((p) => (p == null ? void 0 : p.status) === "optimized").length
  };
  const filteredProducts = getFilteredProducts();
  return /* @__PURE__ */ jsxs(
    motion.div,
    {
      initial: { opacity: 0, y: 20 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.5, ease: "easeOut" },
      className: "min-h-screen bg-black text-white",
      children: [
        /* @__PURE__ */ jsx(TitleBar, { title: "AI BULK SEO - SEO Optimizer" }),
        /* @__PURE__ */ jsx(
          SeoOptimizationHeader,
          {
            totalProducts: dashboardMetrics.totalProducts,
            selectedCount: selectedProducts.length,
            averageSeoScore: dashboardMetrics.averageSeoScore,
            onOptimizeSelected: handleStartOptimization,
            onSelectAll: handleSelectAll,
            isOptimizing: isProcessing
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "bg-black py-20 px-6", children: [
          /* @__PURE__ */ jsxs(
            motion.div,
            {
              className: "max-w-6xl mx-auto",
              initial: { opacity: 0, y: 20 },
              animate: { opacity: 1, y: 0 },
              transition: { duration: 0.5, ease: "easeOut" },
              children: [
                /* @__PURE__ */ jsxs(
                  motion.div,
                  {
                    className: "flex flex-col gap-8",
                    initial: { opacity: 0, y: 10 },
                    animate: { opacity: 1, y: 0 },
                    transition: { duration: 0.3, delay: 0.1 },
                    children: [
                      /* @__PURE__ */ jsxs(
                        motion.div,
                        {
                          className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",
                          initial: { opacity: 0, y: 20 },
                          animate: { opacity: 1, y: 0 },
                          transition: { duration: 0.3, delay: 0.2 },
                          children: [
                            /* @__PURE__ */ jsx(
                              motion.div,
                              {
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.1 },
                                children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all", children: [
                                  /* @__PURE__ */ jsxs("div", { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white/70", children: "Total Products" }),
                                    /* @__PURE__ */ jsx("div", { className: "w-8 h-8 bg-white/20 rounded-2xl flex items-center justify-center", children: /* @__PURE__ */ jsx(MorphingShapeAnimation, { size: 16 }) })
                                  ] }),
                                  /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-white", children: dashboardMetrics.totalProducts.toLocaleString() }),
                                    /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70", children: "Ready for optimization" })
                                  ] })
                                ] })
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.div,
                              {
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.2 },
                                children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all", children: [
                                  /* @__PURE__ */ jsxs("div", { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white/70", children: "Average SEO Score" }),
                                    /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                                      dashboardMetrics.averageSeoScore >= 80 ? "bg-white/30" : "bg-white/20"
                                    ), children: /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-3 h-3 rounded-full",
                                      dashboardMetrics.averageSeoScore >= 80 ? "bg-white" : "bg-white/70"
                                    ) }) })
                                  ] }),
                                  /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
                                    /* @__PURE__ */ jsxs("div", { className: "text-2xl font-bold text-white", children: [
                                      dashboardMetrics.averageSeoScore,
                                      "%"
                                    ] }),
                                    /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70", children: dashboardMetrics.averageSeoScore >= 80 ? "Excellent performance" : dashboardMetrics.averageSeoScore >= 60 ? "Good, can improve" : "Needs attention" })
                                  ] })
                                ] })
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.div,
                              {
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.3 },
                                children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all", children: [
                                  /* @__PURE__ */ jsxs("div", { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white/70", children: "Need Optimization" }),
                                    /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                                      dashboardMetrics.needsOptimization > 0 ? "bg-white/30" : "bg-white/20"
                                    ), children: /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-2 h-2 rotate-45",
                                      dashboardMetrics.needsOptimization > 0 ? "bg-white" : "bg-white/70"
                                    ) }) })
                                  ] }),
                                  /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-white", children: dashboardMetrics.needsOptimization.toLocaleString() }),
                                    /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70", children: "Products below 80% score" })
                                  ] })
                                ] })
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.div,
                              {
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.4 },
                                children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all", children: [
                                  /* @__PURE__ */ jsxs("div", { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white/70", children: "Optimized" }),
                                    /* @__PURE__ */ jsx("div", { className: "w-8 h-8 bg-green-500/30 rounded-2xl flex items-center justify-center", children: /* @__PURE__ */ jsx(SuccessAnimation, { size: 16 }) })
                                  ] }),
                                  /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-white", children: dashboardMetrics.optimizedCount.toLocaleString() }),
                                    /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70", children: "Successfully optimized" })
                                  ] })
                                ] })
                              }
                            ),
                            /* @__PURE__ */ jsx(
                              motion.div,
                              {
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.5 },
                                children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all", children: [
                                  /* @__PURE__ */ jsxs("div", { className: "flex flex-row items-center justify-between space-y-0 pb-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white/70", children: "Credits Remaining" }),
                                    /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-8 h-8 rounded-2xl flex items-center justify-center",
                                      (creditBalance == null ? void 0 : creditBalance.remainingCredits) > 0 ? "bg-green-500/30" : "bg-red-500/30"
                                    ), children: /* @__PURE__ */ jsx("div", { className: cn(
                                      "w-3 h-3 rounded-full",
                                      (creditBalance == null ? void 0 : creditBalance.remainingCredits) > 0 ? "bg-green-400" : "bg-red-400"
                                    ) }) })
                                  ] }),
                                  /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
                                    /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-white", children: (creditBalance == null ? void 0 : creditBalance.remainingCredits) || 0 }),
                                    /* @__PURE__ */ jsx("p", { className: "text-xs text-white/70", children: (creditBalance == null ? void 0 : creditBalance.remainingCredits) > 0 ? `${creditBalance.usedCredits}/${creditBalance.totalCredits} used` : "Purchase credits to optimize" })
                                  ] })
                                ] })
                              }
                            )
                          ]
                        }
                      ),
                      /* @__PURE__ */ jsx(
                        motion.div,
                        {
                          initial: { opacity: 0, y: 20 },
                          animate: { opacity: 1, y: 0 },
                          transition: { duration: 0.3, delay: 0.5 },
                          children: /* @__PURE__ */ jsx(
                            BillingStatus,
                            {
                              subscription: billing.subscription,
                              plan: billing.plan,
                              hasAccess: billing.hasAccess,
                              monthlyUsage: {
                                productsOptimized: dashboardMetrics.optimizedCount,
                                totalSpent: ((_a2 = billing.plan) == null ? void 0 : _a2.type) === "pay_per_use" ? dashboardMetrics.optimizedCount * 0.1 : ((_b = billing.plan) == null ? void 0 : _b.price) || 0
                              }
                            }
                          )
                        }
                      ),
                      /* @__PURE__ */ jsx("div", { className: "bg-yellow-500/20 border border-yellow-500/40 rounded-3xl p-6", children: /* @__PURE__ */ jsxs("div", { className: "flex items-start gap-3", children: [
                        /* @__PURE__ */ jsx("div", { className: "w-5 h-5 bg-yellow-400 rounded-2xl flex items-center justify-center flex-shrink-0 mt-0.5", children: /* @__PURE__ */ jsx("div", { className: "w-2 h-2 bg-black rounded-full" }) }),
                        /* @__PURE__ */ jsxs("div", { children: [
                          /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-yellow-300 mb-1", children: "Pro Tip" }),
                          /* @__PURE__ */ jsx("div", { className: "text-sm text-yellow-200 leading-relaxed", children: "Run these audits on your store's main pages to identify SEO opportunities before optimizing your products. Both tools are completely free and provide actionable insights." })
                        ] })
                      ] }) })
                    ]
                  }
                ),
                /* @__PURE__ */ jsxs(
                  motion.div,
                  {
                    className: "bg-white/10 border border-white/20 rounded-3xl p-8 mb-8",
                    initial: { opacity: 0, y: 20 },
                    animate: { opacity: 1, y: 0 },
                    transition: { duration: 0.3, delay: 0.3 },
                    children: [
                      /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
                        /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2 text-white", children: "Optimization Settings" }),
                        /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Configure which fields to optimize and how to process your products" })
                      ] }),
                      /* @__PURE__ */ jsxs(
                        motion.div,
                        {
                          className: "grid grid-cols-1 lg:grid-cols-2 gap-8",
                          initial: { opacity: 0, y: 20 },
                          animate: { opacity: 1, y: 0 },
                          transition: { duration: 0.3, delay: 0.4 },
                          children: [
                            /* @__PURE__ */ jsxs(
                              motion.div,
                              {
                                className: "space-y-4",
                                initial: { opacity: 0, x: -20 },
                                animate: { opacity: 1, x: 0 },
                                transition: { duration: 0.3, delay: 0.5 },
                                children: [
                                  /* @__PURE__ */ jsx("h3", { className: "text-base font-semibold mb-4 text-white", children: "Fields to Update" }),
                                  /* @__PURE__ */ jsx("div", { className: "space-y-3", children: [
                                    { key: "productTitle", label: "Product Title", desc: "Optimize main product titles" },
                                    { key: "productDescription", label: "Product Description", desc: "Enhance product descriptions" },
                                    { key: "seoFields", label: "SEO Meta Tags", desc: "Page title & meta description" },
                                    { key: "handle", label: "URL Handle", desc: "SEO-friendly URLs" },
                                    { key: "imageAlts", label: "Image Alt Texts", desc: "Accessibility & SEO" }
                                  ].map((field) => /* @__PURE__ */ jsxs(
                                    "label",
                                    {
                                      className: cn(
                                        "flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",
                                        settings[`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}`] ? "border-white/40 bg-white/10" : "border-white/20 bg-white/5 hover:bg-white/10"
                                      ),
                                      children: [
                                        /* @__PURE__ */ jsx(
                                          "input",
                                          {
                                            type: "checkbox",
                                            checked: settings[`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}`],
                                            onChange: (e) => {
                                              setSettings((prev) => ({
                                                ...prev,
                                                [`update${field.key.charAt(0).toUpperCase() + field.key.slice(1)}`]: e.target.checked
                                              }));
                                            },
                                            className: "w-4 h-4 accent-white"
                                          }
                                        ),
                                        /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
                                          /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white", children: field.label }),
                                          /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: field.desc })
                                        ] })
                                      ]
                                    },
                                    field.key
                                  )) })
                                ]
                              }
                            ),
                            /* @__PURE__ */ jsxs(
                              motion.div,
                              {
                                className: "space-y-4",
                                initial: { opacity: 0, y: 20 },
                                animate: { opacity: 1, y: 0 },
                                transition: { duration: 0.3, delay: 0.5 },
                                children: [
                                  /* @__PURE__ */ jsx("h3", { className: "text-base font-semibold mb-4 text-white", children: "Processing Options" }),
                                  /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
                                    /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
                                      /* @__PURE__ */ jsxs("div", { children: [
                                        /* @__PURE__ */ jsx("div", { className: "text-sm font-medium mb-1 text-white", children: "Application Mode" }),
                                        /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: "Choose how to apply optimizations" })
                                      ] }),
                                      /* @__PURE__ */ jsx("div", { className: "space-y-3", children: [
                                        { value: true, label: "Auto-apply changes immediately", desc: "Faster processing" },
                                        { value: false, label: "Review before applying", desc: "Manual approval" }
                                      ].map((option) => /* @__PURE__ */ jsxs(
                                        "label",
                                        {
                                          className: cn(
                                            "flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",
                                            settings.autoApply === option.value ? "border-white/40 bg-white/10" : "border-white/20 bg-white/5 hover:bg-white/10"
                                          ),
                                          children: [
                                            /* @__PURE__ */ jsx(
                                              "input",
                                              {
                                                type: "radio",
                                                name: "autoApply",
                                                checked: settings.autoApply === option.value,
                                                onChange: () => {
                                                  setSettings((prev) => ({
                                                    ...prev,
                                                    autoApply: option.value
                                                  }));
                                                },
                                                className: "w-4 h-4 accent-white"
                                              }
                                            ),
                                            /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
                                              /* @__PURE__ */ jsx("div", { className: "text-sm font-medium text-white", children: option.label }),
                                              /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: option.desc })
                                            ] })
                                          ]
                                        },
                                        option.value.toString()
                                      )) })
                                    ] }),
                                    /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
                                      /* @__PURE__ */ jsxs("div", { children: [
                                        /* @__PURE__ */ jsx("div", { className: "text-sm font-medium mb-1 text-white", children: "Batch Size" }),
                                        /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: "Products processed per batch" })
                                      ] }),
                                      /* @__PURE__ */ jsxs(
                                        "select",
                                        {
                                          value: settings.batchSize,
                                          onChange: (e) => {
                                            setSettings((prev) => ({
                                              ...prev,
                                              batchSize: parseInt(e.target.value)
                                            }));
                                          },
                                          className: "w-full p-4 rounded-2xl border border-white/20 bg-white/5 text-white text-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/40 hover:bg-white/10",
                                          children: [
                                            /* @__PURE__ */ jsx("option", { value: 5, className: "bg-black text-white", children: "5 products (Slower, more stable)" }),
                                            /* @__PURE__ */ jsx("option", { value: 10, className: "bg-black text-white", children: "10 products (Recommended)" }),
                                            /* @__PURE__ */ jsx("option", { value: 20, className: "bg-black text-white", children: "20 products (Faster, higher load)" })
                                          ]
                                        }
                                      )
                                    ] })
                                  ] })
                                ]
                              }
                            )
                          ]
                        }
                      )
                    ]
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsx(AnimatePresence, { children: isProcessing && progressData && /* @__PURE__ */ jsx(
            motion.div,
            {
              ref: progressSectionRef,
              initial: { opacity: 0, scale: 0.95, y: 20 },
              animate: { opacity: 1, scale: 1, y: 0 },
              exit: { opacity: 0, scale: 0.95, y: -20 },
              transition: { duration: 0.4, ease: "easeOut" },
              children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8 mb-8 relative overflow-hidden", children: [
                /* @__PURE__ */ jsx("div", { className: "absolute inset-0 opacity-10 pointer-events-none" }),
                /* @__PURE__ */ jsxs("div", { className: "relative z-10", children: [
                  /* @__PURE__ */ jsxs("div", { className: "text-center mb-8", children: [
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        className: "w-20 h-20 bg-white/20 rounded-full mx-auto mb-5 flex items-center justify-center border border-white/30 shadow-lg",
                        whileHover: { scale: 1.05 },
                        transition: { type: "spring", stiffness: 300 },
                        children: /* @__PURE__ */ jsx("div", { className: "w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin" })
                      }
                    ),
                    /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2 text-white", children: "SEO Optimization in Progress" }),
                    /* @__PURE__ */ jsx("p", { className: "text-sm text-white/70", children: progressData.stage })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6", children: [
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        initial: { opacity: 0, y: 20 },
                        animate: { opacity: 1, y: 0 },
                        transition: { duration: 0.4, delay: 0.1 },
                        whileHover: { scale: 1.05 },
                        children: /* @__PURE__ */ jsxs("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: [
                          /* @__PURE__ */ jsx("div", { className: "text-3xl font-bold mb-1 text-white", children: progressData.completedProducts }),
                          /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 font-medium uppercase tracking-wider", children: "Products Completed" })
                        ] })
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        initial: { opacity: 0, y: 20 },
                        animate: { opacity: 1, y: 0 },
                        transition: { duration: 0.4, delay: 0.2 },
                        whileHover: { scale: 1.05 },
                        children: /* @__PURE__ */ jsxs("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: [
                          /* @__PURE__ */ jsx("div", { className: "text-3xl font-bold mb-1 text-white", children: progressData.totalProducts }),
                          /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 font-medium uppercase tracking-wider", children: "Total Products" })
                        ] })
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        initial: { opacity: 0, y: 20 },
                        animate: { opacity: 1, y: 0 },
                        transition: { duration: 0.4, delay: 0.3 },
                        whileHover: { scale: 1.05 },
                        children: /* @__PURE__ */ jsxs("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: [
                          /* @__PURE__ */ jsxs("div", { className: "text-3xl font-bold mb-1 text-white", children: [
                            Math.round((Date.now() - progressData.startTime) / 1e3),
                            "s"
                          ] }),
                          /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 font-medium uppercase tracking-wider", children: "Elapsed Time" })
                        ] })
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        initial: { opacity: 0, y: 20 },
                        animate: { opacity: 1, y: 0 },
                        transition: { duration: 0.4, delay: 0.4 },
                        whileHover: { scale: 1.05 },
                        children: /* @__PURE__ */ jsxs("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: [
                          /* @__PURE__ */ jsxs("div", { className: "text-3xl font-bold mb-1 text-white", children: [
                            progressData.completedProducts > 0 ? Math.round((progressData.totalProducts - progressData.completedProducts) * ((Date.now() - progressData.startTime) / progressData.completedProducts) / 1e3) : Math.round(progressData.totalProducts * 2.5),
                            "s"
                          ] }),
                          /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 font-medium uppercase tracking-wider", children: "Est. Remaining" })
                        ] })
                      }
                    )
                  ] }),
                  /* @__PURE__ */ jsx("div", { className: "mb-6", children: /* @__PURE__ */ jsx(
                    Progress,
                    {
                      value: Math.round(progressData.completedProducts / progressData.totalProducts * 100),
                      className: "h-3"
                    }
                  ) }),
                  progressData.currentProduct && /* @__PURE__ */ jsxs("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 mb-5 text-center", children: [
                    /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 mb-2 font-medium uppercase tracking-wider", children: "Currently Processing:" }),
                    /* @__PURE__ */ jsx("div", { className: "text-base font-semibold truncate text-white", children: progressData.currentProduct })
                  ] }),
                  /* @__PURE__ */ jsx("div", { className: "bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70 italic", children: "💡 Keep this tab open for the best experience. Processing typically takes 2-3 seconds per product." }) })
                ] })
              ] })
            }
          ) }),
          !isProcessing && /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8 mb-8", children: [
            /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
              /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2 text-white", children: "Product Selection" }),
              /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Select products to optimize their SEO titles, descriptions, and viral keywords." })
            ] }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsxs(
                motion.div,
                {
                  initial: { opacity: 0, y: 10 },
                  animate: { opacity: 1, y: 0 },
                  transition: { duration: 0.3, delay: 0.2 },
                  className: "flex gap-4 mb-6 flex-wrap",
                  children: [
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        whileFocus: { scale: 1.02 },
                        className: "flex-1 min-w-[300px]",
                        children: /* @__PURE__ */ jsx(
                          Input,
                          {
                            type: "text",
                            placeholder: "Search products...",
                            value: searchValue,
                            onChange: (e) => setSearchValue(e.target.value),
                            className: "w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                          }
                        )
                      }
                    ),
                    /* @__PURE__ */ jsx(
                      motion.div,
                      {
                        whileHover: { scale: 1.02 },
                        className: "min-w-[200px]",
                        children: /* @__PURE__ */ jsxs(Select, { value: typeFilter, onValueChange: setTypeFilter, children: [
                          /* @__PURE__ */ jsx(SelectTrigger, { className: "bg-white/5 border-white/20 text-white focus:border-white/40", children: /* @__PURE__ */ jsx(SelectValue, { placeholder: "All Types" }) }),
                          /* @__PURE__ */ jsxs(SelectContent, { className: "bg-black border-white/20", children: [
                            /* @__PURE__ */ jsx(SelectItem, { value: "all", className: "text-white hover:bg-white/10", children: "All Types" }),
                            productTypes.filter((type) => type != null).map((type) => /* @__PURE__ */ jsx(SelectItem, { value: type, className: "text-white hover:bg-white/10", children: type }, type))
                          ] })
                        ] })
                      }
                    )
                  ]
                }
              ),
              /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-4 py-3 border-b border-white/20", children: [
                /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3", children: [
                  /* @__PURE__ */ jsx(
                    motion.div,
                    {
                      whileHover: { scale: 1.05 },
                      whileTap: { scale: 0.95 },
                      transition: { type: "spring", stiffness: 400, damping: 17 },
                      children: /* @__PURE__ */ jsx(
                        Button,
                        {
                          onClick: handleSelectAll,
                          size: "sm",
                          className: "text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 bg-transparent border-blue-400/50",
                          children: (() => {
                            const allSelected = filteredProducts.every((p) => selectedProducts.includes(p.id));
                            return allSelected ? "Deselect All" : "Select All";
                          })()
                        }
                      )
                    }
                  ),
                  /* @__PURE__ */ jsx(Separator, { orientation: "vertical", className: "h-4 bg-white/20" }),
                  /* @__PURE__ */ jsx(
                    motion.div,
                    {
                      whileHover: { scale: 1.05 },
                      whileTap: { scale: 0.95 },
                      transition: { type: "spring", stiffness: 400, damping: 17 },
                      children: /* @__PURE__ */ jsx(
                        Button,
                        {
                          onClick: handleSelectNonOptimized,
                          size: "sm",
                          className: "text-green-400 hover:text-green-300 hover:bg-green-500/20 bg-transparent border-green-400/50",
                          children: (() => {
                            const nonOptimizedProducts = filteredProducts.filter(
                              (p) => p.seoScore < 80
                            );
                            const allNonOptimizedSelected = nonOptimizedProducts.every((p) => selectedProducts.includes(p.id));
                            return allNonOptimizedSelected && nonOptimizedProducts.length > 0 ? "Deselect Needs Optimization" : "Select Needs Optimization";
                          })()
                        }
                      )
                    }
                  ),
                  /* @__PURE__ */ jsxs("span", { className: "text-sm text-white/70", children: [
                    selectedProducts.length,
                    " of ",
                    filteredProducts.length,
                    " selected",
                    (() => {
                      const needsOptimizationCount = filteredProducts.filter(
                        (p) => p.seoScore < 80
                      ).length;
                      return needsOptimizationCount > 0 ? ` (${needsOptimizationCount} need optimization)` : "";
                    })()
                  ] })
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "text-sm text-white/70", children: [
                  "Showing ",
                  filteredProducts.length,
                  " products"
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "border border-white/20 rounded-2xl overflow-hidden", children: [
                /* @__PURE__ */ jsxs("div", { className: "bg-white/5 px-4 py-3 border-b border-white/20 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center text-xs font-semibold text-white/70 uppercase tracking-wider min-w-[600px]", children: [
                  /* @__PURE__ */ jsx("div", {}),
                  /* @__PURE__ */ jsx("div", { children: "Product" }),
                  /* @__PURE__ */ jsx("div", { children: "Type" }),
                  /* @__PURE__ */ jsx("div", { children: "SEO Score" }),
                  /* @__PURE__ */ jsx("div", { children: "Status" })
                ] }),
                /* @__PURE__ */ jsx("div", { className: "max-h-96 overflow-y-auto overflow-x-auto", children: filteredProducts.length === 0 ? /* @__PURE__ */ jsxs("div", { className: "py-12 px-4 text-center text-white/70", children: [
                  /* @__PURE__ */ jsx("div", { className: "text-base mb-2", children: "No products found" }),
                  /* @__PURE__ */ jsx("div", { className: "text-sm", children: "Try adjusting your search or filter criteria" })
                ] }) : filteredProducts.map((product, index) => /* @__PURE__ */ jsxs(
                  motion.div,
                  {
                    initial: { opacity: 0, y: 20 },
                    animate: { opacity: 1, y: 0 },
                    transition: {
                      duration: 0.3,
                      delay: index * 0.05,
                      ease: "easeOut"
                    },
                    whileHover: {
                      scale: 1.01,
                      transition: { duration: 0.2 }
                    },
                    className: "p-4 border-b border-white/10 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center cursor-pointer min-w-[600px] rounded-sm hover:bg-white/5",
                    onClick: () => handleProductSelect(product.id),
                    children: [
                      /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(
                        Checkbox,
                        {
                          checked: selectedProducts.includes(product.id),
                          onCheckedChange: () => handleProductSelect(product.id),
                          className: "border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"
                        }
                      ) }),
                      /* @__PURE__ */ jsxs("div", { className: "min-w-0 overflow-hidden", children: [
                        /* @__PURE__ */ jsx("div", { className: "text-sm font-medium mb-1 truncate text-white", children: product.title }),
                        /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 truncate", children: product.description || "No description" })
                      ] }),
                      /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 truncate min-w-0", children: product.type }),
                      /* @__PURE__ */ jsx("div", { className: "min-w-0 overflow-hidden", children: /* @__PURE__ */ jsxs(
                        Badge,
                        {
                          variant: product.seoScore >= 80 ? "default" : product.seoScore >= 60 ? "secondary" : "destructive",
                          className: "text-xs",
                          children: [
                            product.seoScore,
                            "%"
                          ]
                        }
                      ) }),
                      /* @__PURE__ */ jsx("div", { className: "min-w-0 overflow-hidden", children: /* @__PURE__ */ jsx(
                        Badge,
                        {
                          variant: product.status === "optimized" ? "default" : product.status === "processing" ? "secondary" : "outline",
                          className: "text-xs",
                          children: product.status === "pending" ? "Ready" : product.status === "processing" ? "Processing" : product.status === "optimized" ? "Optimized" : "Failed"
                        }
                      ) })
                    ]
                  },
                  product.id
                )) })
              ] })
            ] })
          ] })
        ] }),
        showResults && completionResults ? /* @__PURE__ */ jsx("div", { className: "bg-black py-20 px-6", children: /* @__PURE__ */ jsx("div", { className: "max-w-6xl mx-auto", children: /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8 mb-8", children: /* @__PURE__ */ jsxs("div", { className: "pt-2", children: [
          /* @__PURE__ */ jsxs("div", { className: "text-center mb-8", children: [
            /* @__PURE__ */ jsx("div", { className: "w-20 h-20 bg-white rounded-full mx-auto mb-5 flex items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "text-2xl text-black", children: "✓" }) }),
            /* @__PURE__ */ jsx("h1", { className: "text-3xl font-bold mb-2 tracking-tight text-white", children: "SEO Optimization Complete!" }),
            /* @__PURE__ */ jsx("p", { className: "text-base text-white/70", children: "Your products are now optimized with viral keywords and enhanced SEO" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8", children: [
            /* @__PURE__ */ jsxs("div", { className: "text-center bg-white/5 border border-white/20 rounded-2xl p-6", children: [
              /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2 text-white", children: completionResults.successCount }),
              /* @__PURE__ */ jsx("div", { className: "text-sm font-semibold mb-1 text-white", children: "Products Optimized" }),
              /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: "Successfully enhanced" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "text-center bg-white/5 border border-white/20 rounded-2xl p-6", children: [
              /* @__PURE__ */ jsxs("div", { className: "text-4xl font-bold mb-2 text-white", children: [
                Math.round((completionResults.processingTime || 0) / 1e3),
                "s"
              ] }),
              /* @__PURE__ */ jsx("div", { className: "text-sm font-semibold mb-1 text-white", children: "Processing Time" }),
              /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: "Lightning fast" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "text-center bg-white/5 border border-white/20 rounded-2xl p-6", children: [
              /* @__PURE__ */ jsxs("div", { className: "text-4xl font-bold mb-2 text-white", children: [
                ((_c = completionResults.summary) == null ? void 0 : _c.averageImprovementScore) || 85,
                "%"
              ] }),
              /* @__PURE__ */ jsx("div", { className: "text-sm font-semibold mb-1 text-white", children: "SEO Score" }),
              /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70", children: "Average improvement" })
            ] })
          ] }),
          ((_e = (_d = completionResults.summary) == null ? void 0 : _d.topViralKeywords) == null ? void 0 : _e.length) > 0 && /* @__PURE__ */ jsxs("div", { className: "mb-6 bg-white/5 border border-white/20 rounded-2xl p-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3 mb-4", children: [
              /* @__PURE__ */ jsx("div", { className: "w-6 h-6 bg-white rounded flex items-center justify-center text-xs text-black font-medium", children: "#" }),
              /* @__PURE__ */ jsx("div", { className: "text-base font-semibold text-white", children: "Top Viral Keywords Added:" })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "flex gap-2 flex-wrap", children: completionResults.summary.topViralKeywords.slice(0, 5).map((keyword, index) => /* @__PURE__ */ jsx(Badge, { variant: "secondary", className: "text-xs bg-white/20 text-white border-white/30", children: keyword }, index)) })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "mb-6 bg-white/5 border border-white/20 rounded-2xl p-6 text-center", children: [
            /* @__PURE__ */ jsx("div", { className: "text-lg font-semibold mb-2 text-white", children: "🚀 Your Products Are Now SEO Optimized!" }),
            /* @__PURE__ */ jsx("div", { className: "text-sm text-white/70 leading-relaxed mb-3", children: "Each product now has optimized titles, descriptions, and viral keywords designed to boost your search rankings and drive more traffic to your store." }),
            /* @__PURE__ */ jsx("div", { className: "text-xs text-white/70 italic", children: "💡 Changes have been automatically applied to your Shopify store" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "flex gap-3 justify-center flex-wrap", children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: () => window.location.reload(),
                size: "lg",
                className: "bg-white text-black hover:bg-gray-100 font-bold rounded-2xl",
                children: "Optimize More Products"
              }
            ),
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: () => setShowResults(false),
                size: "lg",
                className: "border border-white/20 bg-white/10 text-white hover:bg-white/20 rounded-2xl",
                children: "Continue Optimizing"
              }
            )
          ] })
        ] }) }) }) }) : null,
        showPayPerUseModal && /* @__PURE__ */ jsx(
          PayPerUseConfirmation,
          {
            productCount: selectedProducts.length,
            selectedProducts,
            onConfirm: handlePayPerUseConfirm,
            onCancel: handlePayPerUseCancel,
            isProcessing: payPerUseBilling.isProcessing,
            csrfToken
          }
        )
      ]
    }
  );
}
const route14 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$4,
  default: SeoOptimizationDashboard,
  loader: loader$7
}, Symbol.toStringTag, { value: "Module" }));
function calculateSeoScore(product) {
  var _a2, _b;
  let score = 0;
  if (product.title) {
    score += Math.min(30, product.title.length > 10 ? 30 : 15);
  }
  if (product.description && product.description.length > 50) {
    score += 25;
  } else if (product.description) {
    score += 10;
  }
  if ((_a2 = product.seo) == null ? void 0 : _a2.title) {
    score += product.seo.title.length <= 70 ? 25 : 15;
  }
  if ((_b = product.seo) == null ? void 0 : _b.description) {
    score += product.seo.description.length <= 160 ? 20 : 10;
  }
  return Math.min(100, score);
}
const loader$6 = async ({ request }) => {
  var _a2, _b, _c, _d, _e, _f, _g, _h, _i;
  const { admin } = await authenticate.admin(request);
  const url = new URL(request.url);
  const page = parseInt(url.searchParams.get("page") || "1");
  const search = url.searchParams.get("search") || "";
  try {
    const allProducts = [];
    let hasNextPage = true;
    let cursor = null;
    let batchCount = 0;
    console.log("📦 Fetching all products using pagination...");
    while (hasNextPage && batchCount < 20) {
      batchCount++;
      console.log(`📦 Fetching batch ${batchCount}...`);
      const response = await admin.graphql(`
        query getProductsWithSeo($first: Int!, $after: String, $query: String) {
          products(first: $first, after: $after, query: $query) {
            edges {
              node {
                id
                title
                description
                productType
                vendor
                handle
                seo {
                  title
                  description
                }
                createdAt
                updatedAt
              }
              cursor
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `, {
        variables: {
          first: 50,
          // Smaller batch size to keep query cost low
          after: cursor,
          query: search ? `title:*${search}* OR description:*${search}*` : void 0
        }
      });
      const responseJson = await response.json();
      const batchProducts = ((_c = (_b = (_a2 = responseJson.data) == null ? void 0 : _a2.products) == null ? void 0 : _b.edges) == null ? void 0 : _c.map((edge) => {
        var _a3, _b2;
        const product = edge.node;
        return {
          id: product.id.replace("gid://shopify/Product/", ""),
          title: product.title,
          description: product.description || "",
          type: product.productType || "Uncategorized",
          vendor: product.vendor || "Unknown",
          handle: product.handle,
          seoTitle: ((_a3 = product.seo) == null ? void 0 : _a3.title) || "",
          seoDescription: ((_b2 = product.seo) == null ? void 0 : _b2.description) || "",
          seoScore: calculateSeoScore(product),
          status: "pending",
          createdAt: product.createdAt,
          updatedAt: product.updatedAt
        };
      })) || [];
      allProducts.push(...batchProducts);
      hasNextPage = ((_f = (_e = (_d = responseJson.data) == null ? void 0 : _d.products) == null ? void 0 : _e.pageInfo) == null ? void 0 : _f.hasNextPage) || false;
      cursor = (_i = (_h = (_g = responseJson.data) == null ? void 0 : _g.products) == null ? void 0 : _h.pageInfo) == null ? void 0 : _i.endCursor;
      if (hasNextPage) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }
    console.log(`✅ Successfully fetched ${allProducts.length} products in ${batchCount} batches`);
    const products = allProducts;
    const productTypes = [...new Set(products.map((p) => p.type))].filter(Boolean);
    return json({
      products,
      productTypes,
      pagination: {
        currentPage: page,
        hasNextPage: false,
        // We fetched all available products
        hasPreviousPage: false
      },
      totalProducts: products.length
    });
  } catch (error) {
    console.error("Error loading products:", error);
    return json({
      products: [],
      productTypes: [],
      pagination: { currentPage: 1, hasNextPage: false, hasPreviousPage: false },
      totalProducts: 0,
      error: "Failed to load products"
    });
  }
};
const route15 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$6
}, Symbol.toStringTag, { value: "Module" }));
function AdditionalPage() {
  return /* @__PURE__ */ jsxs(Page, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "Additional page" }),
    /* @__PURE__ */ jsxs(Layout, { children: [
      /* @__PURE__ */ jsx(Layout.Section, { children: /* @__PURE__ */ jsx(Card$1, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "300", children: [
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using",
          " ",
          /* @__PURE__ */ jsx(
            Link$1,
            {
              url: "https://shopify.dev/docs/apps/tools/app-bridge",
              target: "_blank",
              removeUnderline: true,
              children: "App Bridge"
            }
          ),
          "."
        ] }),
        /* @__PURE__ */ jsxs(Text, { as: "p", variant: "bodyMd", children: [
          "To create your own page and have it show up in the app navigation, add a page inside ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes" }),
          ", and a link to it in the ",
          /* @__PURE__ */ jsx(Code, { children: "<NavMenu>" }),
          " component found in ",
          /* @__PURE__ */ jsx(Code, { children: "app/routes/app.jsx" }),
          "."
        ] })
      ] }) }) }),
      /* @__PURE__ */ jsx(Layout.Section, { variant: "oneThird", children: /* @__PURE__ */ jsx(Card$1, { children: /* @__PURE__ */ jsxs(BlockStack, { gap: "200", children: [
        /* @__PURE__ */ jsx(Text, { as: "h2", variant: "headingMd", children: "Resources" }),
        /* @__PURE__ */ jsx(List, { children: /* @__PURE__ */ jsx(List.Item, { children: /* @__PURE__ */ jsx(
          Link$1,
          {
            url: "https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",
            target: "_blank",
            removeUnderline: true,
            children: "App nav best practices"
          }
        ) }) })
      ] }) }) })
    ] })
  ] });
}
function Code({ children }) {
  return /* @__PURE__ */ jsx(
    Box,
    {
      as: "span",
      padding: "025",
      paddingInlineStart: "100",
      paddingInlineEnd: "100",
      background: "bg-surface-active",
      borderWidth: "025",
      borderColor: "border",
      borderRadius: "100",
      children: /* @__PURE__ */ jsx("code", { children })
    }
  );
}
const route16 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: AdditionalPage
}, Symbol.toStringTag, { value: "Module" }));
const loader$5 = requireAuth(async () => {
  return json({});
});
const action$3 = requireAuth(async () => {
  return json({ success: true, message: "Settings saved!" });
});
function Settings() {
  var _a2;
  const fetcher = useFetcher();
  const shopify2 = useAppBridge();
  const [titlePrompt, setTitlePrompt] = useState(
    "Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"
  );
  const [descriptionPrompt, setDescriptionPrompt] = useState(
    "Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}"
  );
  const isLoading = fetcher.state === "submitting";
  useEffect(() => {
    if (fetcher.data && "success" in fetcher.data && fetcher.data.success) {
      shopify2.toast.show(fetcher.data.message || "Settings saved!");
    } else if (fetcher.data && "error" in fetcher.data && fetcher.data.error) {
      shopify2.toast.show(fetcher.data.error, { isError: true });
    }
  }, [fetcher.data, shopify2]);
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedTitlePrompt = sessionStorage.getItem("title_prompt_template");
      const savedDescriptionPrompt = sessionStorage.getItem("description_prompt_template");
      if (savedTitlePrompt) setTitlePrompt(savedTitlePrompt);
      if (savedDescriptionPrompt) setDescriptionPrompt(savedDescriptionPrompt);
    }
  }, []);
  useCallback((value) => {
    setTitlePrompt(value);
  }, []);
  useCallback((value) => {
    setDescriptionPrompt(value);
  }, []);
  const handleSave = useCallback(() => {
    if (typeof window !== "undefined") {
      sessionStorage.setItem("title_prompt_template", titlePrompt);
      sessionStorage.setItem("description_prompt_template", descriptionPrompt);
    }
    const formData = new FormData();
    formData.append("action", "save");
    fetcher.submit(formData, { method: "POST" });
  }, [titlePrompt, descriptionPrompt, fetcher]);
  useCallback(() => {
    setTitlePrompt(
      "Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"
    );
    setDescriptionPrompt(
      "Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}"
    );
  }, []);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "AI BULK SEO Settings" }),
    /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-black text-white", children: [
      /* @__PURE__ */ jsx("div", { className: "bg-black py-20 px-6", children: /* @__PURE__ */ jsx("div", { className: "max-w-6xl mx-auto text-center", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center mb-12", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/logo.png",
            alt: "AI BULK SEO Logo",
            className: "w-16 h-16 mb-6 rounded-2xl shadow-2xl",
            style: { filter: "brightness(1.1) contrast(1.1)" }
          }
        ),
        /* @__PURE__ */ jsx("h1", { style: {
          fontSize: "clamp(3rem, 8vw, 6rem)",
          fontWeight: 900,
          lineHeight: 0.9,
          letterSpacing: "-0.05em",
          marginBottom: "1rem",
          color: "white"
        }, children: "SETTINGS" }),
        /* @__PURE__ */ jsx("p", { style: {
          fontSize: "clamp(1.25rem, 3vw, 1.75rem)",
          fontWeight: 300,
          color: "#cbd5e1",
          maxWidth: "40rem",
          margin: "0 auto"
        }, children: "Configure your AI BULK SEO optimization preferences" })
      ] }) }) }),
      /* @__PURE__ */ jsx("div", { className: "px-6 pb-20", children: /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto space-y-8", children: [
        /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
          /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
            /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2", children: "API Configuration" }),
            /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Configure your API keys for enhanced functionality" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "Google PageSpeed API Key" }),
              /* @__PURE__ */ jsx(
                Input,
                {
                  type: "password",
                  placeholder: "Enter your Google PageSpeed API key",
                  className: "w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                }
              ),
              /* @__PURE__ */ jsx("p", { className: "text-xs text-white/60", children: "Required for detailed Core Web Vitals analysis" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "OpenAI API Key" }),
              /* @__PURE__ */ jsx(
                Input,
                {
                  type: "password",
                  placeholder: "Enter your OpenAI API key",
                  className: "w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                }
              ),
              /* @__PURE__ */ jsx("p", { className: "text-xs text-white/60", children: "Required for advanced content optimization" })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
          /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
            /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2", children: "SEO Prompt Templates" }),
            /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Customize AI prompts for product optimization" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "Title Generation Prompt" }),
              /* @__PURE__ */ jsx(
                "textarea",
                {
                  value: titlePrompt,
                  onChange: (e) => setTitlePrompt(e.target.value),
                  className: "w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none",
                  placeholder: "Enter your title generation prompt...",
                  style: { lineHeight: 1.6 }
                }
              )
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "Description Generation Prompt" }),
              /* @__PURE__ */ jsx(
                "textarea",
                {
                  value: descriptionPrompt,
                  onChange: (e) => setDescriptionPrompt(e.target.value),
                  className: "w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none",
                  placeholder: "Enter your description generation prompt...",
                  style: { lineHeight: 1.6 }
                }
              )
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
          /* @__PURE__ */ jsxs("div", { className: "mb-8", children: [
            /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-2", children: "Automation Settings" }),
            /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Configure bulk processing and automation preferences" })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "Batch Size" }),
              /* @__PURE__ */ jsx(
                Input,
                {
                  type: "number",
                  defaultValue: "10",
                  min: "1",
                  max: "50",
                  className: "w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                }
              ),
              /* @__PURE__ */ jsx("p", { className: "text-xs text-white/60", children: "Number of products to process simultaneously" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
              /* @__PURE__ */ jsx("label", { className: "text-sm font-semibold text-white", children: "Processing Delay" }),
              /* @__PURE__ */ jsx(
                Input,
                {
                  type: "number",
                  defaultValue: "1000",
                  min: "500",
                  max: "5000",
                  className: "w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"
                }
              ),
              /* @__PURE__ */ jsx("p", { className: "text-xs text-white/60", children: "Delay between requests (milliseconds)" })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "flex justify-center pt-8", children: /* @__PURE__ */ jsx(
          Button,
          {
            onClick: handleSave,
            disabled: isLoading,
            size: "lg",
            className: "bg-white text-black hover:bg-gray-100 font-bold py-4 px-12 text-lg rounded-2xl transition-colors duration-200",
            children: isLoading ? "Saving Settings..." : "Save Settings"
          }
        ) }),
        ((_a2 = fetcher.data) == null ? void 0 : _a2.success) && /* @__PURE__ */ jsx("div", { className: "mt-8", children: /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-2xl p-6 text-center", children: /* @__PURE__ */ jsx("div", { className: "text-white font-semibold text-lg", children: "✅ Settings saved successfully!" }) }) })
      ] }) })
    ] })
  ] });
}
const route17 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$3,
  default: Settings,
  loader: loader$5
}, Symbol.toStringTag, { value: "Module" }));
function Card({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "card",
      className: cn(
        "bg-black text-white flex flex-col rounded-3xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.01] hover:border-white/30 backdrop-blur-sm group",
        className
      ),
      ...props
    }
  );
}
function CardHeader({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "card-header",
      className: cn(
        "flex flex-col space-y-3 p-8 pb-6",
        className
      ),
      ...props
    }
  );
}
function CardTitle({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "card-title",
      className: cn("text-2xl font-black leading-tight text-white tracking-tight", className),
      ...props
    }
  );
}
function CardDescription({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "card-description",
      className: cn("text-white/70 text-base leading-relaxed font-medium", className),
      ...props
    }
  );
}
function CardContent({ className, ...props }) {
  return /* @__PURE__ */ jsx(
    "div",
    {
      "data-slot": "card-content",
      className: cn("px-8 pb-8", className),
      ...props
    }
  );
}
function getBillingErrorMessage(error) {
  const lowerError = error.toLowerCase();
  if (lowerError.includes("authentication") || lowerError.includes("unauthorized")) {
    return {
      type: "error",
      title: "Authentication Required",
      message: "Please refresh the page and try again. If the problem persists, contact support.",
      action: {
        label: "Refresh Page",
        onClick: () => window.location.reload()
      }
    };
  }
  if (lowerError.includes("rate limit") || lowerError.includes("too many requests")) {
    return {
      type: "warning",
      title: "Too Many Requests",
      message: "Please wait a moment before trying again. We limit requests to ensure system stability.",
      action: {
        label: "Try Again",
        onClick: () => window.location.reload()
      }
    };
  }
  if (lowerError.includes("csrf") || lowerError.includes("security token")) {
    return {
      type: "error",
      title: "Security Check Failed",
      message: "For your security, please refresh the page and try again.",
      action: {
        label: "Refresh Page",
        onClick: () => window.location.reload()
      }
    };
  }
  if (lowerError.includes("validation") || lowerError.includes("invalid")) {
    return {
      type: "error",
      title: "Invalid Information",
      message: "Please check your input and try again. Make sure all required fields are filled correctly."
    };
  }
  if (lowerError.includes("plan") && lowerError.includes("not found")) {
    return {
      type: "error",
      title: "Plan Not Available",
      message: "The selected plan is no longer available. Please choose a different plan.",
      action: {
        label: "View Plans",
        onClick: () => window.location.href = "/app/billing/pricing"
      }
    };
  }
  if (lowerError.includes("subscription")) {
    if (lowerError.includes("already exists") || lowerError.includes("duplicate")) {
      return {
        type: "warning",
        title: "Already Subscribed",
        message: "You already have an active subscription. Check your billing dashboard for details.",
        action: {
          label: "View Dashboard",
          onClick: () => window.location.href = "/app/billing"
        }
      };
    }
    if (lowerError.includes("cancelled") || lowerError.includes("expired")) {
      return {
        type: "error",
        title: "Subscription Issue",
        message: "There was an issue with your subscription. Please try subscribing again.",
        action: {
          label: "Try Again",
          onClick: () => window.location.href = "/app/billing/pricing"
        }
      };
    }
  }
  if (lowerError.includes("payment") || lowerError.includes("billing") || lowerError.includes("charge")) {
    return {
      type: "error",
      title: "Payment Issue",
      message: "There was a problem processing your payment. Please check your payment method and try again.",
      action: {
        label: "Try Again",
        onClick: () => window.location.reload()
      }
    };
  }
  if (lowerError.includes("product count")) {
    if (lowerError.includes("exceed") || lowerError.includes("maximum")) {
      return {
        type: "warning",
        title: "Too Many Products",
        message: "You can optimize up to 1,000 products per purchase. Please reduce the number of selected products."
      };
    }
    if (lowerError.includes("minimum") || lowerError.includes("at least")) {
      return {
        type: "warning",
        title: "No Products Selected",
        message: "Please select at least one product to optimize."
      };
    }
  }
  if (lowerError.includes("network") || lowerError.includes("connection") || lowerError.includes("timeout")) {
    return {
      type: "error",
      title: "Connection Problem",
      message: "Please check your internet connection and try again.",
      action: {
        label: "Retry",
        onClick: () => window.location.reload()
      }
    };
  }
  if (lowerError.includes("server") || lowerError.includes("internal")) {
    return {
      type: "error",
      title: "Server Error",
      message: "Something went wrong on our end. Please try again in a few moments.",
      action: {
        label: "Try Again",
        onClick: () => window.location.reload()
      }
    };
  }
  return {
    type: "error",
    title: "Something Went Wrong",
    message: "An unexpected error occurred. Please try again or contact support if the problem persists.",
    action: {
      label: "Try Again",
      onClick: () => window.location.reload()
    }
  };
}
function getBillingSuccessMessage(action2, details) {
  switch (action2) {
    case "subscription_created":
      return {
        type: "success",
        title: "Subscription Activated!",
        message: `Your ${details == null ? void 0 : details.planName} is now active. You can start optimizing your products right away.`,
        action: {
          label: "Start Optimizing",
          onClick: () => window.location.href = "/app"
        }
      };
    case "subscription_cancelled":
      return {
        type: "info",
        title: "Subscription Cancelled",
        message: "Your subscription has been cancelled. You can still use the app until your current billing period ends.",
        action: {
          label: "View Dashboard",
          onClick: () => window.location.href = "/app/billing"
        }
      };
    case "purchase_created":
      return {
        type: "success",
        title: "Purchase Successful!",
        message: `You can now optimize ${(details == null ? void 0 : details.productCount) || "your selected"} products. The optimization will begin shortly.`,
        action: {
          label: "View Products",
          onClick: () => window.location.href = "/app"
        }
      };
    case "trial_started":
      return {
        type: "success",
        title: "Free Trial Started!",
        message: "Your free trial is now active. Explore all features and optimize your products for free.",
        action: {
          label: "Get Started",
          onClick: () => window.location.href = "/app"
        }
      };
    default:
      return {
        type: "success",
        title: "Success!",
        message: "Your action was completed successfully."
      };
  }
}
function PricingSelection({
  plans,
  onPlanSelect,
  selectedPlan,
  showPayPerUse = true,
  csrfToken,
  hasActiveSubscription = false
}) {
  const fetcher = useFetcher();
  const navigate = useNavigate();
  const shopify2 = useAppBridge();
  const [localSelectedPlan, setLocalSelectedPlan] = React.useState(selectedPlan || "annual");
  React.useEffect(() => {
    console.log("🔄 Fetcher state:", fetcher.state);
    console.log("📋 Fetcher data:", fetcher.data);
    if (fetcher.data && typeof fetcher.data === "object") {
      const data = fetcher.data;
      if (data.error) {
        console.error("❌ Billing error:", data.error);
        const userMessage = getBillingErrorMessage(data.error);
        const errorDiv = document.createElement("div");
        errorDiv.innerHTML = `
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">⚠️</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${userMessage.title}</h4>
                <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">${userMessage.message}</p>
                ${userMessage.action ? `
                  <button onclick="${userMessage.action.onClick.toString()}()" style="
                    background: #dc2626; color: white; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">${userMessage.action.label}</button>
                ` : ""}
              </div>
              <button onclick="this.parentElement.parentElement.remove()" style="
                background: none; border: none; color: #991b1b; cursor: pointer; font-size: 18px;
              ">×</button>
            </div>
          </div>
        `;
        document.body.appendChild(errorDiv);
        setTimeout(() => {
          if (errorDiv.parentElement) {
            errorDiv.remove();
          }
        }, 1e4);
      } else if (data.success && data.confirmationUrl) {
        console.log("✅ Subscription created successfully, redirecting to confirmation...");
        console.log("🔗 Confirmation URL:", data.confirmationUrl.substring(0, 100) + "...");
        const successMessage = getBillingSuccessMessage("subscription_created", {
          planName: "subscription"
        });
        const successDiv = document.createElement("div");
        successDiv.innerHTML = `
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">✅</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${successMessage.title}</h4>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Redirecting to payment...</p>
              </div>
            </div>
          </div>
        `;
        document.body.appendChild(successDiv);
        try {
          setTimeout(() => {
            if (window.top) {
              window.top.location.href = data.confirmationUrl;
            } else {
              window.location.href = data.confirmationUrl;
            }
          }, 1500);
        } catch (error) {
          console.error("❌ Failed to redirect to confirmation URL:", error);
          successDiv.remove();
          const redirectErrorDiv = document.createElement("div");
          redirectErrorDiv.innerHTML = `
            <div style="
              position: fixed; top: 20px; right: 20px; z-index: 1000;
              background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
              padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            ">
              <div style="display: flex; align-items: start; gap: 12px;">
                <div style="color: #ffffff; font-size: 20px;">⚠️</div>
                <div style="flex: 1;">
                  <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">Redirect Issue</h4>
                  <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Subscription created successfully, but failed to redirect. Please refresh the page.</p>
                  <button onclick="window.location.reload()" style="
                    background: #ffffff; color: #000000; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">Refresh Page</button>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                  background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 18px;
                ">×</button>
              </div>
            </div>
          `;
          document.body.appendChild(redirectErrorDiv);
        }
      }
    }
    if (fetcher.state === "loading") {
      console.log("⏳ Billing request in progress...");
    }
    if (fetcher.state === "idle" && fetcher.data) {
      console.log("✅ Billing request completed");
    }
  }, [fetcher.data, fetcher.state]);
  const handlePlanSelect = (planId) => {
    setLocalSelectedPlan(planId);
    onPlanSelect == null ? void 0 : onPlanSelect(planId);
  };
  const handleStartTrial = (planId) => {
    console.log("🔄 Starting trial for plan:", planId);
    console.log("🔐 CSRF token available:", !!csrfToken);
    if (!planId || typeof planId !== "string") {
      console.error("❌ Invalid plan ID:", planId);
      alert("Error: Invalid plan selected");
      return;
    }
    if (!csrfToken) {
      console.error("❌ No CSRF token available");
      alert("Security token missing. Please refresh the page and try again.");
      return;
    }
    if (fetcher.state === "submitting" || fetcher.state === "loading") {
      console.log("⏳ Already processing subscription request...");
      return;
    }
    const formData = new FormData();
    formData.append("action", "create_subscription");
    formData.append("planId", planId);
    if (csrfToken) {
      formData.append("csrfToken", csrfToken);
    }
    console.log("📤 Submitting billing form with data:", {
      action: "create_subscription",
      planId,
      hasCSRF: !!csrfToken
    });
    fetcher.submit(formData, {
      method: "POST"
    });
  };
  const getPlanIcon = (planType) => {
    switch (planType) {
      case "annual":
        return /* @__PURE__ */ jsx(Star, { className: "w-6 h-6 text-yellow-500" });
      case "monthly":
        return /* @__PURE__ */ jsx(Clock, { className: "w-6 h-6 text-blue-500" });
      case "pay_per_use":
        return /* @__PURE__ */ jsx(Zap, { className: "w-6 h-6 text-green-500" });
      default:
        return /* @__PURE__ */ jsx(DollarSign, { className: "w-6 h-6 text-gray-500" });
    }
  };
  const calculateMonthlySavings = () => {
    const annualPlan = plans.find((p) => p.id === "annual");
    const monthlyPlan = plans.find((p) => p.id === "monthly");
    if (annualPlan && monthlyPlan) {
      const annualMonthly = annualPlan.price / 12;
      const savings = (monthlyPlan.price - annualMonthly) * 12;
      return Math.round(savings);
    }
    return 0;
  };
  const shouldShowPayPerUse = showPayPerUse && !hasActiveSubscription;
  const filteredPlans = shouldShowPayPerUse ? plans : plans.filter((p) => p.type !== "pay_per_use");
  console.log("🔍 PricingSelection - showPayPerUse:", showPayPerUse);
  console.log("🔍 PricingSelection - hasActiveSubscription:", hasActiveSubscription);
  console.log("🔍 PricingSelection - shouldShowPayPerUse:", shouldShowPayPerUse);
  console.log("🔍 PricingSelection - filteredPlans:", filteredPlans.map((p) => p.id));
  return /* @__PURE__ */ jsxs("div", { className: "max-w-6xl mx-auto p-6 bg-black text-white", children: [
    /* @__PURE__ */ jsx("div", { className: "text-center mb-12", children: /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6 },
        children: [
          /* @__PURE__ */ jsx("h1", { className: "text-4xl font-bold mb-4 text-white", children: "Choose Your Plan" }),
          /* @__PURE__ */ jsx("p", { className: "text-xl text-white/70 mb-6", children: "Start optimizing your products with our powerful SEO tools" })
        ]
      }
    ) }),
    /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8 mb-12", children: filteredPlans.map((plan, index) => /* @__PURE__ */ jsx(
      motion.div,
      {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6, delay: index * 0.1 },
        children: /* @__PURE__ */ jsxs(
          Card,
          {
            className: `relative h-full transition-all duration-300 hover:shadow-xl cursor-pointer bg-white/10 border-white/20 ${plan.recommended ? "border-2 border-white shadow-lg scale-105" : localSelectedPlan === plan.id ? "border-2 border-blue-400" : "border-white/20 hover:border-white/40"}`,
            onClick: () => handlePlanSelect(plan.id),
            children: [
              plan.recommended && /* @__PURE__ */ jsx("div", { className: "absolute -top-4 left-1/2 transform -translate-x-1/2", children: /* @__PURE__ */ jsxs(Badge, { className: "bg-white text-black px-4 py-1 text-sm font-semibold", children: [
                /* @__PURE__ */ jsx(Star, { className: "w-4 h-4 mr-1" }),
                "Best Value"
              ] }) }),
              /* @__PURE__ */ jsxs(CardHeader, { className: "text-center pb-4", children: [
                /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-4", children: getPlanIcon(plan.type) }),
                /* @__PURE__ */ jsx(CardTitle, { className: "text-2xl font-bold text-white", children: plan.name }),
                /* @__PURE__ */ jsx(CardDescription, { className: "text-base text-white/70", children: plan.description }),
                /* @__PURE__ */ jsx("div", { className: "mt-4", children: plan.type === "pay_per_use" ? /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsxs("span", { className: "text-4xl font-bold text-white", children: [
                    "$",
                    plan.price
                  ] }),
                  /* @__PURE__ */ jsx("span", { className: "text-white/70 ml-2", children: "per product" })
                ] }) : /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsxs("span", { className: "text-4xl font-bold text-white", children: [
                    "$",
                    plan.price
                  ] }),
                  /* @__PURE__ */ jsxs("span", { className: "text-white/70 ml-2", children: [
                    "/",
                    plan.type === "annual" ? "year" : "month"
                  ] }),
                  plan.type === "annual" && /* @__PURE__ */ jsxs("div", { className: "text-sm text-green-400 font-medium mt-1", children: [
                    "Save $",
                    calculateMonthlySavings(),
                    "/year vs monthly"
                  ] }),
                  plan.type === "monthly" && /* @__PURE__ */ jsxs("div", { className: "text-sm text-white/70 mt-1", children: [
                    "Equivalent to $",
                    plan.price.toFixed(2),
                    "/month"
                  ] })
                ] }) })
              ] }),
              /* @__PURE__ */ jsxs(CardContent, { className: "pt-0", children: [
                /* @__PURE__ */ jsx("ul", { className: "space-y-3 mb-6", children: plan.features.map((feature, featureIndex) => /* @__PURE__ */ jsxs("li", { className: "flex items-start gap-3", children: [
                  /* @__PURE__ */ jsx(Check, { className: "w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" }),
                  /* @__PURE__ */ jsx("span", { className: "text-sm text-white/80", children: feature })
                ] }, featureIndex)) }),
                plan.type !== "pay_per_use" ? /* @__PURE__ */ jsx(
                  Button,
                  {
                    onClick: (e) => {
                      e.stopPropagation();
                      handleStartTrial(plan.id);
                    },
                    className: `w-full ${plan.recommended ? "bg-white text-black hover:bg-white/90 disabled:bg-white/60" : "bg-white/20 text-white hover:bg-white/30 disabled:bg-white/10"} disabled:cursor-not-allowed transition-all duration-200`,
                    disabled: fetcher.state === "submitting" || fetcher.state === "loading",
                    children: fetcher.state === "submitting" || fetcher.state === "loading" ? /* @__PURE__ */ jsxs(Fragment, { children: [
                      /* @__PURE__ */ jsx(Clock, { className: "w-4 h-4 mr-2 animate-spin" }),
                      "Processing..."
                    ] }) : /* @__PURE__ */ jsxs(Fragment, { children: [
                      /* @__PURE__ */ jsx(Zap, { className: "w-4 h-4 mr-2" }),
                      "Subscribe Now"
                    ] })
                  }
                ) : /* @__PURE__ */ jsx(
                  Button,
                  {
                    onClick: (e) => {
                      e.stopPropagation();
                      handlePlanSelect(plan.id);
                      console.log("🎯 Pay-per-use selected, navigating to SEO dashboard");
                      shopify2.toast.show("Pay-per-use selected! Select products to optimize and pay only for what you use.", {
                        isError: false
                      });
                      navigate("/app/seo-dashboard");
                    },
                    className: "w-full border-2 border-white/30 bg-transparent text-white hover:bg-white hover:text-black",
                    children: "Select Pay-Per-Use"
                  }
                )
              ] })
            ]
          }
        )
      },
      plan.id
    )) }),
    /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6, delay: 0.4 },
        className: "bg-white/10 border border-white/20 rounded-3xl p-8",
        children: [
          /* @__PURE__ */ jsx("h3", { className: "text-2xl font-bold text-center mb-8 text-white", children: "Plan Comparison" }),
          /* @__PURE__ */ jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxs("table", { className: "w-full", children: [
            /* @__PURE__ */ jsx("thead", { children: /* @__PURE__ */ jsxs("tr", { className: "border-b", children: [
              /* @__PURE__ */ jsx("th", { className: "text-left py-4 px-4", children: "Feature" }),
              /* @__PURE__ */ jsx("th", { className: "text-center py-4 px-4", children: "Annual" }),
              /* @__PURE__ */ jsx("th", { className: "text-center py-4 px-4", children: "Monthly" }),
              /* @__PURE__ */ jsx("th", { className: "text-center py-4 px-4", children: "Pay-Per-Use" })
            ] }) }),
            /* @__PURE__ */ jsxs("tbody", { className: "text-sm", children: [
              /* @__PURE__ */ jsxs("tr", { className: "border-b", children: [
                /* @__PURE__ */ jsx("td", { className: "py-4 px-4 font-medium", children: "Product Optimizations" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Unlimited" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Unlimited" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "$0.10 each" })
              ] }),
              /* @__PURE__ */ jsxs("tr", { className: "border-b", children: [
                /* @__PURE__ */ jsx("td", { className: "py-4 px-4 font-medium", children: "Support Level" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Priority" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Standard" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Standard" })
              ] }),
              /* @__PURE__ */ jsxs("tr", { className: "border-b", children: [
                /* @__PURE__ */ jsx("td", { className: "py-4 px-4 font-medium", children: "Analytics" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Advanced" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Basic" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Basic" })
              ] }),
              /* @__PURE__ */ jsxs("tr", { children: [
                /* @__PURE__ */ jsx("td", { className: "py-4 px-4 font-medium", children: "Monthly Commitment" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Annual only" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "Yes" }),
                /* @__PURE__ */ jsx("td", { className: "text-center py-4 px-4", children: "None" })
              ] })
            ] })
          ] }) })
        ]
      }
    ),
    /* @__PURE__ */ jsxs(
      motion.div,
      {
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.6, delay: 0.6 },
        className: "mt-12 text-center",
        children: [
          /* @__PURE__ */ jsx("h3", { className: "text-xl font-semibold mb-4", children: "Frequently Asked Questions" }),
          /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto", children: [
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h4", { className: "font-medium mb-2", children: "Can I change plans later?" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle." })
            ] }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h4", { className: "font-medium mb-2", children: "How does billing work?" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "You'll be charged immediately upon subscription. Monthly plans bill every 30 days, annual plans bill yearly." })
            ] }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h4", { className: "font-medium mb-2", children: "Is there a setup fee?" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "No setup fees. You only pay the subscription price or per-use charges." })
            ] }),
            /* @__PURE__ */ jsxs("div", { children: [
              /* @__PURE__ */ jsx("h4", { className: "font-medium mb-2", children: "Can I cancel anytime?" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-muted-foreground", children: "Yes, you can cancel your subscription at any time. No long-term contracts required." })
            ] })
          ] })
        ]
      }
    )
  ] });
}
function CreditsDashboard({ balance, history, hasSubscription }) {
  const formatDate = (date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    });
  };
  const getTransactionIcon = (type) => {
    switch (type) {
      case "PURCHASE":
        return "💳";
      case "USAGE":
        return "🔧";
      case "REFUND":
        return "↩️";
      case "BONUS":
        return "🎁";
      default:
        return "📝";
    }
  };
  const getTransactionColor = (type) => {
    switch (type) {
      case "PURCHASE":
        return "text-green-400";
      case "USAGE":
        return "text-blue-400";
      case "REFUND":
        return "text-yellow-400";
      case "BONUS":
        return "text-purple-400";
      default:
        return "text-gray-400";
    }
  };
  if (hasSubscription) {
    return /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "text-6xl mb-4", children: "🚀" }),
      /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold mb-2", children: "Unlimited Access" }),
      /* @__PURE__ */ jsx("p", { className: "text-white/70 text-lg", children: "You have an active subscription with unlimited product optimizations" }),
      /* @__PURE__ */ jsx("div", { className: "mt-6 inline-flex items-center bg-green-500/20 border border-green-500/40 rounded-2xl px-6 py-3", children: /* @__PURE__ */ jsx("div", { className: "text-green-300 font-bold", children: "✅ Active Subscription" }) })
    ] }) });
  }
  return /* @__PURE__ */ jsxs("div", { className: "space-y-8", children: [
    /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-6", children: "Optimization Credits" }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6 mb-8", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2 text-blue-400", children: balance.totalCredits }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Total Purchased" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2 text-orange-400", children: balance.usedCredits }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Used" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2 text-green-400", children: balance.remainingCredits }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Remaining" })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between text-sm text-white/70 mb-2", children: [
          /* @__PURE__ */ jsx("span", { children: "Credits Used" }),
          /* @__PURE__ */ jsxs("span", { children: [
            balance.usedCredits,
            " / ",
            balance.totalCredits
          ] })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "w-full bg-white/10 rounded-full h-3", children: /* @__PURE__ */ jsx(
          "div",
          {
            className: "bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-300",
            style: {
              width: balance.totalCredits > 0 ? `${Math.min(100, balance.usedCredits / balance.totalCredits * 100)}%` : "0%"
            }
          }
        ) })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "bg-white/5 rounded-2xl p-4", children: /* @__PURE__ */ jsxs("div", { className: "text-sm text-white/70", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-2", children: [
          /* @__PURE__ */ jsx("span", { children: "💡 Each credit = 1 product optimization" }),
          /* @__PURE__ */ jsx("span", { children: "💰 $0.10 per credit" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-xs text-white/50", children: [
          "Last updated: ",
          formatDate(balance.lastUpdated)
        ] })
      ] }) })
    ] }),
    history.length > 0 && /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-6", children: "Credit History" }),
      /* @__PURE__ */ jsx("div", { className: "space-y-4 max-h-96 overflow-y-auto", children: history.map((transaction, index) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between py-3 border-b border-white/10 last:border-b-0", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-4", children: [
          /* @__PURE__ */ jsx("div", { className: "text-2xl", children: getTransactionIcon(transaction.type) }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("div", { className: "font-semibold", children: transaction.description }),
            /* @__PURE__ */ jsxs("div", { className: "text-sm text-white/60", children: [
              formatDate(transaction.date),
              transaction.referenceId && /* @__PURE__ */ jsxs("span", { className: "ml-2 text-white/40", children: [
                "#",
                transaction.referenceId.substring(0, 8)
              ] })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: `font-bold text-lg ${getTransactionColor(transaction.type)}`, children: [
          transaction.amount > 0 ? "+" : "",
          transaction.amount
        ] })
      ] }, index)) })
    ] }),
    balance.totalCredits === 0 && /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8 text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "text-6xl mb-4", children: "💳" }),
      /* @__PURE__ */ jsx("h3", { className: "text-2xl font-bold mb-2", children: "No Credits Yet" }),
      /* @__PURE__ */ jsx("p", { className: "text-white/70 mb-6", children: "Purchase optimization credits to start improving your products" }),
      /* @__PURE__ */ jsx("div", { className: "text-sm text-white/60", children: "💡 Tip: Each credit optimizes one product for just $0.10" })
    ] })
  ] });
}
function ModernBillingDashboard({
  subscription,
  plan,
  monthlyUsage,
  recentPurchases = [],
  plans = [],
  csrfToken,
  creditBalance,
  creditHistory = []
}) {
  const fetcher = useFetcher();
  const [showPricingSelection, setShowPricingSelection] = React.useState(false);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [userInteracted, setUserInteracted] = React.useState(false);
  React.useEffect(() => {
    const handleUserInteraction = () => {
      console.log("👆 User interaction detected");
      setUserInteracted(true);
    };
    document.addEventListener("click", handleUserInteraction);
    document.addEventListener("keydown", handleUserInteraction);
    document.addEventListener("touchstart", handleUserInteraction);
    return () => {
      document.removeEventListener("click", handleUserInteraction);
      document.removeEventListener("keydown", handleUserInteraction);
      document.removeEventListener("touchstart", handleUserInteraction);
    };
  }, []);
  React.useEffect(() => {
    console.log("🔍 ModernBillingDashboard Debug:", {
      subscription,
      plan: {
        id: plan == null ? void 0 : plan.id,
        name: plan == null ? void 0 : plan.name,
        price: plan == null ? void 0 : plan.price,
        type: plan == null ? void 0 : plan.type
      },
      hasActiveSubscription: hasActiveSubscription(),
      statusDisplay: getStatusDisplay()
    });
  }, [subscription, plan]);
  React.useEffect(() => {
    if (fetcher.data && typeof fetcher.data === "object") {
      const data = fetcher.data;
      if (data.error) {
        alert(`Error: ${data.error}`);
      } else if (data.success) {
        if (data.redirectTo) {
          window.location.href = data.redirectTo;
        } else {
          window.location.reload();
        }
      }
    }
    setIsRefreshing(fetcher.state === "loading" || fetcher.state === "submitting");
  }, [fetcher.data, fetcher.state]);
  const handleCancelSubscription = () => {
    console.log("🚨 handleCancelSubscription called - this should only happen when user clicks Cancel button");
    console.log("🔍 Call stack:", new Error().stack);
    console.log("🔍 User interacted:", userInteracted);
    if (!userInteracted) {
      console.log("🛡️ BLOCKING automatic cancellation - user has not interacted with page yet");
      return;
    }
    if (!subscription || fetcher.state === "submitting") {
      console.log("❌ Cannot cancel - no subscription or already submitting");
      return;
    }
    const confirmMessage = `Are you sure you want to cancel your ${(plan == null ? void 0 : plan.name) || "subscription"}?`;
    console.log("🤔 Showing confirmation dialog:", confirmMessage);
    if (confirm(confirmMessage)) {
      console.log("✅ User confirmed cancellation");
      const formData = new FormData();
      formData.append("action", "cancel_subscription");
      formData.append("subscriptionId", subscription.id);
      if (csrfToken) formData.append("csrfToken", csrfToken);
      fetcher.submit(formData, { method: "POST" });
    } else {
      console.log("❌ User cancelled the cancellation");
    }
  };
  const getStatusDisplay = () => {
    if (!subscription) return { text: "No Active Plan", color: "text-gray-400" };
    switch (subscription.status) {
      case "ACTIVE":
        if (hasActiveSubscription()) {
          return { text: "Active Subscription", color: "text-green-400" };
        } else {
          return { text: "Active", color: "text-green-400" };
        }
      case "PENDING":
        return { text: "Pending", color: "text-yellow-400" };
      case "CANCELLED":
        return { text: "Cancelled", color: "text-red-400" };
      case "EXPIRED":
        return { text: "Expired", color: "text-red-400" };
      default:
        return { text: subscription.status, color: "text-gray-400" };
    }
  };
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", { style: "currency", currency: "USD" }).format(amount);
  };
  const getPlanDisplayName = () => {
    var _a2, _b;
    if (subscription && subscription.status === "ACTIVE") {
      if (plan == null ? void 0 : plan.name) {
        return plan.name;
      }
      const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
      if ((_b = lineItem == null ? void 0 : lineItem.plan) == null ? void 0 : _b.pricingDetails) {
        const interval = lineItem.plan.pricingDetails.interval;
        if (interval === "ANNUAL") return "Annual Plan";
        if (interval === "EVERY_30_DAYS") return "Monthly Plan";
      }
      return "Active Subscription";
    }
    return (plan == null ? void 0 : plan.name) || "No Plan";
  };
  const getPlanDisplayPrice = () => {
    var _a2, _b, _c, _d;
    if (subscription && subscription.status === "ACTIVE") {
      if (plan == null ? void 0 : plan.price) {
        return formatCurrency(plan.price);
      }
      const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
      if ((_d = (_c = (_b = lineItem == null ? void 0 : lineItem.plan) == null ? void 0 : _b.pricingDetails) == null ? void 0 : _c.price) == null ? void 0 : _d.amount) {
        const amount = parseFloat(lineItem.plan.pricingDetails.price.amount);
        return formatCurrency(amount);
      }
      return "Active";
    }
    return (plan == null ? void 0 : plan.price) ? formatCurrency(plan.price) : "$0";
  };
  const getPlanDisplayInterval = () => {
    var _a2, _b;
    if (subscription && subscription.status === "ACTIVE") {
      if (plan == null ? void 0 : plan.type) {
        return plan.type === "pay_per_use" ? "Per Use" : plan.type === "annual" ? "Annual" : "Monthly";
      }
      const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
      if ((_b = lineItem == null ? void 0 : lineItem.plan) == null ? void 0 : _b.pricingDetails) {
        const interval = lineItem.plan.pricingDetails.interval;
        if (interval === "ANNUAL") return "Annual";
        if (interval === "EVERY_30_DAYS") return "Monthly";
      }
      return "Subscription";
    }
    return (plan == null ? void 0 : plan.type) === "pay_per_use" ? "Per Use" : "Monthly";
  };
  const hasActiveSubscription = () => {
    var _a2, _b;
    if (!subscription || subscription.status !== "ACTIVE") {
      return false;
    }
    if (plan) {
      return plan.type !== "pay_per_use";
    }
    const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
    if ((_b = lineItem == null ? void 0 : lineItem.plan) == null ? void 0 : _b.pricingDetails) {
      const interval = lineItem.plan.pricingDetails.interval;
      return interval === "ANNUAL" || interval === "EVERY_30_DAYS";
    }
    return false;
  };
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
  };
  if (showPricingSelection) {
    return /* @__PURE__ */ jsxs("div", { className: "max-w-6xl mx-auto", children: [
      /* @__PURE__ */ jsx("div", { className: "mb-8", children: /* @__PURE__ */ jsx(
        Button,
        {
          onClick: () => setShowPricingSelection(false),
          className: "mb-4 bg-white/10 border border-white/20 text-white hover:bg-white/20",
          children: "← Back to Billing Dashboard"
        }
      ) }),
      /* @__PURE__ */ jsx(
        PricingSelection,
        {
          plans,
          selectedPlan: plan == null ? void 0 : plan.id,
          csrfToken,
          hasActiveSubscription: hasActiveSubscription()
        }
      )
    ] });
  }
  const statusDisplay = getStatusDisplay();
  console.log("🔍 ModernBillingDashboard Debug:", {
    subscription: subscription ? {
      id: subscription.id,
      status: subscription.status,
      lineItems: subscription.lineItems
    } : null,
    plan: plan ? {
      id: plan.id,
      name: plan.name,
      price: plan.price,
      type: plan.type
    } : null,
    hasActiveSubscription: hasActiveSubscription(),
    statusDisplay
  });
  return /* @__PURE__ */ jsxs("div", { className: "max-w-6xl mx-auto space-y-8", children: [
    /* @__PURE__ */ jsxs("div", { className: "bg-yellow-500/20 border border-yellow-500/40 rounded-2xl p-4 text-center", children: [
      /* @__PURE__ */ jsx("div", { className: "text-yellow-300 font-bold text-lg", children: "🧪 TEST MODE ENABLED" }),
      /* @__PURE__ */ jsx("div", { className: "text-yellow-200 text-sm mt-1", children: "All payments are in test mode - no real charges will be made" })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col md:flex-row md:items-center justify-between mb-8", children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h2", { className: "text-3xl font-bold mb-2", children: "Current Plan" }),
          /* @__PURE__ */ jsx("p", { className: "text-white/70", children: "Manage your subscription and billing preferences" })
        ] }),
        /* @__PURE__ */ jsx("div", { className: `text-lg font-semibold ${statusDisplay.color}`, children: statusDisplay.text })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-8", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2", children: getPlanDisplayName() }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Current Plan" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2", children: getPlanDisplayPrice() }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: getPlanDisplayInterval() })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-4xl font-bold mb-2", children: (monthlyUsage == null ? void 0 : monthlyUsage.productsOptimized) || 0 }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Products Optimized" })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row gap-4 mt-8", children: [
        /* @__PURE__ */ jsx(
          Button,
          {
            onClick: () => setShowPricingSelection(true),
            className: "bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl",
            disabled: isRefreshing,
            children: plan ? "Change Plan" : "Choose Plan"
          }
        ),
        (plan == null ? void 0 : plan.id) === "pay_per_use" && /* @__PURE__ */ jsx(
          Button,
          {
            onClick: () => {
              setIsRefreshing(true);
              if (typeof window !== "undefined") {
                window.location.href = window.location.pathname + "?refresh=true";
              }
            },
            className: "bg-transparent border-2 border-blue-400/60 text-blue-400 hover:bg-blue-400 hover:text-black font-bold py-3 px-8 rounded-2xl",
            disabled: isRefreshing,
            children: isRefreshing ? "Refreshing..." : "Refresh Subscription"
          }
        ),
        subscription && subscription.status === "ACTIVE" && /* @__PURE__ */ jsx(
          Button,
          {
            onClick: handleCancelSubscription,
            className: "bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl",
            disabled: isRefreshing,
            children: "Cancel Subscription"
          }
        )
      ] })
    ] }),
    monthlyUsage && /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-6", children: "Usage This Month" }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-8", children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { className: "text-3xl font-bold mb-2", children: formatCurrency(monthlyUsage.totalSpent) }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Total Spent" })
        ] }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("div", { className: "text-3xl font-bold mb-2", children: monthlyUsage.lastOptimization ? formatDate(monthlyUsage.lastOptimization) : "Never" }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70", children: "Last Optimization" })
        ] })
      ] })
    ] }),
    creditBalance && /* @__PURE__ */ jsx(
      CreditsDashboard,
      {
        balance: creditBalance,
        history: creditHistory,
        hasSubscription: hasActiveSubscription()
      }
    ),
    recentPurchases.length > 0 && /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-8", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold mb-6", children: "Recent Purchases" }),
      /* @__PURE__ */ jsx("div", { className: "space-y-4", children: recentPurchases.slice(0, 5).map((purchase) => /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center py-4 border-b border-white/10 last:border-b-0", children: [
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsxs("div", { className: "font-semibold", children: [
            purchase.productCount,
            " Products Optimized"
          ] }),
          /* @__PURE__ */ jsx("div", { className: "text-white/70 text-sm", children: formatDate(purchase.date) })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "text-right", children: [
          /* @__PURE__ */ jsx("div", { className: "font-bold", children: formatCurrency(purchase.amount) }),
          /* @__PURE__ */ jsx("div", { className: `text-sm ${purchase.status === "ACTIVE" ? "text-green-400" : "text-gray-400"}`, children: purchase.status })
        ] })
      ] }, purchase.id)) })
    ] })
  ] });
}
const loader$4 = withBulletproofAuth(async ({ request, auth }) => {
  var _a2, _b, _c, _d, _e, _f, _g, _h, _i, _j;
  const { admin, session } = auth;
  try {
    console.log("Billing loader - Request URL:", request.url);
    console.log("✅ Billing loader - Authenticated for shop:", session.shop);
    const url = new URL(request.url);
    const success = url.searchParams.get("success");
    const error = url.searchParams.get("error");
    const cancelled = url.searchParams.get("cancelled");
    const forceRefresh = url.searchParams.get("refresh");
    if (success || cancelled || forceRefresh) {
      console.log(`🔄 Billing callback detected (${success || cancelled || "force refresh"}), invalidating cache`);
      const { invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
      invalidateBillingCache2(session.shop);
      invalidateBillingCache2(session.shop, "subscription");
      invalidateBillingCache2(session.shop, "purchase");
    }
    const billingService = new BillingService(admin, session.shop);
    let billingStatus = await billingService.hasActiveBilling();
    if (!billingStatus.hasAccess || ((_a2 = billingStatus.plan) == null ? void 0 : _a2.id) === "pay_per_use") {
      console.log(`🔍 No active billing detected, checking for recent subscription activity...`);
      const recentSubscription = await db.billingSubscription.findFirst({
        where: {
          shop: session.shop,
          status: { in: ["ACTIVE", "PENDING"] },
          createdAt: { gte: new Date(Date.now() - 24 * 60 * 60 * 1e3) }
          // Last 24 hours
        },
        orderBy: { createdAt: "desc" }
      });
      if (recentSubscription) {
        console.log(`🔄 Found recent subscription in database, forcing fresh sync from Shopify...`);
        const { invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
        invalidateBillingCache2(session.shop);
        const subscriptionData = await billingService.getCurrentSubscription();
        const allSubscriptions = ((_c = (_b = subscriptionData.data) == null ? void 0 : _b.currentAppInstallation) == null ? void 0 : _c.activeSubscriptions) || [];
        console.log(`📊 Fresh Shopify data: ${allSubscriptions.length} subscriptions found`);
        if (allSubscriptions.length > 0) {
          const subscription = allSubscriptions[0];
          console.log(`📋 Syncing subscription: ${subscription.id}, status: ${subscription.status}`);
          const determinePlanId2 = (sub) => {
            if (!sub.name) return "monthly";
            const name = sub.name.toLowerCase();
            if (name.includes("annual") || name.includes("yearly")) return "annual";
            return "monthly";
          };
          await db.$transaction(async (tx) => {
            await tx.billingSubscription.upsert({
              where: { subscriptionId: subscription.id },
              update: {
                status: subscription.status,
                planId: determinePlanId2(subscription),
                trialDays: subscription.trialDays || 0,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                updatedAt: /* @__PURE__ */ new Date()
              },
              create: {
                shop: session.shop,
                subscriptionId: subscription.id,
                status: subscription.status,
                planId: determinePlanId2(subscription),
                trialDays: subscription.trialDays || 0,
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                priceAmount: determinePlanId2(subscription) === "annual" ? 299.99 : 29.99,
                priceCurrency: "USD"
              }
            });
            await tx.session.updateMany({
              where: { shop: session.shop },
              data: {
                subscriptionId: subscription.id,
                subscriptionStatus: subscription.status,
                billingPlanId: determinePlanId2(subscription),
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
                lastBillingCheck: /* @__PURE__ */ new Date()
              }
            });
          });
          console.log(`✅ Auto-sync completed, refreshing billing status...`);
          invalidateBillingCache2(session.shop);
          billingStatus = await billingService.hasActiveBilling();
          console.log(`🎯 Updated billing status: hasAccess=${billingStatus.hasAccess}, plan=${(_d = billingStatus.plan) == null ? void 0 : _d.name}`);
        }
      }
    }
    const oneTimePurchases = await billingService.getOneTimePurchases();
    const plans = billingService.getAllBillingPlans();
    const creditsService = new CreditsService(session.shop);
    const creditBalance = await creditsService.getCreditBalance();
    const creditHistory = await creditsService.getCreditHistory();
    const monthlyUsage = {
      productsOptimized: 45,
      totalSpent: ((_e = billingStatus.plan) == null ? void 0 : _e.type) === "pay_per_use" ? 4.5 : ((_f = billingStatus.plan) == null ? void 0 : _f.price) || 0,
      lastOptimization: (/* @__PURE__ */ new Date()).toISOString()
    };
    const recentPurchases = ((_j = (_i = (_h = (_g = oneTimePurchases.data) == null ? void 0 : _g.currentAppInstallation) == null ? void 0 : _h.oneTimePurchases) == null ? void 0 : _i.edges) == null ? void 0 : _j.map((edge) => ({
      id: edge.node.id,
      amount: parseFloat(edge.node.price.amount),
      productCount: Math.round(parseFloat(edge.node.price.amount) / 0.1),
      // Calculate from amount
      date: edge.node.createdAt,
      status: edge.node.status
    }))) || [];
    const data = {
      subscription: billingStatus.subscription,
      plan: billingStatus.plan,
      hasAccess: billingStatus.hasAccess,
      monthlyUsage,
      recentPurchases,
      plans,
      creditBalance,
      creditHistory,
      success,
      error,
      shop: session.shop
    };
    return addCSRFToken(session.shop, data);
  } catch (error) {
    console.error("❌ Billing loader error:", error);
    await logError(
      error instanceof Error ? error : new Error(String(error)),
      { shop: (session == null ? void 0 : session.shop) || "unknown", action: "billing_loader" }
    );
    return {
      subscription: void 0,
      plan: void 0,
      hasAccess: false,
      monthlyUsage: {
        productsOptimized: 0,
        totalSpent: 0,
        lastOptimization: (/* @__PURE__ */ new Date()).toISOString()
      },
      recentPurchases: [],
      plans: [],
      shop: (session == null ? void 0 : session.shop) || "",
      error: error instanceof Error ? error.message : "Failed to load billing information",
      errorCode: "BILLING_DATA_LOAD_FAILED"
    };
  }
});
const action$2 = withBulletproofAction(async (args) => {
  const { handleBillingAction } = await import("./billing-actions.server-CD3y3y5F.js");
  return handleBillingAction(args);
});
function BillingPage() {
  const {
    subscription,
    plan,
    monthlyUsage,
    recentPurchases,
    plans,
    creditBalance,
    creditHistory,
    success,
    error,
    csrfToken
  } = useLoaderData();
  React.useEffect(() => {
    console.log("🏦 BillingPage mounted with success parameter:", success);
    console.log("🔍 URL:", window.location.href);
    console.log("🔍 Subscription:", subscription);
    console.log("🔍 Plan:", plan);
    const listeners = window.getEventListeners ? window.getEventListeners(document) : "getEventListeners not available";
    console.log("🔍 Document event listeners:", listeners);
  }, [success, subscription, plan]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "AI BULK SEO Billing" }),
    success && /* @__PURE__ */ jsx("div", { className: "mx-6 mb-6", children: /* @__PURE__ */ jsxs("div", { className: "bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm shadow-2xl", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3 mb-3", children: [
        /* @__PURE__ */ jsx("div", { className: "w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsx("svg", { className: "w-5 h-5 text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 13l4 4L19 7" }) }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h3", { className: "text-white font-bold text-lg", children: success === "subscription" ? "Payment Successful!" : "Payment Successful!" }),
          /* @__PURE__ */ jsx("p", { className: "text-white/70 text-sm", children: "Your payment has been processed and credits have been added to your account." })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 text-xs text-white/70", children: [
        /* @__PURE__ */ jsx("div", { className: "w-1.5 h-1.5 bg-white rounded-full animate-pulse" }),
        /* @__PURE__ */ jsx("span", { children: "Ready to start optimizing your products" })
      ] })
    ] }) }),
    error && /* @__PURE__ */ jsx("div", { className: "mx-6 mb-6", children: /* @__PURE__ */ jsxs("div", { className: "bg-black border border-white/20 rounded-3xl p-6 backdrop-blur-sm", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-3 mb-3", children: [
        /* @__PURE__ */ jsx("div", { className: "w-10 h-10 bg-black border border-white/20 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsx("svg", { className: "w-5 h-5 text-white", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M6 18L18 6M6 6l12 12" }) }) }),
        /* @__PURE__ */ jsxs("div", { children: [
          /* @__PURE__ */ jsx("h3", { className: "text-white font-bold text-lg", children: "Payment Error" }),
          /* @__PURE__ */ jsx("p", { className: "text-white/70 text-sm", children: "There was an issue processing your payment. Please try again or contact support." })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2 text-xs text-white/70", children: [
        /* @__PURE__ */ jsx("div", { className: "w-1.5 h-1.5 bg-white rounded-full" }),
        /* @__PURE__ */ jsx("span", { children: "Please check your payment details and try again" })
      ] })
    ] }) }),
    /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-black text-white", children: [
      /* @__PURE__ */ jsx("div", { className: "bg-black py-20 px-6", children: /* @__PURE__ */ jsx("div", { className: "max-w-6xl mx-auto text-center", children: /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center mb-12", children: [
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/logo.png",
            alt: "AI BULK SEO Logo",
            className: "w-16 h-16 mb-6 rounded-2xl shadow-2xl",
            style: { filter: "brightness(1.1) contrast(1.1)" }
          }
        ),
        /* @__PURE__ */ jsx("h1", { style: {
          fontSize: "clamp(3rem, 8vw, 6rem)",
          fontWeight: 900,
          lineHeight: 0.9,
          letterSpacing: "-0.05em",
          marginBottom: "1rem",
          color: "white"
        }, children: "BILLING" }),
        /* @__PURE__ */ jsx("p", { style: {
          fontSize: "clamp(1.25rem, 3vw, 1.75rem)",
          fontWeight: 300,
          color: "#cbd5e1",
          maxWidth: "40rem",
          margin: "0 auto"
        }, children: "Manage your AI BULK SEO subscription and usage" })
      ] }) }) }),
      /* @__PURE__ */ jsx("div", { className: "px-6 pb-20", children: /* @__PURE__ */ jsx(
        ModernBillingDashboard,
        {
          subscription,
          plan,
          monthlyUsage,
          recentPurchases,
          plans,
          creditBalance,
          creditHistory,
          csrfToken
        }
      ) })
    ] })
  ] });
}
const route18 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$2,
  default: BillingPage,
  loader: loader$4
}, Symbol.toStringTag, { value: "Module" }));
const action$1 = async (args) => {
  const { handleBillingAction } = await import("./billing-actions.server-CD3y3y5F.js");
  return handleBillingAction(args);
};
const route19 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action: action$1
}, Symbol.toStringTag, { value: "Module" }));
const loader$3 = async ({ request }) => {
  var _a2, _b, _c, _d, _e, _f;
  try {
    const url = new URL(request.url);
    const charge_id = url.searchParams.get("charge_id");
    const purchase_id = url.searchParams.get("purchase_id");
    console.log(`🔄 Billing callback received - charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    console.log(`🔄 Callback URL: ${request.url}`);
    let admin, session;
    try {
      const authResult = await authenticate.admin(request);
      admin = authResult.admin;
      session = authResult.session;
      console.log(`✅ Authentication successful for shop: ${session.shop}`);
    } catch (authError) {
      console.error("❌ Authentication failed in billing callback:", authError);
      const shopParam = url.searchParams.get("shop");
      if (shopParam) {
        console.log(`🔄 Attempting to recover billing callback for shop: ${shopParam}`);
        const callbackData = {
          charge_id,
          purchase_id,
          timestamp: Date.now(),
          url: request.url,
          shop: shopParam
        };
        const recoveryUrl = `/app/billing/recovery?shop=${shopParam}&data=${encodeURIComponent(JSON.stringify(callbackData))}`;
        console.log(`🔄 Redirecting to recovery URL: ${recoveryUrl}`);
        throw redirect(recoveryUrl);
      } else {
        console.error("❌ No shop parameter found in billing callback");
        console.log(`🔍 Callback URL: ${request.url}`);
        console.log(`🔍 Available URL params:`, Object.fromEntries(url.searchParams.entries()));
        throw redirect("/app/billing?error=callback_failed");
      }
    }
    if (!(session == null ? void 0 : session.shop)) {
      console.error("❌ No session or shop found in billing callback");
      throw redirect("/app/billing?error=no_session");
    }
    const dbSession = await db.session.findUnique({
      where: { shop: session.shop }
    });
    if (!dbSession) {
      console.error(`❌ Session not found in database for shop: ${session.shop}`);
      await db.session.upsert({
        where: { shop: session.shop },
        update: {
          accessToken: session.accessToken || "",
          scope: session.scope || "",
          expires: session.expires ? new Date(session.expires) : null,
          isOnline: session.isOnline || false,
          state: session.state || "",
          updatedAt: /* @__PURE__ */ new Date()
        },
        create: {
          id: session.id || `offline_${session.shop}`,
          shop: session.shop,
          state: session.state || "",
          isOnline: session.isOnline || false,
          scope: session.scope || "",
          expires: session.expires ? new Date(session.expires) : null,
          accessToken: session.accessToken || "",
          userId: session.userId || null,
          firstName: "",
          lastName: "",
          email: "",
          accountOwner: false,
          locale: "",
          collaborator: false,
          emailVerified: false
        }
      });
      console.log(`✅ Session created/updated for shop: ${session.shop}`);
    }
    console.log(`🔄 Processing billing callback for shop: ${session.shop}, charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    const billingService = new BillingService(admin, session.shop);
    if (charge_id) {
      console.log(`Subscription confirmed for shop ${session.shop}, charge_id: ${charge_id}`);
      const subscriptionData = await billingService.getCurrentSubscription();
      const activeSubscriptions = ((_b = (_a2 = subscriptionData.data) == null ? void 0 : _a2.currentAppInstallation) == null ? void 0 : _b.activeSubscriptions) || [];
      if (activeSubscriptions.length > 0) {
        const subscription = activeSubscriptions[0];
        await db.$transaction(async (tx) => {
          try {
            await tx.billingSubscription.upsert({
              where: { subscriptionId: subscription.id },
              update: {
                status: subscription.status,
                planId: determinePlanId$1(subscription),
                trialDays: subscription.trialDays || 0,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                updatedAt: /* @__PURE__ */ new Date()
              },
              create: {
                shop: session.shop,
                subscriptionId: subscription.id,
                status: subscription.status,
                planId: determinePlanId$1(subscription),
                trialDays: subscription.trialDays || 0,
                trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                priceAmount: getSubscriptionPrice$1(subscription),
                priceCurrency: "USD"
              }
            });
            const sessionExists = await tx.session.findUnique({
              where: { shop: session.shop }
            });
            if (sessionExists) {
              await tx.billingEvent.create({
                data: {
                  shop: session.shop,
                  eventType: "subscription_confirmed",
                  referenceId: subscription.id,
                  eventData: JSON.stringify({
                    chargeId: charge_id,
                    subscriptionStatus: subscription.status,
                    trialDays: subscription.trialDays,
                    timestamp: (/* @__PURE__ */ new Date()).toISOString()
                  })
                }
              });
              await tx.session.update({
                where: { shop: session.shop },
                data: {
                  subscriptionId: subscription.id,
                  subscriptionStatus: subscription.status,
                  billingPlanId: determinePlanId$1(subscription),
                  trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
                  lastBillingCheck: /* @__PURE__ */ new Date()
                }
              });
            } else {
              console.warn(`⚠️ Session not found for shop ${session.shop}, skipping billing event and session update`);
            }
            console.log(`✅ Subscription confirmed and stored for shop ${session.shop}`);
          } catch (dbError) {
            console.error(`❌ Database error in billing callback for shop ${session.shop}:`, dbError);
            throw dbError;
          }
        });
        invalidateBillingCache(session.shop);
        console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);
        const { setSubscriptionUpdateFlag: setSubscriptionUpdateFlag2 } = await Promise.resolve().then(() => cache_server);
        setSubscriptionUpdateFlag2(session.shop);
        console.log(`🚩 Subscription update flag set for shop ${session.shop}`);
        return redirect("/app/billing?success=subscription");
      }
    }
    if (purchase_id) {
      console.log(`One-time purchase confirmed for shop ${session.shop}, purchase_id: ${purchase_id}`);
      const purchaseData = await billingService.getOneTimePurchases();
      const purchases = ((_e = (_d = (_c = purchaseData.data) == null ? void 0 : _c.currentAppInstallation) == null ? void 0 : _d.oneTimePurchases) == null ? void 0 : _e.edges) || [];
      const purchase = (_f = purchases.find((edge) => edge.node.id === purchase_id)) == null ? void 0 : _f.node;
      if (purchase) {
        try {
          await db.billingPurchase.upsert({
            where: { purchaseId: purchase.id },
            update: {
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round(parseFloat(purchase.price.amount) / 0.1),
              description: purchase.name,
              updatedAt: /* @__PURE__ */ new Date()
            },
            create: {
              shop: session.shop,
              purchaseId: purchase.id,
              status: purchase.status,
              amount: parseFloat(purchase.price.amount),
              productCount: Math.round(parseFloat(purchase.price.amount) / 0.1),
              currency: purchase.price.currencyCode || "USD",
              description: purchase.name
            }
          });
          await db.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: "purchase_confirmed",
              referenceId: purchase.id,
              eventData: JSON.stringify(purchase)
            }
          });
          console.log(`✅ Purchase confirmed and stored for shop ${session.shop}`);
          const creditsService = new CreditsService(session.shop);
          await creditsService.addCredits(
            purchase.productCount || 1,
            purchase.id,
            `Purchase of ${purchase.productCount || 1} optimization credits`
          );
          console.log(`💳 Added ${purchase.productCount || 1} credits for shop ${session.shop}`);
          invalidateBillingCache(session.shop);
          console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);
        } catch (dbError) {
          console.error("Failed to store purchase confirmation in database:", dbError);
        }
        return redirect("/app/billing?success=purchase");
      }
    }
    console.error(`Billing callback failed for shop ${session.shop}. charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    return redirect("/app/billing?error=callback_failed");
  } catch (error) {
    console.error("❌ Billing callback error:", error);
    if (error instanceof Response && error.status >= 300 && error.status < 400) {
      throw error;
    }
    const url = new URL(request.url);
    const shopParam = url.searchParams.get("shop");
    const charge_id = url.searchParams.get("charge_id");
    const purchase_id = url.searchParams.get("purchase_id");
    console.error(`❌ Billing callback failed for shop: ${shopParam}, charge_id: ${charge_id}, purchase_id: ${purchase_id}`, error);
    if (shopParam) {
      return redirect(`/app/billing?error=callback_failed&shop=${shopParam}`);
    }
    return redirect("/app/billing?error=system_error");
  }
};
function determinePlanId$1(subscription) {
  var _a2, _b;
  const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
  if (!lineItem) return "unknown";
  const pricingDetails = (_b = lineItem.plan) == null ? void 0 : _b.pricingDetails;
  if (!pricingDetails) return "unknown";
  if (pricingDetails.interval === "ANNUAL") {
    return "annual";
  } else if (pricingDetails.interval === "EVERY_30_DAYS") {
    return "monthly";
  }
  return "unknown";
}
function getSubscriptionPrice$1(subscription) {
  var _a2, _b;
  const lineItem = (_a2 = subscription.lineItems) == null ? void 0 : _a2[0];
  if (!lineItem) return 0;
  const pricingDetails = (_b = lineItem.plan) == null ? void 0 : _b.pricingDetails;
  if (!pricingDetails) return 0;
  if (pricingDetails.interval === "ANNUAL") {
    return 199.99;
  } else if (pricingDetails.interval === "EVERY_30_DAYS") {
    return 19.99;
  }
  return 0;
}
const route20 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$3
}, Symbol.toStringTag, { value: "Module" }));
const loader$2 = async ({ request }) => {
  var _a2, _b;
  try {
    const url = new URL(request.url);
    const shopParam = url.searchParams.get("shop");
    const dataParam = url.searchParams.get("data");
    console.log(`🔄 Billing recovery initiated for shop: ${shopParam}`);
    if (!shopParam || !dataParam) {
      console.error("❌ Missing shop or data parameters in billing recovery");
      return redirect("/app/billing?error=invalid_recovery");
    }
    const { admin, session } = await authenticate.admin(request);
    if (!(session == null ? void 0 : session.shop) || session.shop !== shopParam) {
      console.error(`❌ Shop mismatch in billing recovery. Expected: ${shopParam}, Got: ${session == null ? void 0 : session.shop}`);
      return redirect("/app/billing?error=shop_mismatch");
    }
    let callbackData;
    try {
      callbackData = JSON.parse(decodeURIComponent(dataParam));
    } catch (parseError) {
      console.error("❌ Failed to parse callback data:", parseError);
      return redirect("/app/billing?error=invalid_data");
    }
    const { charge_id, purchase_id } = callbackData;
    console.log(`🔄 Processing recovered billing callback - charge_id: ${charge_id}, purchase_id: ${purchase_id}`);
    const billingService = new BillingService(admin, session.shop);
    if (charge_id) {
      console.log(`✅ Processing subscription confirmation for shop ${session.shop}, charge_id: ${charge_id}`);
      const subscriptionData = await billingService.getCurrentSubscription();
      const allSubscriptions = ((_b = (_a2 = subscriptionData.data) == null ? void 0 : _a2.currentAppInstallation) == null ? void 0 : _b.activeSubscriptions) || [];
      const subscription = allSubscriptions.find(
        (sub) => sub.id.includes(charge_id) || sub.id === charge_id
      ) || allSubscriptions[0];
      if (subscription) {
        console.log(`📋 Found subscription: ${subscription.id}, status: ${subscription.status}`);
        await db.$transaction(async (tx) => {
          await tx.billingSubscription.upsert({
            where: { subscriptionId: subscription.id },
            update: {
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              updatedAt: /* @__PURE__ */ new Date()
            },
            create: {
              shop: session.shop,
              subscriptionId: subscription.id,
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              priceAmount: getSubscriptionPrice(subscription),
              priceCurrency: "USD"
            }
          });
          await tx.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: "subscription_confirmed_recovery",
              referenceId: subscription.id,
              eventData: JSON.stringify({
                chargeId: charge_id,
                subscriptionStatus: subscription.status,
                trialDays: subscription.trialDays,
                recoveryTimestamp: (/* @__PURE__ */ new Date()).toISOString(),
                originalCallback: callbackData
              })
            }
          });
          await tx.session.update({
            where: { shop: session.shop },
            data: {
              subscriptionId: subscription.id,
              subscriptionStatus: subscription.status,
              billingPlanId: determinePlanId(subscription),
              trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1e3) : null,
              lastBillingCheck: /* @__PURE__ */ new Date()
            }
          });
          console.log(`✅ Subscription recovery completed for shop ${session.shop}`);
        });
        invalidateBillingCache(session.shop);
        console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);
        const { setSubscriptionUpdateFlag: setSubscriptionUpdateFlag2 } = await Promise.resolve().then(() => cache_server);
        setSubscriptionUpdateFlag2(session.shop);
        console.log(`🚩 Subscription update flag set for shop ${session.shop}`);
        return redirect("/app/billing?success=subscription_recovered");
      } else {
        console.error(`❌ No subscription found for charge_id: ${charge_id}`);
        return redirect("/app/billing?error=subscription_not_found");
      }
    }
    if (purchase_id) {
      console.log(`✅ Processing purchase confirmation recovery for shop ${session.shop}, purchase_id: ${purchase_id}`);
      return redirect("/app/billing?success=purchase_recovered");
    }
    console.error(`❌ No valid callback data found in recovery`);
    return redirect("/app/billing?error=no_callback_data");
  } catch (error) {
    console.error("❌ Billing recovery error:", error);
    return redirect("/app/billing?error=recovery_failed");
  }
};
function determinePlanId(subscription) {
  if (!subscription.name) return "unknown";
  const name = subscription.name.toLowerCase();
  if (name.includes("monthly")) return "monthly";
  if (name.includes("annual") || name.includes("yearly")) return "annual";
  return "monthly";
}
function getSubscriptionPrice(subscription) {
  var _a2;
  if (subscription.lineItems && subscription.lineItems.length > 0) {
    const lineItem = subscription.lineItems[0];
    if (lineItem.plan && lineItem.plan.pricingDetails) {
      return parseFloat(((_a2 = lineItem.plan.pricingDetails.price) == null ? void 0 : _a2.amount) || "0");
    }
  }
  const planId = determinePlanId(subscription);
  switch (planId) {
    case "monthly":
      return 29.99;
    case "annual":
      return 299.99;
    default:
      return 0;
  }
}
const route21 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  loader: loader$2
}, Symbol.toStringTag, { value: "Module" }));
function PageContent({ children, className }) {
  return /* @__PURE__ */ jsx("div", { className: cn(
    "bg-black min-h-screen py-16 px-6",
    className
  ), children: /* @__PURE__ */ jsx("div", { className: "max-w-7xl mx-auto", children: /* @__PURE__ */ jsx(
    motion.div,
    {
      initial: { opacity: 0, y: 30 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.8, ease: "easeOut" },
      className: "space-y-12",
      children
    }
  ) }) });
}
const loader$1 = withBulletproofAuth(async ({ auth }) => {
  const { admin, session } = auth;
  const { getSubscriptionUpdateFlag: getSubscriptionUpdateFlag2, clearSubscriptionUpdateFlag: clearSubscriptionUpdateFlag2, invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
  const subscriptionUpdateFlag = getSubscriptionUpdateFlag2(session.shop);
  if (subscriptionUpdateFlag) {
    console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh on pricing page`);
    invalidateBillingCache2(session.shop);
    clearSubscriptionUpdateFlag2(session.shop);
  }
  const billingService = new BillingService(admin, session.shop);
  const billingStatus = await billingService.hasActiveBilling();
  const plans = billingService.getAllBillingPlans();
  const data = {
    plans,
    currentPlan: billingStatus.plan,
    hasAccess: billingStatus.hasAccess,
    shop: session.shop
  };
  return json(addCSRFToken(session.shop, data));
});
const action = withBulletproofAction(async (args) => {
  const { handleBillingAction } = await import("./billing-actions.server-CD3y3y5F.js");
  return handleBillingAction(args);
});
function BillingPricingPage() {
  const { plans, currentPlan, csrfToken } = useLoaderData();
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "Choose Your Plan" }),
    /* @__PURE__ */ jsx(PageContent, { children: /* @__PURE__ */ jsx(
      PricingSelection,
      {
        plans,
        selectedPlan: currentPlan == null ? void 0 : currentPlan.id,
        showPayPerUse: true,
        csrfToken
      }
    ) })
  ] });
}
const route22 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  action,
  default: BillingPricingPage,
  loader: loader$1
}, Symbol.toStringTag, { value: "Module" }));
function Review() {
  const navigate = useNavigate();
  useEffect(() => {
    navigate("/app/seo-dashboard");
  }, [navigate]);
  return /* @__PURE__ */ jsxs(Page, { title: "SEO Review", children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "SEO Review" }),
    /* @__PURE__ */ jsxs(BlockStack, { gap: "400", align: "center", children: [
      /* @__PURE__ */ jsx(Spinner, { size: "large" }),
      /* @__PURE__ */ jsx(Text, { as: "p", variant: "bodyMd", children: "Redirecting to SEO Dashboard..." })
    ] })
  ] });
}
const route23 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Review
}, Symbol.toStringTag, { value: "Module" }));
const OptimizeIcon = ({ size = 48 }) => /* @__PURE__ */ jsx("svg", { width: size, height: size, viewBox: "0 0 24 24", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { d: "M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" }) });
const BillingIcon = ({ size = 48 }) => /* @__PURE__ */ jsxs("svg", { width: size, height: size, viewBox: "0 0 24 24", fill: "currentColor", children: [
  /* @__PURE__ */ jsx("rect", { x: "2", y: "6", width: "20", height: "12", rx: "2" }),
  /* @__PURE__ */ jsx("path", { d: "M6 10H10V14H6V10Z", fill: "white" }),
  /* @__PURE__ */ jsx("line", { x1: "14", y1: "11", x2: "18", y2: "11", stroke: "white", strokeWidth: "2" }),
  /* @__PURE__ */ jsx("line", { x1: "14", y1: "13", x2: "16", y2: "13", stroke: "white", strokeWidth: "2" })
] });
const SettingsIcon = ({ size = 48 }) => /* @__PURE__ */ jsxs("svg", { width: size, height: size, viewBox: "0 0 24 24", fill: "currentColor", children: [
  /* @__PURE__ */ jsx("path", { d: "M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" }),
  /* @__PURE__ */ jsx("path", { d: "M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" })
] });
const loader = withBulletproofAuth(async ({ auth }) => {
  const { admin, session } = auth;
  const { getSubscriptionUpdateFlag: getSubscriptionUpdateFlag2, clearSubscriptionUpdateFlag: clearSubscriptionUpdateFlag2, invalidateBillingCache: invalidateBillingCache2 } = await Promise.resolve().then(() => cache_server);
  const subscriptionUpdateFlag = getSubscriptionUpdateFlag2(session.shop);
  if (subscriptionUpdateFlag) {
    console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh on main dashboard`);
    invalidateBillingCache2(session.shop);
    clearSubscriptionUpdateFlag2(session.shop);
  }
  const billingService = new BillingService(admin, session.shop);
  const billingStatus = await billingService.hasActiveBilling();
  return json({
    products: [],
    totalProducts: 0,
    isInitialLoad: true,
    billing: {
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan,
      subscription: billingStatus.subscription
    }
  });
});
function Dashboard() {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const retryConnection = () => {
    setError(null);
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 2e3);
  };
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(TitleBar, { title: "SEO Dashboard" }),
    /* @__PURE__ */ jsxs("div", { className: "min-h-screen bg-black text-white flex items-center justify-center px-6 py-20 relative", children: [
      /* @__PURE__ */ jsxs("div", { className: "absolute inset-0 opacity-5", children: [
        /* @__PURE__ */ jsx("div", { className: "absolute top-1/4 left-1/4 w-96 h-96 bg-white rounded-full blur-3xl" }),
        /* @__PURE__ */ jsx("div", { className: "absolute bottom-1/4 right-1/4 w-80 h-80 bg-white rounded-full blur-2xl" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "relative z-10 w-full max-w-7xl mx-auto", children: [
        /* @__PURE__ */ jsxs("div", { className: "text-center mb-20", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center mb-12", children: [
            /* @__PURE__ */ jsx(
              "img",
              {
                src: "/logo.png",
                alt: "AI BULK SEO Logo",
                className: "w-16 h-16 mb-6 rounded-2xl shadow-2xl",
                style: { filter: "brightness(1.1) contrast(1.1)" }
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center bg-white/10 rounded-full px-8 py-4 border border-white/20", children: [
              /* @__PURE__ */ jsx("div", { className: "w-3 h-3 bg-white rounded-full mr-4" }),
              /* @__PURE__ */ jsx("span", { className: "text-sm font-semibold tracking-widest uppercase", children: "SEO Dashboard" })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("h1", { style: {
            fontSize: "clamp(4rem, 12vw, 12rem)",
            fontWeight: 900,
            lineHeight: 0.85,
            letterSpacing: "-0.08em",
            marginBottom: "2rem",
            color: "white"
          }, children: [
            "PRODRANK",
            /* @__PURE__ */ jsx("br", {}),
            /* @__PURE__ */ jsx("span", { style: { color: "#64748b" }, children: "X" })
          ] }),
          /* @__PURE__ */ jsx("p", { style: {
            fontSize: "clamp(1.5rem, 4vw, 2.5rem)",
            fontWeight: 300,
            color: "#cbd5e1",
            lineHeight: 1.4,
            maxWidth: "60rem",
            margin: "0 auto 1.5rem auto"
          }, children: "Transform your Shopify store with AI-powered SEO optimization" }),
          /* @__PURE__ */ jsx("p", { style: {
            fontSize: "clamp(1.125rem, 2.5vw, 1.5rem)",
            fontWeight: 400,
            color: "#94a3b8",
            maxWidth: "40rem",
            margin: "0 auto",
            letterSpacing: "0.05em"
          }, children: "Boost rankings • Drive traffic • Increase conversions" })
        ] }),
        /* @__PURE__ */ jsx("div", { className: "max-w-7xl mx-auto mb-8", children: /* @__PURE__ */ jsxs("div", { className: "bg-yellow-500/20 border border-yellow-500/40 rounded-2xl p-4 text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "text-yellow-300 font-bold text-lg", children: "🧪 TEST MODE ENABLED" }),
          /* @__PURE__ */ jsx("div", { className: "text-yellow-200 text-sm mt-1", children: "All payments are in test mode - no real charges will be made" })
        ] }) }),
        /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-10 max-w-7xl mx-auto", children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "group cursor-pointer transform hover:scale-105 transition-transform duration-200",
              onClick: () => navigate("/app/seo-dashboard"),
              children: /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
                /* @__PURE__ */ jsx("div", { className: "w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black", children: /* @__PURE__ */ jsx(OptimizeIcon, { size: 48 }) }),
                /* @__PURE__ */ jsx("h3", { style: {
                  fontSize: "clamp(1.75rem, 3vw, 2.25rem)",
                  fontWeight: 800,
                  marginBottom: "1.5rem",
                  color: "white",
                  letterSpacing: "-0.02em"
                }, children: "Product Optimizer" }),
                /* @__PURE__ */ jsx("p", { style: {
                  fontSize: "clamp(1.125rem, 2vw, 1.25rem)",
                  fontWeight: 300,
                  color: "#cbd5e1",
                  lineHeight: 1.8,
                  marginBottom: "2.5rem",
                  letterSpacing: "0.02em"
                }, children: "Bulk optimize titles, descriptions, and SEO meta tags with AI-powered content generation and viral keywords" }),
                /* @__PURE__ */ jsx(
                  Button$1,
                  {
                    size: "large",
                    onClick: () => navigate("/app/seo-dashboard"),
                    children: "Start Optimizing →"
                  }
                )
              ] }) })
            }
          ),
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "group cursor-pointer transform hover:scale-105 transition-transform duration-200",
              onClick: () => navigate("/app/billing"),
              children: /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
                /* @__PURE__ */ jsx("div", { className: "w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black", children: /* @__PURE__ */ jsx(BillingIcon, { size: 48 }) }),
                /* @__PURE__ */ jsx("h3", { style: {
                  fontSize: "clamp(1.75rem, 3vw, 2.25rem)",
                  fontWeight: 800,
                  marginBottom: "1.5rem",
                  color: "white",
                  letterSpacing: "-0.02em"
                }, children: "Billing & Plans" }),
                /* @__PURE__ */ jsx("p", { style: {
                  fontSize: "clamp(1.125rem, 2vw, 1.25rem)",
                  fontWeight: 300,
                  color: "#cbd5e1",
                  lineHeight: 1.8,
                  marginBottom: "2.5rem",
                  letterSpacing: "0.02em"
                }, children: "Manage subscriptions, view usage analytics, and upgrade your plan to unlock premium features" }),
                /* @__PURE__ */ jsx(
                  Button$1,
                  {
                    size: "large",
                    onClick: () => navigate("/app/billing"),
                    children: "Manage Billing →"
                  }
                )
              ] }) })
            }
          ),
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "group cursor-pointer transform hover:scale-105 transition-transform duration-200",
              onClick: () => navigate("/app/settings"),
              children: /* @__PURE__ */ jsx("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-12 hover:bg-white/15 hover:border-white/30 transition-all duration-300 h-full", children: /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
                /* @__PURE__ */ jsx("div", { className: "w-24 h-24 bg-white rounded-3xl mx-auto mb-10 flex items-center justify-center text-black", children: /* @__PURE__ */ jsx(SettingsIcon, { size: 48 }) }),
                /* @__PURE__ */ jsx("h3", { style: {
                  fontSize: "clamp(1.75rem, 3vw, 2.25rem)",
                  fontWeight: 800,
                  marginBottom: "1.5rem",
                  color: "white",
                  letterSpacing: "-0.02em"
                }, children: "Settings" }),
                /* @__PURE__ */ jsx("p", { style: {
                  fontSize: "clamp(1.125rem, 2vw, 1.25rem)",
                  fontWeight: 300,
                  color: "#cbd5e1",
                  lineHeight: 1.8,
                  marginBottom: "2.5rem",
                  letterSpacing: "0.02em"
                }, children: "Configure SEO preferences, API settings, and customize your optimization workflows" }),
                /* @__PURE__ */ jsx(
                  Button$1,
                  {
                    size: "large",
                    onClick: () => navigate("/app/settings"),
                    children: "Open Settings →"
                  }
                )
              ] }) })
            }
          )
        ] }),
        isLoading && /* @__PURE__ */ jsx("div", { className: "mt-20 text-center", children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-white/20 rounded-3xl p-12 max-w-md mx-auto", children: [
          /* @__PURE__ */ jsx(Spinner, { accessibilityLabel: "Loading dashboard...", size: "large" }),
          /* @__PURE__ */ jsx("p", { className: "text-slate-300 mt-6 text-lg", children: "Preparing your SEO workspace..." })
        ] }) }),
        error && /* @__PURE__ */ jsx("div", { className: "mt-20 max-w-lg mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "bg-white/10 border border-red-500/30 rounded-3xl p-12 text-center", children: [
          /* @__PURE__ */ jsx("div", { className: "w-16 h-16 bg-red-500/20 rounded-2xl mx-auto mb-6 flex items-center justify-center", children: /* @__PURE__ */ jsx("div", { className: "w-8 h-8 border-2 border-red-400 rounded-full border-t-transparent animate-spin" }) }),
          /* @__PURE__ */ jsx("h3", { className: "text-2xl font-bold mb-4 text-red-400", children: "Connection Error" }),
          /* @__PURE__ */ jsx("p", { className: "text-slate-300 mb-8 text-lg leading-relaxed", children: error }),
          /* @__PURE__ */ jsx(
            Button$1,
            {
              onClick: retryConnection,
              size: "large",
              children: "Retry Connection"
            }
          )
        ] }) })
      ] })
    ] })
  ] });
}
const route24 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Dashboard,
  loader
}, Symbol.toStringTag, { value: "Module" }));
const serverManifest = { "entry": { "module": "/assets/entry.client-CvHm_X_I.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/index-Ci0627_k.js", "/assets/index-DI88vBYx.js", "/assets/components-C2v4lmCU.js"], "css": [] }, "routes": { "root": { "id": "root", "parentId": void 0, "path": "", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/root-BtIjXRKj.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/index-Ci0627_k.js", "/assets/index-DI88vBYx.js", "/assets/components-C2v4lmCU.js"], "css": [] }, "routes/webhooks.app_purchases_one_time.update": { "id": "routes/webhooks.app_purchases_one_time.update", "parentId": "root", "path": "webhooks/app_purchases_one_time/update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app_purchases_one_time.update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app_subscriptions.update": { "id": "routes/webhooks.app_subscriptions.update", "parentId": "root", "path": "webhooks/app_subscriptions/update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app_subscriptions.update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app.scopes_update": { "id": "routes/webhooks.app.scopes_update", "parentId": "root", "path": "webhooks/app/scopes_update", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.scopes_update-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/webhooks.app.uninstalled": { "id": "routes/webhooks.app.uninstalled", "parentId": "root", "path": "webhooks/app/uninstalled", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/webhooks.app.uninstalled-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/billing-success": { "id": "routes/billing-success", "parentId": "root", "path": "billing-success", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/billing-success-CseoujZC.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js"], "css": [] }, "routes/auth.login": { "id": "routes/auth.login", "parentId": "root", "path": "auth/login", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-CNhm4395.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/styles-duVO-NUe.js", "/assets/components-C2v4lmCU.js", "/assets/Page-Dli0YmGf.js", "/assets/Card-u-CSbGqf.js", "/assets/Button-3T1ZHLBP.js", "/assets/use-is-after-initial-mount-MUb1fdtO.js", "/assets/context-Dt_50QHC.js", "/assets/index-Ci0627_k.js", "/assets/index-DI88vBYx.js"], "css": [] }, "routes/exitiframe": { "id": "routes/exitiframe", "parentId": "root", "path": "exitiframe", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/exitiframe-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/auth.$": { "id": "routes/auth.$", "parentId": "root", "path": "auth/*", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/auth._-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/_index": { "id": "routes/_index", "parentId": "root", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/route-lftYn0I2.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/components-C2v4lmCU.js", "/assets/index-BJYSoprK.js", "/assets/index-Ci0627_k.js", "/assets/index-DI88vBYx.js"], "css": [] }, "routes/app": { "id": "routes/app", "parentId": "root", "path": "app", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": true, "module": "/assets/app-DjdKZano.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/components-C2v4lmCU.js", "/assets/styles-duVO-NUe.js", "/assets/index-DI88vBYx.js", "/assets/proxy-CdGEizrq.js", "/assets/index-DsLkuawQ.js", "/assets/NavMenu-B_tHUzzF.js", "/assets/index-Ci0627_k.js", "/assets/use-is-after-initial-mount-MUb1fdtO.js", "/assets/context-Dt_50QHC.js"], "css": [] }, "routes/app.api.sync-subscription": { "id": "routes/app.api.sync-subscription", "parentId": "routes/app", "path": "api/sync-subscription", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.api.sync-subscription-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.api.billing-refresh": { "id": "routes/app.api.billing-refresh", "parentId": "routes/app", "path": "api/billing-refresh", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.api.billing-refresh-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.api.billing-status": { "id": "routes/app.api.billing-status", "parentId": "routes/app", "path": "api/billing-status", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.api.billing-status-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.seo-dashboard": { "id": "routes/app.seo-dashboard", "parentId": "routes/app", "path": "seo-dashboard", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.seo-dashboard-Bysk8wDl.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/proxy-CdGEizrq.js", "/assets/button-RlsfHhb_.js", "/assets/input-Bg89CcEX.js", "/assets/index-Ci0627_k.js", "/assets/badge-CSt7rrAu.js", "/assets/components-C2v4lmCU.js", "/assets/index-DI88vBYx.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/index-DsLkuawQ.js"], "css": [] }, "routes/app.api.products": { "id": "routes/app.api.products", "parentId": "routes/app", "path": "api/products", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.api.products-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.additional": { "id": "routes/app.additional", "parentId": "routes/app", "path": "additional", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.additional-C9mRbJ5o.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/Page-Dli0YmGf.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/index-BJYSoprK.js", "/assets/Button-3T1ZHLBP.js", "/assets/Card-u-CSbGqf.js", "/assets/use-is-after-initial-mount-MUb1fdtO.js", "/assets/context-Dt_50QHC.js", "/assets/index-Ci0627_k.js"], "css": [] }, "routes/app.settings": { "id": "routes/app.settings", "parentId": "routes/app", "path": "settings", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.settings-DJa9AoT_.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/button-RlsfHhb_.js", "/assets/input-Bg89CcEX.js", "/assets/components-C2v4lmCU.js", "/assets/useAppBridge-Bj34gXAL.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/index-Ci0627_k.js", "/assets/index-DI88vBYx.js"], "css": [] }, "routes/app.billing": { "id": "routes/app.billing", "parentId": "routes/app", "path": "billing", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.billing-4y1TSYm1.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/button-RlsfHhb_.js", "/assets/PricingSelection-BaVbg180.js", "/assets/components-C2v4lmCU.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/badge-CSt7rrAu.js", "/assets/index-DI88vBYx.js", "/assets/useAppBridge-Bj34gXAL.js", "/assets/proxy-CdGEizrq.js", "/assets/index-Ci0627_k.js"], "css": [] }, "routes/app.billing.pay-per-use": { "id": "routes/app.billing.pay-per-use", "parentId": "routes/app.billing", "path": "pay-per-use", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.billing.pay-per-use-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.billing.callback": { "id": "routes/app.billing.callback", "parentId": "routes/app.billing", "path": "callback", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.billing.callback-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.billing.recovery": { "id": "routes/app.billing.recovery", "parentId": "routes/app.billing", "path": "recovery", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.billing.recovery-l0sNRNKZ.js", "imports": [], "css": [] }, "routes/app.billing.pricing": { "id": "routes/app.billing.pricing", "parentId": "routes/app.billing", "path": "pricing", "index": void 0, "caseSensitive": void 0, "hasAction": true, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.billing.pricing-CDvsXsbk.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/PricingSelection-BaVbg180.js", "/assets/button-RlsfHhb_.js", "/assets/proxy-CdGEizrq.js", "/assets/components-C2v4lmCU.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/index-BJYSoprK.js", "/assets/badge-CSt7rrAu.js", "/assets/index-DI88vBYx.js", "/assets/useAppBridge-Bj34gXAL.js", "/assets/index-Ci0627_k.js"], "css": [] }, "routes/app.review": { "id": "routes/app.review", "parentId": "routes/app", "path": "review", "index": void 0, "caseSensitive": void 0, "hasAction": false, "hasLoader": false, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app.review-CdrXvDUw.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/index-DI88vBYx.js", "/assets/Page-Dli0YmGf.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/Button-3T1ZHLBP.js", "/assets/use-is-after-initial-mount-MUb1fdtO.js", "/assets/context-Dt_50QHC.js", "/assets/index-Ci0627_k.js"], "css": [] }, "routes/app._index": { "id": "routes/app._index", "parentId": "routes/app", "path": void 0, "index": true, "caseSensitive": void 0, "hasAction": false, "hasLoader": true, "hasClientAction": false, "hasClientLoader": false, "hasErrorBoundary": false, "module": "/assets/app._index-Dx6SIUzF.js", "imports": ["/assets/jsx-runtime-0DLF9kdB.js", "/assets/index-BJYSoprK.js", "/assets/index-DI88vBYx.js", "/assets/TitleBar-DFMSJ8Yc.js", "/assets/Button-3T1ZHLBP.js", "/assets/use-is-after-initial-mount-MUb1fdtO.js"], "css": [] } }, "url": "/assets/manifest-5bba1d9f.js", "version": "5bba1d9f" };
const mode = "production";
const assetsBuildDirectory = "build\\client";
const basename = "/";
const future = { "v3_fetcherPersist": true, "v3_relativeSplatPath": true, "v3_throwAbortReason": true, "v3_routeConfig": true, "v3_singleFetch": false, "v3_lazyRouteDiscovery": true, "unstable_optimizeDeps": false };
const isSpaMode = false;
const publicPath = "/";
const entry = { module: entryServer };
const routes = {
  "root": {
    id: "root",
    parentId: void 0,
    path: "",
    index: void 0,
    caseSensitive: void 0,
    module: route0
  },
  "routes/webhooks.app_purchases_one_time.update": {
    id: "routes/webhooks.app_purchases_one_time.update",
    parentId: "root",
    path: "webhooks/app_purchases_one_time/update",
    index: void 0,
    caseSensitive: void 0,
    module: route1
  },
  "routes/webhooks.app_subscriptions.update": {
    id: "routes/webhooks.app_subscriptions.update",
    parentId: "root",
    path: "webhooks/app_subscriptions/update",
    index: void 0,
    caseSensitive: void 0,
    module: route2
  },
  "routes/webhooks.app.scopes_update": {
    id: "routes/webhooks.app.scopes_update",
    parentId: "root",
    path: "webhooks/app/scopes_update",
    index: void 0,
    caseSensitive: void 0,
    module: route3
  },
  "routes/webhooks.app.uninstalled": {
    id: "routes/webhooks.app.uninstalled",
    parentId: "root",
    path: "webhooks/app/uninstalled",
    index: void 0,
    caseSensitive: void 0,
    module: route4
  },
  "routes/billing-success": {
    id: "routes/billing-success",
    parentId: "root",
    path: "billing-success",
    index: void 0,
    caseSensitive: void 0,
    module: route5
  },
  "routes/auth.login": {
    id: "routes/auth.login",
    parentId: "root",
    path: "auth/login",
    index: void 0,
    caseSensitive: void 0,
    module: route6
  },
  "routes/exitiframe": {
    id: "routes/exitiframe",
    parentId: "root",
    path: "exitiframe",
    index: void 0,
    caseSensitive: void 0,
    module: route7
  },
  "routes/auth.$": {
    id: "routes/auth.$",
    parentId: "root",
    path: "auth/*",
    index: void 0,
    caseSensitive: void 0,
    module: route8
  },
  "routes/_index": {
    id: "routes/_index",
    parentId: "root",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route9
  },
  "routes/app": {
    id: "routes/app",
    parentId: "root",
    path: "app",
    index: void 0,
    caseSensitive: void 0,
    module: route10
  },
  "routes/app.api.sync-subscription": {
    id: "routes/app.api.sync-subscription",
    parentId: "routes/app",
    path: "api/sync-subscription",
    index: void 0,
    caseSensitive: void 0,
    module: route11
  },
  "routes/app.api.billing-refresh": {
    id: "routes/app.api.billing-refresh",
    parentId: "routes/app",
    path: "api/billing-refresh",
    index: void 0,
    caseSensitive: void 0,
    module: route12
  },
  "routes/app.api.billing-status": {
    id: "routes/app.api.billing-status",
    parentId: "routes/app",
    path: "api/billing-status",
    index: void 0,
    caseSensitive: void 0,
    module: route13
  },
  "routes/app.seo-dashboard": {
    id: "routes/app.seo-dashboard",
    parentId: "routes/app",
    path: "seo-dashboard",
    index: void 0,
    caseSensitive: void 0,
    module: route14
  },
  "routes/app.api.products": {
    id: "routes/app.api.products",
    parentId: "routes/app",
    path: "api/products",
    index: void 0,
    caseSensitive: void 0,
    module: route15
  },
  "routes/app.additional": {
    id: "routes/app.additional",
    parentId: "routes/app",
    path: "additional",
    index: void 0,
    caseSensitive: void 0,
    module: route16
  },
  "routes/app.settings": {
    id: "routes/app.settings",
    parentId: "routes/app",
    path: "settings",
    index: void 0,
    caseSensitive: void 0,
    module: route17
  },
  "routes/app.billing": {
    id: "routes/app.billing",
    parentId: "routes/app",
    path: "billing",
    index: void 0,
    caseSensitive: void 0,
    module: route18
  },
  "routes/app.billing.pay-per-use": {
    id: "routes/app.billing.pay-per-use",
    parentId: "routes/app.billing",
    path: "pay-per-use",
    index: void 0,
    caseSensitive: void 0,
    module: route19
  },
  "routes/app.billing.callback": {
    id: "routes/app.billing.callback",
    parentId: "routes/app.billing",
    path: "callback",
    index: void 0,
    caseSensitive: void 0,
    module: route20
  },
  "routes/app.billing.recovery": {
    id: "routes/app.billing.recovery",
    parentId: "routes/app.billing",
    path: "recovery",
    index: void 0,
    caseSensitive: void 0,
    module: route21
  },
  "routes/app.billing.pricing": {
    id: "routes/app.billing.pricing",
    parentId: "routes/app.billing",
    path: "pricing",
    index: void 0,
    caseSensitive: void 0,
    module: route22
  },
  "routes/app.review": {
    id: "routes/app.review",
    parentId: "routes/app",
    path: "review",
    index: void 0,
    caseSensitive: void 0,
    module: route23
  },
  "routes/app._index": {
    id: "routes/app._index",
    parentId: "routes/app",
    path: void 0,
    index: true,
    caseSensitive: void 0,
    module: route24
  }
};
export {
  BillingService as B,
  RATE_LIMITERS as R,
  authenticate as a,
  applyRateLimit as b,
  assetsBuildDirectory as c,
  db as d,
  basename as e,
  future as f,
  getEnvironmentConfig as g,
  entry as h,
  isSpaMode as i,
  cache_server as j,
  mode as m,
  publicPath as p,
  routes as r,
  serverManifest as s,
  validateCSRFFromForm as v
};
