import{r as n}from"./index-BJYSoprK.js";import{j as p}from"./jsx-runtime-0DLF9kdB.js";import{S as k,c as u,a as v}from"./button-RlsfHhb_.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),f=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{const a=f(e);return a.charAt(0).toUpperCase()+a.slice(1)},d=(...e)=>e.filter((a,t,r)=>!!a&&a.trim()!==""&&r.indexOf(a)===t).join(" ").trim(),m=e=>{for(const a in e)if(a.startsWith("aria-")||a==="role"||a==="title")return!0};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var x={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=n.forwardRef(({color:e="currentColor",size:a=24,strokeWidth:t=2,absoluteStrokeWidth:r,className:s="",children:o,iconNode:h,...l},b)=>n.createElement("svg",{ref:b,...x,width:a,height:a,stroke:e,strokeWidth:r?Number(t)*24/Number(a):t,className:d("lucide",s),...!o&&!m(l)&&{"aria-hidden":"true"},...l},[...h.map(([g,w])=>n.createElement(g,w)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c=(e,a)=>{const t=n.forwardRef(({className:r,...s},o)=>n.createElement(C,{ref:o,iconNode:a,className:d(`lucide-${y(i(e))}`,`lucide-${e}`,r),...s}));return t.displayName=i(e),t};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],B=c("check",A);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],M=c("dollar-sign",_);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L=[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]],Z=c("star",L);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $=[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]],q=c("zap",$),j=v("inline-flex items-center justify-center rounded-full px-4 py-2 text-xs font-bold w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-2 [&>svg]:pointer-events-none transition-all duration-300 transform hover:scale-110 shadow-lg uppercase tracking-wider",{variants:{variant:{default:"bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",secondary:"bg-gray-100 text-black hover:bg-gray-200 shadow-gray-200 hover:shadow-gray-300 border border-gray-300",destructive:"bg-white text-black border-2 border-black hover:bg-black hover:text-white shadow-black/20 hover:shadow-black/40",outline:"border-2 border-black bg-white text-black hover:bg-black hover:text-white shadow-black/10 hover:shadow-black/30",success:"bg-black text-white hover:bg-gray-900 shadow-black/20 hover:shadow-black/40",warning:"bg-white text-black border border-gray-400 hover:bg-gray-100 shadow-gray-200 hover:shadow-gray-300"}},defaultVariants:{variant:"default"}});function z({className:e,variant:a,asChild:t=!1,...r}){const s=t?k:"span";return p.jsx(s,{"data-slot":"badge",className:u(j({variant:a}),e),...r})}export{z as B,B as C,M as D,Z as S,q as Z,c};
