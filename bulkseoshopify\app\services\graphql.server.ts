/**
 * GraphQL Service
 * Handles GraphQL operations with proper error handling, rate limiting, and query optimization
 */

import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";

export interface GraphQLResponse<T = any> {
  data?: T;
  errors?: Array<{
    message: string;
    locations?: Array<{ line: number; column: number }>;
    path?: Array<string | number>;
    extensions?: {
      code?: string;
      cost?: number;
      maxCost?: number;
    };
  }>;
  extensions?: {
    cost?: {
      requestedQueryCost: number;
      actualQueryCost: number;
      throttleStatus: {
        maximumAvailable: number;
        currentlyAvailable: number;
        restoreRate: number;
      };
    };
  };
}

export interface GraphQLQueryOptions {
  variables?: Record<string, any>;
  retries?: number;
  timeout?: number;
  skipRateLimit?: boolean;
}

export interface BatchGraphQLOperation {
  query: string;
  variables?: Record<string, any>;
  operationName?: string;
}

/**
 * Enhanced GraphQL Service with rate limiting and error handling
 */
export class GraphQLService {
  private admin: any;
  private shop: string;
  private maxQueryCost = 1000; // Shopify's default limit
  private maxRetries = 3;

  constructor(admin: any, shop: string) {
    this.admin = admin;
    this.shop = shop;
  }

  /**
   * Execute a single GraphQL query with comprehensive error handling
   */
  async query<T = any>(
    query: string, 
    options: GraphQLQueryOptions = {},
    context?: ErrorContext
  ): Promise<GraphQLResponse<T>> {
    const { variables, retries = this.maxRetries, timeout = 30000, skipRateLimit = false } = options;

    // Apply rate limiting unless skipped
    if (!skipRateLimit) {
      try {
        await applyRateLimit(RATE_LIMITERS.API_GENERAL(this.shop), context);
      } catch (error) {
        throw createError('RATE_LIMIT_EXCEEDED', { ...context, shop: this.shop });
      }
    }

    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔍 GraphQL Query (attempt ${attempt}/${retries}) for shop: ${this.shop}`);
        
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('GraphQL query timeout')), timeout);
        });

        // Execute query with timeout
        const queryPromise = this.admin.graphql(query, variables ? { variables } : undefined);
        const response = await Promise.race([queryPromise, timeoutPromise]);
        const result: GraphQLResponse<T> = await response.json();

        // Check for GraphQL errors
        if (result.errors && result.errors.length > 0) {
          const errorMessages = result.errors.map(err => err.message).join(', ');
          console.warn(`⚠️ GraphQL errors: ${errorMessages}`);

          // Check for rate limiting errors
          const rateLimitError = result.errors.find(err => 
            err.extensions?.code === 'THROTTLED' || 
            err.message.includes('throttled') ||
            err.message.includes('rate limit')
          );

          if (rateLimitError && attempt < retries) {
            const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
            console.log(`⏳ Rate limited, waiting ${delay}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, delay));
            continue;
          }

          // Check for query cost errors
          const costError = result.errors.find(err => 
            err.message.includes('query cost') || 
            err.message.includes('complexity')
          );

          if (costError) {
            throw createError('GRAPHQL_QUERY_TOO_COMPLEX', {
              ...context,
              shop: this.shop,
              metadata: { error: costError.message }
            });
          }
        }

        // Log query cost information if available
        if (result.extensions?.cost) {
          const cost = result.extensions.cost;
          console.log(`📊 Query cost: ${cost.actualQueryCost}/${cost.requestedQueryCost} (available: ${cost.throttleStatus.currentlyAvailable}/${cost.throttleStatus.maximumAvailable})`);
          
          // Warn if approaching limits
          if (cost.throttleStatus.currentlyAvailable < 100) {
            console.warn(`⚠️ GraphQL rate limit approaching: ${cost.throttleStatus.currentlyAvailable} points remaining`);
          }
        }

        console.log(`✅ GraphQL query successful for shop: ${this.shop}`);
        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('GraphQL query failed');
        console.error(`❌ GraphQL query failed (attempt ${attempt}/${retries}):`, error);

        // Don't retry on certain errors
        if (error instanceof Error) {
          if (error.message.includes('authentication') || 
              error.message.includes('unauthorized') ||
              error.message.includes('forbidden')) {
            break;
          }
        }

        // Wait before retry (except on last attempt)
        if (attempt < retries) {
          const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Log the final error
    await logError(lastError!, {
      ...context,
      shop: this.shop,
      action: 'graphql_query',
      metadata: { query: query.substring(0, 200) } // Log first 200 chars of query
    });

    throw lastError!;
  }

  /**
   * Execute multiple GraphQL operations in batches to avoid query cost limits
   */
  async batchQuery<T = any>(
    operations: BatchGraphQLOperation[],
    batchSize: number = 5,
    context?: ErrorContext
  ): Promise<GraphQLResponse<T>[]> {
    const results: GraphQLResponse<T>[] = [];
    
    console.log(`🔄 Executing ${operations.length} GraphQL operations in batches of ${batchSize}`);

    for (let i = 0; i < operations.length; i += batchSize) {
      const batch = operations.slice(i, i + batchSize);
      console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(operations.length / batchSize)}`);

      // Execute batch operations in parallel
      const batchPromises = batch.map(operation => 
        this.query<T>(operation.query, { 
          variables: operation.variables,
          skipRateLimit: true // We apply rate limiting at batch level
        }, context)
      );

      try {
        const batchResults = await Promise.allSettled(batchPromises);
        
        for (const result of batchResults) {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            console.error('❌ Batch operation failed:', result.reason);
            // Add error result to maintain array consistency
            results.push({
              errors: [{ message: result.reason?.message || 'Batch operation failed' }]
            });
          }
        }

        // Add delay between batches to respect rate limits
        if (i + batchSize < operations.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

      } catch (error) {
        console.error('❌ Batch execution failed:', error);
        await logError(
          error instanceof Error ? error : new Error('Batch GraphQL execution failed'),
          { ...context, shop: this.shop, action: 'graphql_batch_query' }
        );
        throw error;
      }
    }

    console.log(`✅ Completed ${operations.length} GraphQL operations`);
    return results;
  }

  /**
   * Get products with cursor-based pagination to handle large datasets
   */
  async getAllProducts(
    searchQuery?: string,
    maxProducts: number = 1000,
    context?: ErrorContext
  ): Promise<any[]> {
    const allProducts: any[] = [];
    let hasNextPage = true;
    let cursor: string | null = null;
    let batchCount = 0;
    const maxBatches = Math.ceil(maxProducts / 50);

    console.log(`📦 Fetching products (max: ${maxProducts}) for shop: ${this.shop}`);

    while (hasNextPage && batchCount < maxBatches) {
      batchCount++;
      
      const query = `
        query getProducts($first: Int!, $after: String, $query: String) {
          products(first: $first, after: $after, query: $query) {
            edges {
              node {
                id
                title
                description
                handle
                status
                seo {
                  title
                  description
                }
                images(first: 5) {
                  edges {
                    node {
                      id
                      altText
                      url
                    }
                  }
                }
              }
              cursor
            }
            pageInfo {
              hasNextPage
              endCursor
            }
          }
        }
      `;

      try {
        const result: any = await this.query(query, {
          variables: {
            first: Math.min(50, maxProducts - allProducts.length),
            after: cursor,
            query: searchQuery
          }
        }, context);

        if (result.data?.products?.edges) {
          const products = result.data.products.edges.map((edge: any) => edge.node);
          allProducts.push(...products);
          
          hasNextPage = result.data.products.pageInfo.hasNextPage;
          cursor = result.data.products.pageInfo.endCursor;
          
          console.log(`📦 Fetched batch ${batchCount}: ${products.length} products (total: ${allProducts.length})`);
        } else {
          console.warn('⚠️ No products data in response');
          break;
        }

        // Stop if we've reached the maximum
        if (allProducts.length >= maxProducts) {
          console.log(`🛑 Reached maximum product limit: ${maxProducts}`);
          break;
        }

      } catch (error) {
        console.error(`❌ Failed to fetch product batch ${batchCount}:`, error);
        break;
      }
    }

    console.log(`✅ Fetched ${allProducts.length} products total`);
    return allProducts.slice(0, maxProducts);
  }

  /**
   * Update products in batches with proper error handling
   */
  async updateProductsBatch(
    updates: Array<{ id: string; input: any }>,
    batchSize: number = 10,
    context?: ErrorContext
  ): Promise<{ success: number; failed: number; errors: string[] }> {
    const results = { success: 0, failed: 0, errors: [] as string[] };
    
    console.log(`🔄 Updating ${updates.length} products in batches of ${batchSize}`);

    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      
      const operations = batch.map(update => ({
        query: `
          mutation updateProduct($input: ProductInput!) {
            productUpdate(input: $input) {
              product {
                id
                title
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        variables: { input: { id: update.id, ...update.input } }
      }));

      try {
        const batchResults = await this.batchQuery(operations, batchSize, context);
        
        for (const result of batchResults) {
          if (result.data?.productUpdate?.product) {
            results.success++;
          } else {
            results.failed++;
            const errors = result.data?.productUpdate?.userErrors || result.errors || [];
            const errorMessages = errors.map((err: any) => err.message).join(', ');
            results.errors.push(errorMessages);
          }
        }

      } catch (error) {
        console.error(`❌ Batch update failed:`, error);
        results.failed += batch.length;
        results.errors.push(error instanceof Error ? error.message : 'Batch update failed');
      }
    }

    console.log(`✅ Product updates completed: ${results.success} success, ${results.failed} failed`);
    return results;
  }
}

/**
 * Create GraphQL service instance
 */
export function createGraphQLService(admin: any, shop: string): GraphQLService {
  return new GraphQLService(admin, shop);
}
