const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-C61uXkzv.js","assets/index-BJYSoprK.js","assets/jsx-runtime-0DLF9kdB.js","assets/index-BOY1O_0a.js","assets/NavMenu-B_tHUzzF.js","assets/TitleBar-DFMSJ8Yc.js","assets/useAppBridge-Bj34gXAL.js"])))=>i.map(i=>d[i]);
import{j as o}from"./jsx-runtime-0DLF9kdB.js";import{r as d,R as Me,a as Un}from"./index-BJYSoprK.js";import{m as V}from"./proxy-CdGEizrq.js";import{u as ee,b as ht,c as oe,B as fe}from"./button-RlsfHhb_.js";import{I as yo}from"./input-Bg89CcEX.js";import{a as bt,b as So}from"./index-Ci0627_k.js";import{c as Fe,C as Hn,S as No,B as Ee,Z as Co,D as Po}from"./badge-CSt7rrAu.js";import{n as rt,a as $t,u as jo}from"./components-C2v4lmCU.js";import{u as Eo}from"./index-DI88vBYx.js";import{T as Oo}from"./TitleBar-DFMSJ8Yc.js";import{A as ko}from"./index-DsLkuawQ.js";const Ao="modulepreload",Ro=function(e){return"/"+e},pn={},Kn=function(t,n,r){let s=Promise.resolve();if(n&&n.length>0){let c=function(l){return Promise.all(l.map(h=>Promise.resolve(h).then(m=>({status:"fulfilled",value:m}),m=>({status:"rejected",reason:m}))))};document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),u=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));s=c(n.map(l=>{if(l=Ro(l),l in pn)return;pn[l]=!0;const h=l.endsWith(".css"),m=h?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${l}"]${m}`))return;const p=document.createElement("link");if(p.rel=h?"stylesheet":Ao,h||(p.as="script"),p.crossOrigin="",p.href=l,u&&p.setAttribute("nonce",u),document.head.appendChild(p),h)return new Promise((v,y)=>{p.addEventListener("load",v),p.addEventListener("error",()=>y(new Error(`Unable to preload CSS for ${l}`)))})}))}function a(c){const i=new Event("vite:preloadError",{cancelable:!0});if(i.payload=c,window.dispatchEvent(i),!i.defaultPrevented)throw c}return s.then(c=>{for(const i of c||[])i.status==="rejected"&&a(i.reason);return t().catch(a)})},To=({animationData:e,loop:t=!0})=>{const[n,r]=d.useState(null);return d.useEffect(()=>{Kn(()=>import("./index.es-C61uXkzv.js"),__vite__mapDeps([0,1,2])).then(s=>{r(()=>s.default)})},[]),n?o.jsx(n,{animationData:e,loop:t}):o.jsx("div",{className:"animate-pulse bg-gray-200 rounded w-full h-full"})},Io=({size:e=100,className:t=""})=>{const n={v:"5.7.4",fr:30,ip:0,op:60,w:200,h:200,nm:"Success",ddd:0,assets:[],layers:[{ddd:0,ind:1,ty:4,nm:"Check",sr:1,ks:{o:{a:0,k:100},r:{a:0,k:0},p:{a:0,k:[100,100,0]},a:{a:0,k:[0,0,0]},s:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0,0,100]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:20,s:[120,120,100]},{t:40,s:[100,100,100]}]}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ks:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:10,s:[{i:[[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0]],v:[[-20,0],[-20,0],[-20,0]],c:!1}]},{t:50,s:[{i:[[0,0],[0,0],[0,0]],o:[[0,0],[0,0],[0,0]],v:[[-20,0],[0,20],[30,-20]],c:!1}]}]}},{ty:"st",c:{a:0,k:[.2,.8,.2,1]},o:{a:0,k:100},w:{a:0,k:8},lc:2,lj:2,ml:4},{ty:"tr",p:{a:0,k:[0,0]},a:{a:0,k:[0,0]},s:{a:0,k:[100,100]},r:{a:0,k:0},o:{a:0,k:100}}]}],ip:0,op:60,st:0,bm:0},{ddd:0,ind:2,ty:4,nm:"Circle",sr:1,ks:{o:{a:0,k:100},r:{a:0,k:0},p:{a:0,k:[100,100,0]},a:{a:0,k:[0,0,0]},s:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0,0,100]},{t:30,s:[100,100,100]}]}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[80,80]},p:{a:0,k:[0,0]}},{ty:"st",c:{a:0,k:[.2,.8,.2,1]},o:{a:0,k:100},w:{a:0,k:4},lc:1,lj:1,ml:4},{ty:"tr",p:{a:0,k:[0,0]},a:{a:0,k:[0,0]},s:{a:0,k:[100,100]},r:{a:0,k:0},o:{a:0,k:100}}]}],ip:0,op:60,st:0,bm:0}]};return o.jsx(V.div,{initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.5},className:t,style:{width:e,height:e},children:o.jsx(To,{animationData:n,loop:!1})})},_o=({size:e=100,className:t=""})=>o.jsx(V.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},className:t,style:{width:e,height:e,display:"flex",alignItems:"center",justifyContent:"center"},children:o.jsx(V.div,{animate:{borderRadius:["20%","50%","20%"],rotate:[0,180,360],scale:[1,1.2,1]},transition:{duration:3,repeat:1/0,ease:"easeInOut"},style:{width:e*.6,height:e*.6,background:"linear-gradient(45deg, #000000, #333333)",borderRadius:"20%"}})});function xn(e,[t,n]){return Math.min(n,Math.max(t,e))}function J(e,t,{checkForDefaultPrevented:n=!0}={}){return function(s){if(e==null||e(s),n===!1||!s.defaultPrevented)return t==null?void 0:t(s)}}function et(e,t=[]){let n=[];function r(a,c){const i=d.createContext(c),u=n.length;n=[...n,c];const l=m=>{var x;const{scope:p,children:v,...y}=m,f=((x=p==null?void 0:p[e])==null?void 0:x[u])||i,b=d.useMemo(()=>y,Object.values(y));return o.jsx(f.Provider,{value:b,children:v})};l.displayName=a+"Provider";function h(m,p){var f;const v=((f=p==null?void 0:p[e])==null?void 0:f[u])||i,y=d.useContext(v);if(y)return y;if(c!==void 0)return c;throw new Error(`\`${m}\` must be used within \`${a}\``)}return[l,h]}const s=()=>{const a=n.map(c=>d.createContext(c));return function(i){const u=(i==null?void 0:i[e])||a;return d.useMemo(()=>({[`__scope${e}`]:{...i,[e]:u}}),[i,u])}};return s.scopeName=e,[r,Mo(s,...t)]}function Mo(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(a){const c=r.reduce((i,{useScope:u,scopeName:l})=>{const m=u(a)[`__scope${l}`];return{...i,...m}},{});return d.useMemo(()=>({[`__scope${t.scopeName}`]:c}),[c])}};return n.scopeName=t.scopeName,n}function Do(e){const t=e+"CollectionProvider",[n,r]=et(t),[s,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=f=>{const{scope:b,children:x}=f,w=Me.useRef(null),S=Me.useRef(new Map).current;return o.jsx(s,{scope:b,itemMap:S,collectionRef:w,children:x})};c.displayName=t;const i=e+"CollectionSlot",u=ht(i),l=Me.forwardRef((f,b)=>{const{scope:x,children:w}=f,S=a(i,x),N=ee(b,S.collectionRef);return o.jsx(u,{ref:N,children:w})});l.displayName=i;const h=e+"CollectionItemSlot",m="data-radix-collection-item",p=ht(h),v=Me.forwardRef((f,b)=>{const{scope:x,children:w,...S}=f,N=Me.useRef(null),j=ee(b,N),O=a(h,x);return Me.useEffect(()=>(O.itemMap.set(N,{ref:N,...S}),()=>void O.itemMap.delete(N))),o.jsx(p,{[m]:"",ref:j,children:w})});v.displayName=h;function y(f){const b=a(e+"CollectionConsumer",f);return Me.useCallback(()=>{const w=b.collectionRef.current;if(!w)return[];const S=Array.from(w.querySelectorAll(`[${m}]`));return Array.from(b.itemMap.values()).sort((O,C)=>S.indexOf(O.ref.current)-S.indexOf(C.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:c,Slot:l,ItemSlot:v},y,r]}var Lo=d.createContext(void 0);function zo(e){const t=d.useContext(Lo);return e||t||"ltr"}var $o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Y=$o.reduce((e,t)=>{const n=ht(`Primitive.${t}`),r=d.forwardRef((s,a)=>{const{asChild:c,...i}=s,u=c?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),o.jsx(u,{...i,ref:a})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Fo(e,t){e&&bt.flushSync(()=>e.dispatchEvent(t))}function De(e){const t=d.useRef(e);return d.useEffect(()=>{t.current=e}),d.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Bo(e,t=globalThis==null?void 0:globalThis.document){const n=De(e);d.useEffect(()=>{const r=s=>{s.key==="Escape"&&n(s)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Wo="DismissableLayer",Ft="dismissableLayer.update",Vo="dismissableLayer.pointerDownOutside",Uo="dismissableLayer.focusOutside",gn,qn=d.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Yn=d.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:s,onFocusOutside:a,onInteractOutside:c,onDismiss:i,...u}=e,l=d.useContext(qn),[h,m]=d.useState(null),p=(h==null?void 0:h.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,v]=d.useState({}),y=ee(t,C=>m(C)),f=Array.from(l.layers),[b]=[...l.layersWithOutsidePointerEventsDisabled].slice(-1),x=f.indexOf(b),w=h?f.indexOf(h):-1,S=l.layersWithOutsidePointerEventsDisabled.size>0,N=w>=x,j=qo(C=>{const R=C.target,I=[...l.branches].some(L=>L.contains(R));!N||I||(s==null||s(C),c==null||c(C),C.defaultPrevented||i==null||i())},p),O=Yo(C=>{const R=C.target;[...l.branches].some(L=>L.contains(R))||(a==null||a(C),c==null||c(C),C.defaultPrevented||i==null||i())},p);return Bo(C=>{w===l.layers.size-1&&(r==null||r(C),!C.defaultPrevented&&i&&(C.preventDefault(),i()))},p),d.useEffect(()=>{if(h)return n&&(l.layersWithOutsidePointerEventsDisabled.size===0&&(gn=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),l.layersWithOutsidePointerEventsDisabled.add(h)),l.layers.add(h),vn(),()=>{n&&l.layersWithOutsidePointerEventsDisabled.size===1&&(p.body.style.pointerEvents=gn)}},[h,p,n,l]),d.useEffect(()=>()=>{h&&(l.layers.delete(h),l.layersWithOutsidePointerEventsDisabled.delete(h),vn())},[h,l]),d.useEffect(()=>{const C=()=>v({});return document.addEventListener(Ft,C),()=>document.removeEventListener(Ft,C)},[]),o.jsx(Y.div,{...u,ref:y,style:{pointerEvents:S?N?"auto":"none":void 0,...e.style},onFocusCapture:J(e.onFocusCapture,O.onFocusCapture),onBlurCapture:J(e.onBlurCapture,O.onBlurCapture),onPointerDownCapture:J(e.onPointerDownCapture,j.onPointerDownCapture)})});Yn.displayName=Wo;var Ho="DismissableLayerBranch",Ko=d.forwardRef((e,t)=>{const n=d.useContext(qn),r=d.useRef(null),s=ee(t,r);return d.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),o.jsx(Y.div,{...e,ref:s})});Ko.displayName=Ho;function qo(e,t=globalThis==null?void 0:globalThis.document){const n=De(e),r=d.useRef(!1),s=d.useRef(()=>{});return d.useEffect(()=>{const a=i=>{if(i.target&&!r.current){let u=function(){Xn(Vo,n,l,{discrete:!0})};const l={originalEvent:i};i.pointerType==="touch"?(t.removeEventListener("click",s.current),s.current=u,t.addEventListener("click",s.current,{once:!0})):u()}else t.removeEventListener("click",s.current);r.current=!1},c=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),t.removeEventListener("pointerdown",a),t.removeEventListener("click",s.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Yo(e,t=globalThis==null?void 0:globalThis.document){const n=De(e),r=d.useRef(!1);return d.useEffect(()=>{const s=a=>{a.target&&!r.current&&Xn(Uo,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",s),()=>t.removeEventListener("focusin",s)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function vn(){const e=new CustomEvent(Ft);document.dispatchEvent(e)}function Xn(e,t,n,{discrete:r}){const s=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&s.addEventListener(e,t,{once:!0}),r?Fo(s,a):s.dispatchEvent(a)}var At=0;function Xo(){d.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??wn()),document.body.insertAdjacentElement("beforeend",e[1]??wn()),At++,()=>{At===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),At--}},[])}function wn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Rt="focusScope.autoFocusOnMount",Tt="focusScope.autoFocusOnUnmount",bn={bubbles:!1,cancelable:!0},Go="FocusScope",Gn=d.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:a,...c}=e,[i,u]=d.useState(null),l=De(s),h=De(a),m=d.useRef(null),p=ee(t,f=>u(f)),v=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(r){let f=function(S){if(v.paused||!i)return;const N=S.target;i.contains(N)?m.current=N:je(m.current,{select:!0})},b=function(S){if(v.paused||!i)return;const N=S.relatedTarget;N!==null&&(i.contains(N)||je(m.current,{select:!0}))},x=function(S){if(document.activeElement===document.body)for(const j of S)j.removedNodes.length>0&&je(i)};document.addEventListener("focusin",f),document.addEventListener("focusout",b);const w=new MutationObserver(x);return i&&w.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",f),document.removeEventListener("focusout",b),w.disconnect()}}},[r,i,v.paused]),d.useEffect(()=>{if(i){Sn.add(v);const f=document.activeElement;if(!i.contains(f)){const x=new CustomEvent(Rt,bn);i.addEventListener(Rt,l),i.dispatchEvent(x),x.defaultPrevented||(Zo(ns(Zn(i)),{select:!0}),document.activeElement===f&&je(i))}return()=>{i.removeEventListener(Rt,l),setTimeout(()=>{const x=new CustomEvent(Tt,bn);i.addEventListener(Tt,h),i.dispatchEvent(x),x.defaultPrevented||je(f??document.body,{select:!0}),i.removeEventListener(Tt,h),Sn.remove(v)},0)}}},[i,l,h,v]);const y=d.useCallback(f=>{if(!n&&!r||v.paused)return;const b=f.key==="Tab"&&!f.altKey&&!f.ctrlKey&&!f.metaKey,x=document.activeElement;if(b&&x){const w=f.currentTarget,[S,N]=Jo(w);S&&N?!f.shiftKey&&x===N?(f.preventDefault(),n&&je(S,{select:!0})):f.shiftKey&&x===S&&(f.preventDefault(),n&&je(N,{select:!0})):x===w&&f.preventDefault()}},[n,r,v.paused]);return o.jsx(Y.div,{tabIndex:-1,...c,ref:p,onKeyDown:y})});Gn.displayName=Go;function Zo(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(je(r,{select:t}),document.activeElement!==n)return}function Jo(e){const t=Zn(e),n=yn(t,e),r=yn(t.reverse(),e);return[n,r]}function Zn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const s=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||s?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function yn(e,t){for(const n of e)if(!Qo(n,{upTo:t}))return n}function Qo(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function es(e){return e instanceof HTMLInputElement&&"select"in e}function je(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&es(e)&&t&&e.select()}}var Sn=ts();function ts(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Nn(e,t),e.unshift(t)},remove(t){var n;e=Nn(e,t),(n=e[0])==null||n.resume()}}}function Nn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function ns(e){return e.filter(t=>t.tagName!=="A")}var se=globalThis!=null&&globalThis.document?d.useLayoutEffect:()=>{},rs=Un[" useId ".trim().toString()]||(()=>{}),os=0;function Zt(e){const[t,n]=d.useState(rs());return se(()=>{n(r=>r??String(os++))},[e]),e||(t?`radix-${t}`:"")}const ss=["top","right","bottom","left"],ke=Math.min,ce=Math.max,mt=Math.round,ot=Math.floor,ye=e=>({x:e,y:e}),is={left:"right",right:"left",bottom:"top",top:"bottom"},as={start:"end",end:"start"};function Bt(e,t,n){return ce(e,ke(t,n))}function Ce(e,t){return typeof e=="function"?e(t):e}function Pe(e){return e.split("-")[0]}function qe(e){return e.split("-")[1]}function Jt(e){return e==="x"?"y":"x"}function Qt(e){return e==="y"?"height":"width"}const cs=new Set(["top","bottom"]);function be(e){return cs.has(Pe(e))?"y":"x"}function en(e){return Jt(be(e))}function ls(e,t,n){n===void 0&&(n=!1);const r=qe(e),s=en(e),a=Qt(s);let c=s==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(c=pt(c)),[c,pt(c)]}function ds(e){const t=pt(e);return[Wt(e),t,Wt(t)]}function Wt(e){return e.replace(/start|end/g,t=>as[t])}const Cn=["left","right"],Pn=["right","left"],us=["top","bottom"],fs=["bottom","top"];function hs(e,t,n){switch(e){case"top":case"bottom":return n?t?Pn:Cn:t?Cn:Pn;case"left":case"right":return t?us:fs;default:return[]}}function ms(e,t,n,r){const s=qe(e);let a=hs(Pe(e),n==="start",r);return s&&(a=a.map(c=>c+"-"+s),t&&(a=a.concat(a.map(Wt)))),a}function pt(e){return e.replace(/left|right|bottom|top/g,t=>is[t])}function ps(e){return{top:0,right:0,bottom:0,left:0,...e}}function Jn(e){return typeof e!="number"?ps(e):{top:e,right:e,bottom:e,left:e}}function xt(e){const{x:t,y:n,width:r,height:s}=e;return{width:r,height:s,top:n,left:t,right:t+r,bottom:n+s,x:t,y:n}}function jn(e,t,n){let{reference:r,floating:s}=e;const a=be(t),c=en(t),i=Qt(c),u=Pe(t),l=a==="y",h=r.x+r.width/2-s.width/2,m=r.y+r.height/2-s.height/2,p=r[i]/2-s[i]/2;let v;switch(u){case"top":v={x:h,y:r.y-s.height};break;case"bottom":v={x:h,y:r.y+r.height};break;case"right":v={x:r.x+r.width,y:m};break;case"left":v={x:r.x-s.width,y:m};break;default:v={x:r.x,y:r.y}}switch(qe(t)){case"start":v[c]-=p*(n&&l?-1:1);break;case"end":v[c]+=p*(n&&l?-1:1);break}return v}const xs=async(e,t,n)=>{const{placement:r="bottom",strategy:s="absolute",middleware:a=[],platform:c}=n,i=a.filter(Boolean),u=await(c.isRTL==null?void 0:c.isRTL(t));let l=await c.getElementRects({reference:e,floating:t,strategy:s}),{x:h,y:m}=jn(l,r,u),p=r,v={},y=0;for(let f=0;f<i.length;f++){const{name:b,fn:x}=i[f],{x:w,y:S,data:N,reset:j}=await x({x:h,y:m,initialPlacement:r,placement:p,strategy:s,middlewareData:v,rects:l,platform:c,elements:{reference:e,floating:t}});h=w??h,m=S??m,v={...v,[b]:{...v[b],...N}},j&&y<=50&&(y++,typeof j=="object"&&(j.placement&&(p=j.placement),j.rects&&(l=j.rects===!0?await c.getElementRects({reference:e,floating:t,strategy:s}):j.rects),{x:h,y:m}=jn(l,p,u)),f=-1)}return{x:h,y:m,placement:p,strategy:s,middlewareData:v}};async function Je(e,t){var n;t===void 0&&(t={});const{x:r,y:s,platform:a,rects:c,elements:i,strategy:u}=e,{boundary:l="clippingAncestors",rootBoundary:h="viewport",elementContext:m="floating",altBoundary:p=!1,padding:v=0}=Ce(t,e),y=Jn(v),b=i[p?m==="floating"?"reference":"floating":m],x=xt(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(b)))==null||n?b:b.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(i.floating)),boundary:l,rootBoundary:h,strategy:u})),w=m==="floating"?{x:r,y:s,width:c.floating.width,height:c.floating.height}:c.reference,S=await(a.getOffsetParent==null?void 0:a.getOffsetParent(i.floating)),N=await(a.isElement==null?void 0:a.isElement(S))?await(a.getScale==null?void 0:a.getScale(S))||{x:1,y:1}:{x:1,y:1},j=xt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:w,offsetParent:S,strategy:u}):w);return{top:(x.top-j.top+y.top)/N.y,bottom:(j.bottom-x.bottom+y.bottom)/N.y,left:(x.left-j.left+y.left)/N.x,right:(j.right-x.right+y.right)/N.x}}const gs=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:s,rects:a,platform:c,elements:i,middlewareData:u}=t,{element:l,padding:h=0}=Ce(e,t)||{};if(l==null)return{};const m=Jn(h),p={x:n,y:r},v=en(s),y=Qt(v),f=await c.getDimensions(l),b=v==="y",x=b?"top":"left",w=b?"bottom":"right",S=b?"clientHeight":"clientWidth",N=a.reference[y]+a.reference[v]-p[v]-a.floating[y],j=p[v]-a.reference[v],O=await(c.getOffsetParent==null?void 0:c.getOffsetParent(l));let C=O?O[S]:0;(!C||!await(c.isElement==null?void 0:c.isElement(O)))&&(C=i.floating[S]||a.floating[y]);const R=N/2-j/2,I=C/2-f[y]/2-1,L=ke(m[x],I),$=ke(m[w],I),U=L,_=C-f[y]-$,z=C/2-f[y]/2+R,F=Bt(U,z,_),M=!u.arrow&&qe(s)!=null&&z!==F&&a.reference[y]/2-(z<U?L:$)-f[y]/2<0,D=M?z<U?z-U:z-_:0;return{[v]:p[v]+D,data:{[v]:F,centerOffset:z-F-D,...M&&{alignmentOffset:D}},reset:M}}}),vs=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:s,middlewareData:a,rects:c,initialPlacement:i,platform:u,elements:l}=t,{mainAxis:h=!0,crossAxis:m=!0,fallbackPlacements:p,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:f=!0,...b}=Ce(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const x=Pe(s),w=be(i),S=Pe(i)===i,N=await(u.isRTL==null?void 0:u.isRTL(l.floating)),j=p||(S||!f?[pt(i)]:ds(i)),O=y!=="none";!p&&O&&j.push(...ms(i,f,y,N));const C=[i,...j],R=await Je(t,b),I=[];let L=((r=a.flip)==null?void 0:r.overflows)||[];if(h&&I.push(R[x]),m){const z=ls(s,c,N);I.push(R[z[0]],R[z[1]])}if(L=[...L,{placement:s,overflows:I}],!I.every(z=>z<=0)){var $,U;const z=((($=a.flip)==null?void 0:$.index)||0)+1,F=C[z];if(F&&(!(m==="alignment"?w!==be(F):!1)||L.every(T=>T.overflows[0]>0&&be(T.placement)===w)))return{data:{index:z,overflows:L},reset:{placement:F}};let M=(U=L.filter(D=>D.overflows[0]<=0).sort((D,T)=>D.overflows[1]-T.overflows[1])[0])==null?void 0:U.placement;if(!M)switch(v){case"bestFit":{var _;const D=(_=L.filter(T=>{if(O){const q=be(T.placement);return q===w||q==="y"}return!0}).map(T=>[T.placement,T.overflows.filter(q=>q>0).reduce((q,ne)=>q+ne,0)]).sort((T,q)=>T[1]-q[1])[0])==null?void 0:_[0];D&&(M=D);break}case"initialPlacement":M=i;break}if(s!==M)return{reset:{placement:M}}}return{}}}};function En(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function On(e){return ss.some(t=>e[t]>=0)}const ws=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...s}=Ce(e,t);switch(r){case"referenceHidden":{const a=await Je(t,{...s,elementContext:"reference"}),c=En(a,n.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:On(c)}}}case"escaped":{const a=await Je(t,{...s,altBoundary:!0}),c=En(a,n.floating);return{data:{escapedOffsets:c,escaped:On(c)}}}default:return{}}}}},Qn=new Set(["left","top"]);async function bs(e,t){const{placement:n,platform:r,elements:s}=e,a=await(r.isRTL==null?void 0:r.isRTL(s.floating)),c=Pe(n),i=qe(n),u=be(n)==="y",l=Qn.has(c)?-1:1,h=a&&u?-1:1,m=Ce(t,e);let{mainAxis:p,crossAxis:v,alignmentAxis:y}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return i&&typeof y=="number"&&(v=i==="end"?y*-1:y),u?{x:v*h,y:p*l}:{x:p*l,y:v*h}}const ys=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:s,y:a,placement:c,middlewareData:i}=t,u=await bs(t,e);return c===((n=i.offset)==null?void 0:n.placement)&&(r=i.arrow)!=null&&r.alignmentOffset?{}:{x:s+u.x,y:a+u.y,data:{...u,placement:c}}}}},Ss=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:s}=t,{mainAxis:a=!0,crossAxis:c=!1,limiter:i={fn:b=>{let{x,y:w}=b;return{x,y:w}}},...u}=Ce(e,t),l={x:n,y:r},h=await Je(t,u),m=be(Pe(s)),p=Jt(m);let v=l[p],y=l[m];if(a){const b=p==="y"?"top":"left",x=p==="y"?"bottom":"right",w=v+h[b],S=v-h[x];v=Bt(w,v,S)}if(c){const b=m==="y"?"top":"left",x=m==="y"?"bottom":"right",w=y+h[b],S=y-h[x];y=Bt(w,y,S)}const f=i.fn({...t,[p]:v,[m]:y});return{...f,data:{x:f.x-n,y:f.y-r,enabled:{[p]:a,[m]:c}}}}}},Ns=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:s,rects:a,middlewareData:c}=t,{offset:i=0,mainAxis:u=!0,crossAxis:l=!0}=Ce(e,t),h={x:n,y:r},m=be(s),p=Jt(m);let v=h[p],y=h[m];const f=Ce(i,t),b=typeof f=="number"?{mainAxis:f,crossAxis:0}:{mainAxis:0,crossAxis:0,...f};if(u){const S=p==="y"?"height":"width",N=a.reference[p]-a.floating[S]+b.mainAxis,j=a.reference[p]+a.reference[S]-b.mainAxis;v<N?v=N:v>j&&(v=j)}if(l){var x,w;const S=p==="y"?"width":"height",N=Qn.has(Pe(s)),j=a.reference[m]-a.floating[S]+(N&&((x=c.offset)==null?void 0:x[m])||0)+(N?0:b.crossAxis),O=a.reference[m]+a.reference[S]+(N?0:((w=c.offset)==null?void 0:w[m])||0)-(N?b.crossAxis:0);y<j?y=j:y>O&&(y=O)}return{[p]:v,[m]:y}}}},Cs=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:s,rects:a,platform:c,elements:i}=t,{apply:u=()=>{},...l}=Ce(e,t),h=await Je(t,l),m=Pe(s),p=qe(s),v=be(s)==="y",{width:y,height:f}=a.floating;let b,x;m==="top"||m==="bottom"?(b=m,x=p===(await(c.isRTL==null?void 0:c.isRTL(i.floating))?"start":"end")?"left":"right"):(x=m,b=p==="end"?"top":"bottom");const w=f-h.top-h.bottom,S=y-h.left-h.right,N=ke(f-h[b],w),j=ke(y-h[x],S),O=!t.middlewareData.shift;let C=N,R=j;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(R=S),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(C=w),O&&!p){const L=ce(h.left,0),$=ce(h.right,0),U=ce(h.top,0),_=ce(h.bottom,0);v?R=y-2*(L!==0||$!==0?L+$:ce(h.left,h.right)):C=f-2*(U!==0||_!==0?U+_:ce(h.top,h.bottom))}await u({...t,availableWidth:R,availableHeight:C});const I=await c.getDimensions(i.floating);return y!==I.width||f!==I.height?{reset:{rects:!0}}:{}}}};function yt(){return typeof window<"u"}function Ye(e){return er(e)?(e.nodeName||"").toLowerCase():"#document"}function le(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Ne(e){var t;return(t=(er(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function er(e){return yt()?e instanceof Node||e instanceof le(e).Node:!1}function ge(e){return yt()?e instanceof Element||e instanceof le(e).Element:!1}function Se(e){return yt()?e instanceof HTMLElement||e instanceof le(e).HTMLElement:!1}function kn(e){return!yt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof le(e).ShadowRoot}const Ps=new Set(["inline","contents"]);function tt(e){const{overflow:t,overflowX:n,overflowY:r,display:s}=ve(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!Ps.has(s)}const js=new Set(["table","td","th"]);function Es(e){return js.has(Ye(e))}const Os=[":popover-open",":modal"];function St(e){return Os.some(t=>{try{return e.matches(t)}catch{return!1}})}const ks=["transform","translate","scale","rotate","perspective"],As=["transform","translate","scale","rotate","perspective","filter"],Rs=["paint","layout","strict","content"];function tn(e){const t=nn(),n=ge(e)?ve(e):e;return ks.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||As.some(r=>(n.willChange||"").includes(r))||Rs.some(r=>(n.contain||"").includes(r))}function Ts(e){let t=Ae(e);for(;Se(t)&&!Ke(t);){if(tn(t))return t;if(St(t))return null;t=Ae(t)}return null}function nn(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const Is=new Set(["html","body","#document"]);function Ke(e){return Is.has(Ye(e))}function ve(e){return le(e).getComputedStyle(e)}function Nt(e){return ge(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Ae(e){if(Ye(e)==="html")return e;const t=e.assignedSlot||e.parentNode||kn(e)&&e.host||Ne(e);return kn(t)?t.host:t}function tr(e){const t=Ae(e);return Ke(t)?e.ownerDocument?e.ownerDocument.body:e.body:Se(t)&&tt(t)?t:tr(t)}function Qe(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=tr(e),a=s===((r=e.ownerDocument)==null?void 0:r.body),c=le(s);if(a){const i=Vt(c);return t.concat(c,c.visualViewport||[],tt(s)?s:[],i&&n?Qe(i):[])}return t.concat(s,Qe(s,[],n))}function Vt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function nr(e){const t=ve(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const s=Se(e),a=s?e.offsetWidth:n,c=s?e.offsetHeight:r,i=mt(n)!==a||mt(r)!==c;return i&&(n=a,r=c),{width:n,height:r,$:i}}function rn(e){return ge(e)?e:e.contextElement}function Ue(e){const t=rn(e);if(!Se(t))return ye(1);const n=t.getBoundingClientRect(),{width:r,height:s,$:a}=nr(t);let c=(a?mt(n.width):n.width)/r,i=(a?mt(n.height):n.height)/s;return(!c||!Number.isFinite(c))&&(c=1),(!i||!Number.isFinite(i))&&(i=1),{x:c,y:i}}const _s=ye(0);function rr(e){const t=le(e);return!nn()||!t.visualViewport?_s:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Ms(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==le(e)?!1:t}function Le(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),a=rn(e);let c=ye(1);t&&(r?ge(r)&&(c=Ue(r)):c=Ue(e));const i=Ms(a,n,r)?rr(a):ye(0);let u=(s.left+i.x)/c.x,l=(s.top+i.y)/c.y,h=s.width/c.x,m=s.height/c.y;if(a){const p=le(a),v=r&&ge(r)?le(r):r;let y=p,f=Vt(y);for(;f&&r&&v!==y;){const b=Ue(f),x=f.getBoundingClientRect(),w=ve(f),S=x.left+(f.clientLeft+parseFloat(w.paddingLeft))*b.x,N=x.top+(f.clientTop+parseFloat(w.paddingTop))*b.y;u*=b.x,l*=b.y,h*=b.x,m*=b.y,u+=S,l+=N,y=le(f),f=Vt(y)}}return xt({width:h,height:m,x:u,y:l})}function on(e,t){const n=Nt(e).scrollLeft;return t?t.left+n:Le(Ne(e)).left+n}function or(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),s=r.left+t.scrollLeft-(n?0:on(e,r)),a=r.top+t.scrollTop;return{x:s,y:a}}function Ds(e){let{elements:t,rect:n,offsetParent:r,strategy:s}=e;const a=s==="fixed",c=Ne(r),i=t?St(t.floating):!1;if(r===c||i&&a)return n;let u={scrollLeft:0,scrollTop:0},l=ye(1);const h=ye(0),m=Se(r);if((m||!m&&!a)&&((Ye(r)!=="body"||tt(c))&&(u=Nt(r)),Se(r))){const v=Le(r);l=Ue(r),h.x=v.x+r.clientLeft,h.y=v.y+r.clientTop}const p=c&&!m&&!a?or(c,u,!0):ye(0);return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-u.scrollLeft*l.x+h.x+p.x,y:n.y*l.y-u.scrollTop*l.y+h.y+p.y}}function Ls(e){return Array.from(e.getClientRects())}function zs(e){const t=Ne(e),n=Nt(e),r=e.ownerDocument.body,s=ce(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=ce(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let c=-n.scrollLeft+on(e);const i=-n.scrollTop;return ve(r).direction==="rtl"&&(c+=ce(t.clientWidth,r.clientWidth)-s),{width:s,height:a,x:c,y:i}}function $s(e,t){const n=le(e),r=Ne(e),s=n.visualViewport;let a=r.clientWidth,c=r.clientHeight,i=0,u=0;if(s){a=s.width,c=s.height;const l=nn();(!l||l&&t==="fixed")&&(i=s.offsetLeft,u=s.offsetTop)}return{width:a,height:c,x:i,y:u}}const Fs=new Set(["absolute","fixed"]);function Bs(e,t){const n=Le(e,!0,t==="fixed"),r=n.top+e.clientTop,s=n.left+e.clientLeft,a=Se(e)?Ue(e):ye(1),c=e.clientWidth*a.x,i=e.clientHeight*a.y,u=s*a.x,l=r*a.y;return{width:c,height:i,x:u,y:l}}function An(e,t,n){let r;if(t==="viewport")r=$s(e,n);else if(t==="document")r=zs(Ne(e));else if(ge(t))r=Bs(t,n);else{const s=rr(e);r={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return xt(r)}function sr(e,t){const n=Ae(e);return n===t||!ge(n)||Ke(n)?!1:ve(n).position==="fixed"||sr(n,t)}function Ws(e,t){const n=t.get(e);if(n)return n;let r=Qe(e,[],!1).filter(i=>ge(i)&&Ye(i)!=="body"),s=null;const a=ve(e).position==="fixed";let c=a?Ae(e):e;for(;ge(c)&&!Ke(c);){const i=ve(c),u=tn(c);!u&&i.position==="fixed"&&(s=null),(a?!u&&!s:!u&&i.position==="static"&&!!s&&Fs.has(s.position)||tt(c)&&!u&&sr(e,c))?r=r.filter(h=>h!==c):s=i,c=Ae(c)}return t.set(e,r),r}function Vs(e){let{element:t,boundary:n,rootBoundary:r,strategy:s}=e;const c=[...n==="clippingAncestors"?St(t)?[]:Ws(t,this._c):[].concat(n),r],i=c[0],u=c.reduce((l,h)=>{const m=An(t,h,s);return l.top=ce(m.top,l.top),l.right=ke(m.right,l.right),l.bottom=ke(m.bottom,l.bottom),l.left=ce(m.left,l.left),l},An(t,i,s));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function Us(e){const{width:t,height:n}=nr(e);return{width:t,height:n}}function Hs(e,t,n){const r=Se(t),s=Ne(t),a=n==="fixed",c=Le(e,!0,a,t);let i={scrollLeft:0,scrollTop:0};const u=ye(0);function l(){u.x=on(s)}if(r||!r&&!a)if((Ye(t)!=="body"||tt(s))&&(i=Nt(t)),r){const v=Le(t,!0,a,t);u.x=v.x+t.clientLeft,u.y=v.y+t.clientTop}else s&&l();a&&!r&&s&&l();const h=s&&!r&&!a?or(s,i):ye(0),m=c.left+i.scrollLeft-u.x-h.x,p=c.top+i.scrollTop-u.y-h.y;return{x:m,y:p,width:c.width,height:c.height}}function It(e){return ve(e).position==="static"}function Rn(e,t){if(!Se(e)||ve(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Ne(e)===n&&(n=n.ownerDocument.body),n}function ir(e,t){const n=le(e);if(St(e))return n;if(!Se(e)){let s=Ae(e);for(;s&&!Ke(s);){if(ge(s)&&!It(s))return s;s=Ae(s)}return n}let r=Rn(e,t);for(;r&&Es(r)&&It(r);)r=Rn(r,t);return r&&Ke(r)&&It(r)&&!tn(r)?n:r||Ts(e)||n}const Ks=async function(e){const t=this.getOffsetParent||ir,n=this.getDimensions,r=await n(e.floating);return{reference:Hs(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function qs(e){return ve(e).direction==="rtl"}const Ys={convertOffsetParentRelativeRectToViewportRelativeRect:Ds,getDocumentElement:Ne,getClippingRect:Vs,getOffsetParent:ir,getElementRects:Ks,getClientRects:Ls,getDimensions:Us,getScale:Ue,isElement:ge,isRTL:qs};function ar(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Xs(e,t){let n=null,r;const s=Ne(e);function a(){var i;clearTimeout(r),(i=n)==null||i.disconnect(),n=null}function c(i,u){i===void 0&&(i=!1),u===void 0&&(u=1),a();const l=e.getBoundingClientRect(),{left:h,top:m,width:p,height:v}=l;if(i||t(),!p||!v)return;const y=ot(m),f=ot(s.clientWidth-(h+p)),b=ot(s.clientHeight-(m+v)),x=ot(h),S={rootMargin:-y+"px "+-f+"px "+-b+"px "+-x+"px",threshold:ce(0,ke(1,u))||1};let N=!0;function j(O){const C=O[0].intersectionRatio;if(C!==u){if(!N)return c();C?c(!1,C):r=setTimeout(()=>{c(!1,1e-7)},1e3)}C===1&&!ar(l,e.getBoundingClientRect())&&c(),N=!1}try{n=new IntersectionObserver(j,{...S,root:s.ownerDocument})}catch{n=new IntersectionObserver(j,S)}n.observe(e)}return c(!0),a}function Gs(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:s=!0,ancestorResize:a=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:i=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,l=rn(e),h=s||a?[...l?Qe(l):[],...Qe(t)]:[];h.forEach(x=>{s&&x.addEventListener("scroll",n,{passive:!0}),a&&x.addEventListener("resize",n)});const m=l&&i?Xs(l,n):null;let p=-1,v=null;c&&(v=new ResizeObserver(x=>{let[w]=x;w&&w.target===l&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var S;(S=v)==null||S.observe(t)})),n()}),l&&!u&&v.observe(l),v.observe(t));let y,f=u?Le(e):null;u&&b();function b(){const x=Le(e);f&&!ar(f,x)&&n(),f=x,y=requestAnimationFrame(b)}return n(),()=>{var x;h.forEach(w=>{s&&w.removeEventListener("scroll",n),a&&w.removeEventListener("resize",n)}),m==null||m(),(x=v)==null||x.disconnect(),v=null,u&&cancelAnimationFrame(y)}}const Zs=ys,Js=Ss,Qs=vs,ei=Cs,ti=ws,Tn=gs,ni=Ns,ri=(e,t,n)=>{const r=new Map,s={platform:Ys,...n},a={...s.platform,_c:r};return xs(e,t,{...s,platform:a})};var oi=typeof document<"u",si=function(){},dt=oi?d.useLayoutEffect:si;function gt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,s;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!gt(e[r],t[r]))return!1;return!0}if(s=Object.keys(e),n=s.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,s[r]))return!1;for(r=n;r--!==0;){const a=s[r];if(!(a==="_owner"&&e.$$typeof)&&!gt(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function cr(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function In(e,t){const n=cr(e);return Math.round(t*n)/n}function _t(e){const t=d.useRef(e);return dt(()=>{t.current=e}),t}function ii(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:s,elements:{reference:a,floating:c}={},transform:i=!0,whileElementsMounted:u,open:l}=e,[h,m]=d.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,v]=d.useState(r);gt(p,r)||v(r);const[y,f]=d.useState(null),[b,x]=d.useState(null),w=d.useCallback(T=>{T!==O.current&&(O.current=T,f(T))},[]),S=d.useCallback(T=>{T!==C.current&&(C.current=T,x(T))},[]),N=a||y,j=c||b,O=d.useRef(null),C=d.useRef(null),R=d.useRef(h),I=u!=null,L=_t(u),$=_t(s),U=_t(l),_=d.useCallback(()=>{if(!O.current||!C.current)return;const T={placement:t,strategy:n,middleware:p};$.current&&(T.platform=$.current),ri(O.current,C.current,T).then(q=>{const ne={...q,isPositioned:U.current!==!1};z.current&&!gt(R.current,ne)&&(R.current=ne,bt.flushSync(()=>{m(ne)}))})},[p,t,n,$,U]);dt(()=>{l===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,m(T=>({...T,isPositioned:!1})))},[l]);const z=d.useRef(!1);dt(()=>(z.current=!0,()=>{z.current=!1}),[]),dt(()=>{if(N&&(O.current=N),j&&(C.current=j),N&&j){if(L.current)return L.current(N,j,_);_()}},[N,j,_,L,I]);const F=d.useMemo(()=>({reference:O,floating:C,setReference:w,setFloating:S}),[w,S]),M=d.useMemo(()=>({reference:N,floating:j}),[N,j]),D=d.useMemo(()=>{const T={position:n,left:0,top:0};if(!M.floating)return T;const q=In(M.floating,h.x),ne=In(M.floating,h.y);return i?{...T,transform:"translate("+q+"px, "+ne+"px)",...cr(M.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:q,top:ne}},[n,i,M.floating,h.x,h.y]);return d.useMemo(()=>({...h,update:_,refs:F,elements:M,floatingStyles:D}),[h,_,F,M,D])}const ai=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:s}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Tn({element:r.current,padding:s}).fn(n):{}:r?Tn({element:r,padding:s}).fn(n):{}}}},ci=(e,t)=>({...Zs(e),options:[e,t]}),li=(e,t)=>({...Js(e),options:[e,t]}),di=(e,t)=>({...ni(e),options:[e,t]}),ui=(e,t)=>({...Qs(e),options:[e,t]}),fi=(e,t)=>({...ei(e),options:[e,t]}),hi=(e,t)=>({...ti(e),options:[e,t]}),mi=(e,t)=>({...ai(e),options:[e,t]});var pi="Arrow",lr=d.forwardRef((e,t)=>{const{children:n,width:r=10,height:s=5,...a}=e;return o.jsx(Y.svg,{...a,ref:t,width:r,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:o.jsx("polygon",{points:"0,0 30,0 15,10"})})});lr.displayName=pi;var xi=lr;function dr(e){const[t,n]=d.useState(void 0);return se(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const a=s[0];let c,i;if("borderBoxSize"in a){const u=a.borderBoxSize,l=Array.isArray(u)?u[0]:u;c=l.inlineSize,i=l.blockSize}else c=e.offsetWidth,i=e.offsetHeight;n({width:c,height:i})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var sn="Popper",[ur,fr]=et(sn),[gi,hr]=ur(sn),mr=e=>{const{__scopePopper:t,children:n}=e,[r,s]=d.useState(null);return o.jsx(gi,{scope:t,anchor:r,onAnchorChange:s,children:n})};mr.displayName=sn;var pr="PopperAnchor",xr=d.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...s}=e,a=hr(pr,n),c=d.useRef(null),i=ee(t,c);return d.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||c.current)}),r?null:o.jsx(Y.div,{...s,ref:i})});xr.displayName=pr;var an="PopperContent",[vi,wi]=ur(an),gr=d.forwardRef((e,t)=>{var k,H,te,K,B,W;const{__scopePopper:n,side:r="bottom",sideOffset:s=0,align:a="center",alignOffset:c=0,arrowPadding:i=0,avoidCollisions:u=!0,collisionBoundary:l=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:p=!1,updatePositionStrategy:v="optimized",onPlaced:y,...f}=e,b=hr(an,n),[x,w]=d.useState(null),S=ee(t,ie=>w(ie)),[N,j]=d.useState(null),O=dr(N),C=(O==null?void 0:O.width)??0,R=(O==null?void 0:O.height)??0,I=r+(a!=="center"?"-"+a:""),L=typeof h=="number"?h:{top:0,right:0,bottom:0,left:0,...h},$=Array.isArray(l)?l:[l],U=$.length>0,_={padding:L,boundary:$.filter(yi),altBoundary:U},{refs:z,floatingStyles:F,placement:M,isPositioned:D,middlewareData:T}=ii({strategy:"fixed",placement:I,whileElementsMounted:(...ie)=>Gs(...ie,{animationFrame:v==="always"}),elements:{reference:b.anchor},middleware:[ci({mainAxis:s+R,alignmentAxis:c}),u&&li({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?di():void 0,..._}),u&&ui({..._}),fi({..._,apply:({elements:ie,rects:de,availableWidth:Ie,availableHeight:Q})=>{const{width:ae,height:nt}=de.reference,_e=ie.floating.style;_e.setProperty("--radix-popper-available-width",`${Ie}px`),_e.setProperty("--radix-popper-available-height",`${Q}px`),_e.setProperty("--radix-popper-anchor-width",`${ae}px`),_e.setProperty("--radix-popper-anchor-height",`${nt}px`)}}),N&&mi({element:N,padding:i}),Si({arrowWidth:C,arrowHeight:R}),p&&hi({strategy:"referenceHidden",..._})]}),[q,ne]=br(M),he=De(y);se(()=>{D&&(he==null||he())},[D,he]);const me=(k=T.arrow)==null?void 0:k.x,X=(H=T.arrow)==null?void 0:H.y,G=((te=T.arrow)==null?void 0:te.centerOffset)!==0,[re,pe]=d.useState();return se(()=>{x&&pe(window.getComputedStyle(x).zIndex)},[x]),o.jsx("div",{ref:z.setFloating,"data-radix-popper-content-wrapper":"",style:{...F,transform:D?F.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:re,"--radix-popper-transform-origin":[(K=T.transformOrigin)==null?void 0:K.x,(B=T.transformOrigin)==null?void 0:B.y].join(" "),...((W=T.hide)==null?void 0:W.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:o.jsx(vi,{scope:n,placedSide:q,onArrowChange:j,arrowX:me,arrowY:X,shouldHideArrow:G,children:o.jsx(Y.div,{"data-side":q,"data-align":ne,...f,ref:S,style:{...f.style,animation:D?void 0:"none"}})})})});gr.displayName=an;var vr="PopperArrow",bi={top:"bottom",right:"left",bottom:"top",left:"right"},wr=d.forwardRef(function(t,n){const{__scopePopper:r,...s}=t,a=wi(vr,r),c=bi[a.placedSide];return o.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:o.jsx(xi,{...s,ref:n,style:{...s.style,display:"block"}})})});wr.displayName=vr;function yi(e){return e!==null}var Si=e=>({name:"transformOrigin",options:e,fn(t){var b,x,w;const{placement:n,rects:r,middlewareData:s}=t,c=((b=s.arrow)==null?void 0:b.centerOffset)!==0,i=c?0:e.arrowWidth,u=c?0:e.arrowHeight,[l,h]=br(n),m={start:"0%",center:"50%",end:"100%"}[h],p=(((x=s.arrow)==null?void 0:x.x)??0)+i/2,v=(((w=s.arrow)==null?void 0:w.y)??0)+u/2;let y="",f="";return l==="bottom"?(y=c?m:`${p}px`,f=`${-u}px`):l==="top"?(y=c?m:`${p}px`,f=`${r.floating.height+u}px`):l==="right"?(y=`${-u}px`,f=c?m:`${v}px`):l==="left"&&(y=`${r.floating.width+u}px`,f=c?m:`${v}px`),{data:{x:y,y:f}}}});function br(e){const[t,n="center"]=e.split("-");return[t,n]}var Ni=mr,Ci=xr,Pi=gr,ji=wr,Ei="Portal",yr=d.forwardRef((e,t)=>{var i;const{container:n,...r}=e,[s,a]=d.useState(!1);se(()=>a(!0),[]);const c=n||s&&((i=globalThis==null?void 0:globalThis.document)==null?void 0:i.body);return c?So.createPortal(o.jsx(Y.div,{...r,ref:t}),c):null});yr.displayName=Ei;var Oi=Un[" useInsertionEffect ".trim().toString()]||se;function Ut({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){const[s,a,c]=ki({defaultProp:t,onChange:n}),i=e!==void 0,u=i?e:s;{const h=d.useRef(e!==void 0);d.useEffect(()=>{const m=h.current;m!==i&&console.warn(`${r} is changing from ${m?"controlled":"uncontrolled"} to ${i?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),h.current=i},[i,r])}const l=d.useCallback(h=>{var m;if(i){const p=Ai(h)?h(e):h;p!==e&&((m=c.current)==null||m.call(c,p))}else a(h)},[i,e,a,c]);return[u,l]}function ki({defaultProp:e,onChange:t}){const[n,r]=d.useState(e),s=d.useRef(n),a=d.useRef(t);return Oi(()=>{a.current=t},[t]),d.useEffect(()=>{var c;s.current!==n&&((c=a.current)==null||c.call(a,n),s.current=n)},[n,s]),[n,r,a]}function Ai(e){return typeof e=="function"}function Sr(e){const t=d.useRef({value:e,previous:e});return d.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var Nr=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),Ri="VisuallyHidden",Ti=d.forwardRef((e,t)=>o.jsx(Y.span,{...e,ref:t,style:{...Nr,...e.style}}));Ti.displayName=Ri;var Ii=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Be=new WeakMap,st=new WeakMap,it={},Mt=0,Cr=function(e){return e&&(e.host||Cr(e.parentNode))},_i=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Cr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Mi=function(e,t,n,r){var s=_i(t,Array.isArray(e)?e:[e]);it[n]||(it[n]=new WeakMap);var a=it[n],c=[],i=new Set,u=new Set(s),l=function(m){!m||i.has(m)||(i.add(m),l(m.parentNode))};s.forEach(l);var h=function(m){!m||u.has(m)||Array.prototype.forEach.call(m.children,function(p){if(i.has(p))h(p);else try{var v=p.getAttribute(r),y=v!==null&&v!=="false",f=(Be.get(p)||0)+1,b=(a.get(p)||0)+1;Be.set(p,f),a.set(p,b),c.push(p),f===1&&y&&st.set(p,!0),b===1&&p.setAttribute(n,"true"),y||p.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",p,x)}})};return h(t),i.clear(),Mt++,function(){c.forEach(function(m){var p=Be.get(m)-1,v=a.get(m)-1;Be.set(m,p),a.set(m,v),p||(st.has(m)||m.removeAttribute(r),st.delete(m)),v||m.removeAttribute(n)}),Mt--,Mt||(Be=new WeakMap,Be=new WeakMap,st=new WeakMap,it={})}},Di=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),s=Ii(e);return s?(r.push.apply(r,Array.from(s.querySelectorAll("[aria-live], script"))),Mi(r,s,n,"aria-hidden")):function(){return null}},we=function(){return we=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},we.apply(this,arguments)};function Pr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}function Li(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,a;r<s;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var ut="right-scroll-bar-position",ft="width-before-scroll-bar",zi="with-scroll-bars-hidden",$i="--removed-body-scroll-bar-size";function Dt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Fi(e,t){var n=d.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var s=n.value;s!==r&&(n.value=r,n.callback(r,s))}}}})[0];return n.callback=t,n.facade}var Bi=typeof window<"u"?d.useLayoutEffect:d.useEffect,_n=new WeakMap;function Wi(e,t){var n=Fi(null,function(r){return e.forEach(function(s){return Dt(s,r)})});return Bi(function(){var r=_n.get(n);if(r){var s=new Set(r),a=new Set(e),c=n.current;s.forEach(function(i){a.has(i)||Dt(i,null)}),a.forEach(function(i){s.has(i)||Dt(i,c)})}_n.set(n,e)},[e]),n}function Vi(e){return e}function Ui(e,t){t===void 0&&(t=Vi);var n=[],r=!1,s={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var c=t(a,r);return n.push(c),function(){n=n.filter(function(i){return i!==c})}},assignSyncMedium:function(a){for(r=!0;n.length;){var c=n;n=[],c.forEach(a)}n={push:function(i){return a(i)},filter:function(){return n}}},assignMedium:function(a){r=!0;var c=[];if(n.length){var i=n;n=[],i.forEach(a),c=n}var u=function(){var h=c;c=[],h.forEach(a)},l=function(){return Promise.resolve().then(u)};l(),n={push:function(h){c.push(h),l()},filter:function(h){return c=c.filter(h),n}}}};return s}function Hi(e){e===void 0&&(e={});var t=Ui(null);return t.options=we({async:!0,ssr:!1},e),t}var jr=function(e){var t=e.sideCar,n=Pr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return d.createElement(r,we({},n))};jr.isSideCarExport=!0;function Ki(e,t){return e.useMedium(t),jr}var Er=Hi(),Lt=function(){},Ct=d.forwardRef(function(e,t){var n=d.useRef(null),r=d.useState({onScrollCapture:Lt,onWheelCapture:Lt,onTouchMoveCapture:Lt}),s=r[0],a=r[1],c=e.forwardProps,i=e.children,u=e.className,l=e.removeScrollBar,h=e.enabled,m=e.shards,p=e.sideCar,v=e.noRelative,y=e.noIsolation,f=e.inert,b=e.allowPinchZoom,x=e.as,w=x===void 0?"div":x,S=e.gapMode,N=Pr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=p,O=Wi([n,t]),C=we(we({},N),s);return d.createElement(d.Fragment,null,h&&d.createElement(j,{sideCar:Er,removeScrollBar:l,shards:m,noRelative:v,noIsolation:y,inert:f,setCallbacks:a,allowPinchZoom:!!b,lockRef:n,gapMode:S}),c?d.cloneElement(d.Children.only(i),we(we({},C),{ref:O})):d.createElement(w,we({},C,{className:u,ref:O}),i))});Ct.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ct.classNames={fullWidth:ft,zeroRight:ut};var qi=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Yi(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=qi();return t&&e.setAttribute("nonce",t),e}function Xi(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Gi(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Zi=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Yi())&&(Xi(t,n),Gi(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Ji=function(){var e=Zi();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Or=function(){var e=Ji(),t=function(n){var r=n.styles,s=n.dynamic;return e(r,s),null};return t},Qi={left:0,top:0,right:0,gap:0},zt=function(e){return parseInt(e||"",10)||0},ea=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],s=t[e==="padding"?"paddingRight":"marginRight"];return[zt(n),zt(r),zt(s)]},ta=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Qi;var t=ea(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},na=Or(),He="data-scroll-locked",ra=function(e,t,n,r){var s=e.left,a=e.top,c=e.right,i=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(zi,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(i,"px ").concat(r,`;
  }
  body[`).concat(He,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(i,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ut,` {
    right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(ft,` {
    margin-right: `).concat(i,"px ").concat(r,`;
  }
  
  .`).concat(ut," .").concat(ut,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(ft," .").concat(ft,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(He,`] {
    `).concat($i,": ").concat(i,`px;
  }
`)},Mn=function(){var e=parseInt(document.body.getAttribute(He)||"0",10);return isFinite(e)?e:0},oa=function(){d.useEffect(function(){return document.body.setAttribute(He,(Mn()+1).toString()),function(){var e=Mn()-1;e<=0?document.body.removeAttribute(He):document.body.setAttribute(He,e.toString())}},[])},sa=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,s=r===void 0?"margin":r;oa();var a=d.useMemo(function(){return ta(s)},[s]);return d.createElement(na,{styles:ra(a,!t,s,n?"":"!important")})},Ht=!1;if(typeof window<"u")try{var at=Object.defineProperty({},"passive",{get:function(){return Ht=!0,!0}});window.addEventListener("test",at,at),window.removeEventListener("test",at,at)}catch{Ht=!1}var We=Ht?{passive:!1}:!1,ia=function(e){return e.tagName==="TEXTAREA"},kr=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!ia(e)&&n[t]==="visible")},aa=function(e){return kr(e,"overflowY")},ca=function(e){return kr(e,"overflowX")},Dn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var s=Ar(e,r);if(s){var a=Rr(e,r),c=a[1],i=a[2];if(c>i)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},la=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},da=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ar=function(e,t){return e==="v"?aa(t):ca(t)},Rr=function(e,t){return e==="v"?la(t):da(t)},ua=function(e,t){return e==="h"&&t==="rtl"?-1:1},fa=function(e,t,n,r,s){var a=ua(e,window.getComputedStyle(t).direction),c=a*r,i=n.target,u=t.contains(i),l=!1,h=c>0,m=0,p=0;do{if(!i)break;var v=Rr(e,i),y=v[0],f=v[1],b=v[2],x=f-b-a*y;(y||x)&&Ar(e,i)&&(m+=x,p+=y);var w=i.parentNode;i=w&&w.nodeType===Node.DOCUMENT_FRAGMENT_NODE?w.host:w}while(!u&&i!==document.body||u&&(t.contains(i)||t===i));return(h&&Math.abs(m)<1||!h&&Math.abs(p)<1)&&(l=!0),l},ct=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Ln=function(e){return[e.deltaX,e.deltaY]},zn=function(e){return e&&"current"in e?e.current:e},ha=function(e,t){return e[0]===t[0]&&e[1]===t[1]},ma=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},pa=0,Ve=[];function xa(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),s=d.useState(pa++)[0],a=d.useState(Or)[0],c=d.useRef(e);d.useEffect(function(){c.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(s));var f=Li([e.lockRef.current],(e.shards||[]).map(zn),!0).filter(Boolean);return f.forEach(function(b){return b.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),f.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(s))})}}},[e.inert,e.lockRef.current,e.shards]);var i=d.useCallback(function(f,b){if("touches"in f&&f.touches.length===2||f.type==="wheel"&&f.ctrlKey)return!c.current.allowPinchZoom;var x=ct(f),w=n.current,S="deltaX"in f?f.deltaX:w[0]-x[0],N="deltaY"in f?f.deltaY:w[1]-x[1],j,O=f.target,C=Math.abs(S)>Math.abs(N)?"h":"v";if("touches"in f&&C==="h"&&O.type==="range")return!1;var R=Dn(C,O);if(!R)return!0;if(R?j=C:(j=C==="v"?"h":"v",R=Dn(C,O)),!R)return!1;if(!r.current&&"changedTouches"in f&&(S||N)&&(r.current=j),!j)return!0;var I=r.current||j;return fa(I,b,f,I==="h"?S:N)},[]),u=d.useCallback(function(f){var b=f;if(!(!Ve.length||Ve[Ve.length-1]!==a)){var x="deltaY"in b?Ln(b):ct(b),w=t.current.filter(function(j){return j.name===b.type&&(j.target===b.target||b.target===j.shadowParent)&&ha(j.delta,x)})[0];if(w&&w.should){b.cancelable&&b.preventDefault();return}if(!w){var S=(c.current.shards||[]).map(zn).filter(Boolean).filter(function(j){return j.contains(b.target)}),N=S.length>0?i(b,S[0]):!c.current.noIsolation;N&&b.cancelable&&b.preventDefault()}}},[]),l=d.useCallback(function(f,b,x,w){var S={name:f,delta:b,target:x,should:w,shadowParent:ga(x)};t.current.push(S),setTimeout(function(){t.current=t.current.filter(function(N){return N!==S})},1)},[]),h=d.useCallback(function(f){n.current=ct(f),r.current=void 0},[]),m=d.useCallback(function(f){l(f.type,Ln(f),f.target,i(f,e.lockRef.current))},[]),p=d.useCallback(function(f){l(f.type,ct(f),f.target,i(f,e.lockRef.current))},[]);d.useEffect(function(){return Ve.push(a),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:p}),document.addEventListener("wheel",u,We),document.addEventListener("touchmove",u,We),document.addEventListener("touchstart",h,We),function(){Ve=Ve.filter(function(f){return f!==a}),document.removeEventListener("wheel",u,We),document.removeEventListener("touchmove",u,We),document.removeEventListener("touchstart",h,We)}},[]);var v=e.removeScrollBar,y=e.inert;return d.createElement(d.Fragment,null,y?d.createElement(a,{styles:ma(s)}):null,v?d.createElement(sa,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function ga(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const va=Ki(Er,xa);var Tr=d.forwardRef(function(e,t){return d.createElement(Ct,we({},e,{ref:t,sideCar:va}))});Tr.classNames=Ct.classNames;var wa=[" ","Enter","ArrowUp","ArrowDown"],ba=[" ","Enter"],ze="Select",[Pt,jt,ya]=Do(ze),[Xe,el]=et(ze,[ya,fr]),Et=fr(),[Sa,Re]=Xe(ze),[Na,Ca]=Xe(ze),Ir=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:s,onOpenChange:a,value:c,defaultValue:i,onValueChange:u,dir:l,name:h,autoComplete:m,disabled:p,required:v,form:y}=e,f=Et(t),[b,x]=d.useState(null),[w,S]=d.useState(null),[N,j]=d.useState(!1),O=zo(l),[C,R]=Ut({prop:r,defaultProp:s??!1,onChange:a,caller:ze}),[I,L]=Ut({prop:c,defaultProp:i,onChange:u,caller:ze}),$=d.useRef(null),U=b?y||!!b.closest("form"):!0,[_,z]=d.useState(new Set),F=Array.from(_).map(M=>M.props.value).join(";");return o.jsx(Ni,{...f,children:o.jsxs(Sa,{required:v,scope:t,trigger:b,onTriggerChange:x,valueNode:w,onValueNodeChange:S,valueNodeHasChildren:N,onValueNodeHasChildrenChange:j,contentId:Zt(),value:I,onValueChange:L,open:C,onOpenChange:R,dir:O,triggerPointerDownPosRef:$,disabled:p,children:[o.jsx(Pt.Provider,{scope:t,children:o.jsx(Na,{scope:e.__scopeSelect,onNativeOptionAdd:d.useCallback(M=>{z(D=>new Set(D).add(M))},[]),onNativeOptionRemove:d.useCallback(M=>{z(D=>{const T=new Set(D);return T.delete(M),T})},[]),children:n})}),U?o.jsxs(to,{"aria-hidden":!0,required:v,tabIndex:-1,name:h,autoComplete:m,value:I,onChange:M=>L(M.target.value),disabled:p,form:y,children:[I===void 0?o.jsx("option",{value:""}):null,Array.from(_)]},F):null]})})};Ir.displayName=ze;var _r="SelectTrigger",Mr=d.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...s}=e,a=Et(n),c=Re(_r,n),i=c.disabled||r,u=ee(t,c.onTriggerChange),l=jt(n),h=d.useRef("touch"),[m,p,v]=ro(f=>{const b=l().filter(S=>!S.disabled),x=b.find(S=>S.value===c.value),w=oo(b,f,x);w!==void 0&&c.onValueChange(w.value)}),y=f=>{i||(c.onOpenChange(!0),v()),f&&(c.triggerPointerDownPosRef.current={x:Math.round(f.pageX),y:Math.round(f.pageY)})};return o.jsx(Ci,{asChild:!0,...a,children:o.jsx(Y.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:i,"data-disabled":i?"":void 0,"data-placeholder":no(c.value)?"":void 0,...s,ref:u,onClick:J(s.onClick,f=>{f.currentTarget.focus(),h.current!=="mouse"&&y(f)}),onPointerDown:J(s.onPointerDown,f=>{h.current=f.pointerType;const b=f.target;b.hasPointerCapture(f.pointerId)&&b.releasePointerCapture(f.pointerId),f.button===0&&f.ctrlKey===!1&&f.pointerType==="mouse"&&(y(f),f.preventDefault())}),onKeyDown:J(s.onKeyDown,f=>{const b=m.current!=="";!(f.ctrlKey||f.altKey||f.metaKey)&&f.key.length===1&&p(f.key),!(b&&f.key===" ")&&wa.includes(f.key)&&(y(),f.preventDefault())})})})});Mr.displayName=_r;var Dr="SelectValue",Lr=d.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,children:a,placeholder:c="",...i}=e,u=Re(Dr,n),{onValueNodeHasChildrenChange:l}=u,h=a!==void 0,m=ee(t,u.onValueNodeChange);return se(()=>{l(h)},[l,h]),o.jsx(Y.span,{...i,ref:m,style:{pointerEvents:"none"},children:no(u.value)?o.jsx(o.Fragment,{children:c}):a})});Lr.displayName=Dr;var Pa="SelectIcon",zr=d.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...s}=e;return o.jsx(Y.span,{"aria-hidden":!0,...s,ref:t,children:r||"▼"})});zr.displayName=Pa;var ja="SelectPortal",$r=e=>o.jsx(yr,{asChild:!0,...e});$r.displayName=ja;var $e="SelectContent",Fr=d.forwardRef((e,t)=>{const n=Re($e,e.__scopeSelect),[r,s]=d.useState();if(se(()=>{s(new DocumentFragment)},[]),!n.open){const a=r;return a?bt.createPortal(o.jsx(Br,{scope:e.__scopeSelect,children:o.jsx(Pt.Slot,{scope:e.__scopeSelect,children:o.jsx("div",{children:e.children})})}),a):null}return o.jsx(Wr,{...e,ref:t})});Fr.displayName=$e;var xe=10,[Br,Te]=Xe($e),Ea="SelectContentImpl",Oa=ht("SelectContent.RemoveScroll"),Wr=d.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:a,onPointerDownOutside:c,side:i,sideOffset:u,align:l,alignOffset:h,arrowPadding:m,collisionBoundary:p,collisionPadding:v,sticky:y,hideWhenDetached:f,avoidCollisions:b,...x}=e,w=Re($e,n),[S,N]=d.useState(null),[j,O]=d.useState(null),C=ee(t,k=>N(k)),[R,I]=d.useState(null),[L,$]=d.useState(null),U=jt(n),[_,z]=d.useState(!1),F=d.useRef(!1);d.useEffect(()=>{if(S)return Di(S)},[S]),Xo();const M=d.useCallback(k=>{const[H,...te]=U().map(W=>W.ref.current),[K]=te.slice(-1),B=document.activeElement;for(const W of k)if(W===B||(W==null||W.scrollIntoView({block:"nearest"}),W===H&&j&&(j.scrollTop=0),W===K&&j&&(j.scrollTop=j.scrollHeight),W==null||W.focus(),document.activeElement!==B))return},[U,j]),D=d.useCallback(()=>M([R,S]),[M,R,S]);d.useEffect(()=>{_&&D()},[_,D]);const{onOpenChange:T,triggerPointerDownPosRef:q}=w;d.useEffect(()=>{if(S){let k={x:0,y:0};const H=K=>{var B,W;k={x:Math.abs(Math.round(K.pageX)-(((B=q.current)==null?void 0:B.x)??0)),y:Math.abs(Math.round(K.pageY)-(((W=q.current)==null?void 0:W.y)??0))}},te=K=>{k.x<=10&&k.y<=10?K.preventDefault():S.contains(K.target)||T(!1),document.removeEventListener("pointermove",H),q.current=null};return q.current!==null&&(document.addEventListener("pointermove",H),document.addEventListener("pointerup",te,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",H),document.removeEventListener("pointerup",te,{capture:!0})}}},[S,T,q]),d.useEffect(()=>{const k=()=>T(!1);return window.addEventListener("blur",k),window.addEventListener("resize",k),()=>{window.removeEventListener("blur",k),window.removeEventListener("resize",k)}},[T]);const[ne,he]=ro(k=>{const H=U().filter(B=>!B.disabled),te=H.find(B=>B.ref.current===document.activeElement),K=oo(H,k,te);K&&setTimeout(()=>K.ref.current.focus())}),me=d.useCallback((k,H,te)=>{const K=!F.current&&!te;(w.value!==void 0&&w.value===H||K)&&(I(k),K&&(F.current=!0))},[w.value]),X=d.useCallback(()=>S==null?void 0:S.focus(),[S]),G=d.useCallback((k,H,te)=>{const K=!F.current&&!te;(w.value!==void 0&&w.value===H||K)&&$(k)},[w.value]),re=r==="popper"?Kt:Vr,pe=re===Kt?{side:i,sideOffset:u,align:l,alignOffset:h,arrowPadding:m,collisionBoundary:p,collisionPadding:v,sticky:y,hideWhenDetached:f,avoidCollisions:b}:{};return o.jsx(Br,{scope:n,content:S,viewport:j,onViewportChange:O,itemRefCallback:me,selectedItem:R,onItemLeave:X,itemTextRefCallback:G,focusSelectedItem:D,selectedItemText:L,position:r,isPositioned:_,searchRef:ne,children:o.jsx(Tr,{as:Oa,allowPinchZoom:!0,children:o.jsx(Gn,{asChild:!0,trapped:w.open,onMountAutoFocus:k=>{k.preventDefault()},onUnmountAutoFocus:J(s,k=>{var H;(H=w.trigger)==null||H.focus({preventScroll:!0}),k.preventDefault()}),children:o.jsx(Yn,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:c,onFocusOutside:k=>k.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:o.jsx(re,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:k=>k.preventDefault(),...x,...pe,onPlaced:()=>z(!0),ref:C,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:J(x.onKeyDown,k=>{const H=k.ctrlKey||k.altKey||k.metaKey;if(k.key==="Tab"&&k.preventDefault(),!H&&k.key.length===1&&he(k.key),["ArrowUp","ArrowDown","Home","End"].includes(k.key)){let K=U().filter(B=>!B.disabled).map(B=>B.ref.current);if(["ArrowUp","End"].includes(k.key)&&(K=K.slice().reverse()),["ArrowUp","ArrowDown"].includes(k.key)){const B=k.target,W=K.indexOf(B);K=K.slice(W+1)}setTimeout(()=>M(K)),k.preventDefault()}})})})})})})});Wr.displayName=Ea;var ka="SelectItemAlignedPosition",Vr=d.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...s}=e,a=Re($e,n),c=Te($e,n),[i,u]=d.useState(null),[l,h]=d.useState(null),m=ee(t,C=>h(C)),p=jt(n),v=d.useRef(!1),y=d.useRef(!0),{viewport:f,selectedItem:b,selectedItemText:x,focusSelectedItem:w}=c,S=d.useCallback(()=>{if(a.trigger&&a.valueNode&&i&&l&&f&&b&&x){const C=a.trigger.getBoundingClientRect(),R=l.getBoundingClientRect(),I=a.valueNode.getBoundingClientRect(),L=x.getBoundingClientRect();if(a.dir!=="rtl"){const B=L.left-R.left,W=I.left-B,ie=C.left-W,de=C.width+ie,Ie=Math.max(de,R.width),Q=window.innerWidth-xe,ae=xn(W,[xe,Math.max(xe,Q-Ie)]);i.style.minWidth=de+"px",i.style.left=ae+"px"}else{const B=R.right-L.right,W=window.innerWidth-I.right-B,ie=window.innerWidth-C.right-W,de=C.width+ie,Ie=Math.max(de,R.width),Q=window.innerWidth-xe,ae=xn(W,[xe,Math.max(xe,Q-Ie)]);i.style.minWidth=de+"px",i.style.right=ae+"px"}const $=p(),U=window.innerHeight-xe*2,_=f.scrollHeight,z=window.getComputedStyle(l),F=parseInt(z.borderTopWidth,10),M=parseInt(z.paddingTop,10),D=parseInt(z.borderBottomWidth,10),T=parseInt(z.paddingBottom,10),q=F+M+_+T+D,ne=Math.min(b.offsetHeight*5,q),he=window.getComputedStyle(f),me=parseInt(he.paddingTop,10),X=parseInt(he.paddingBottom,10),G=C.top+C.height/2-xe,re=U-G,pe=b.offsetHeight/2,k=b.offsetTop+pe,H=F+M+k,te=q-H;if(H<=G){const B=$.length>0&&b===$[$.length-1].ref.current;i.style.bottom="0px";const W=l.clientHeight-f.offsetTop-f.offsetHeight,ie=Math.max(re,pe+(B?X:0)+W+D),de=H+ie;i.style.height=de+"px"}else{const B=$.length>0&&b===$[0].ref.current;i.style.top="0px";const ie=Math.max(G,F+f.offsetTop+(B?me:0)+pe)+te;i.style.height=ie+"px",f.scrollTop=H-G+f.offsetTop}i.style.margin=`${xe}px 0`,i.style.minHeight=ne+"px",i.style.maxHeight=U+"px",r==null||r(),requestAnimationFrame(()=>v.current=!0)}},[p,a.trigger,a.valueNode,i,l,f,b,x,a.dir,r]);se(()=>S(),[S]);const[N,j]=d.useState();se(()=>{l&&j(window.getComputedStyle(l).zIndex)},[l]);const O=d.useCallback(C=>{C&&y.current===!0&&(S(),w==null||w(),y.current=!1)},[S,w]);return o.jsx(Ra,{scope:n,contentWrapper:i,shouldExpandOnScrollRef:v,onScrollButtonChange:O,children:o.jsx("div",{ref:u,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:o.jsx(Y.div,{...s,ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});Vr.displayName=ka;var Aa="SelectPopperPosition",Kt=d.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:s=xe,...a}=e,c=Et(n);return o.jsx(Pi,{...c,...a,ref:t,align:r,collisionPadding:s,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});Kt.displayName=Aa;var[Ra,cn]=Xe($e,{}),qt="SelectViewport",Ur=d.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...s}=e,a=Te(qt,n),c=cn(qt,n),i=ee(t,a.onViewportChange),u=d.useRef(0);return o.jsxs(o.Fragment,{children:[o.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),o.jsx(Pt.Slot,{scope:n,children:o.jsx(Y.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:i,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:J(s.onScroll,l=>{const h=l.currentTarget,{contentWrapper:m,shouldExpandOnScrollRef:p}=c;if(p!=null&&p.current&&m){const v=Math.abs(u.current-h.scrollTop);if(v>0){const y=window.innerHeight-xe*2,f=parseFloat(m.style.minHeight),b=parseFloat(m.style.height),x=Math.max(f,b);if(x<y){const w=x+v,S=Math.min(y,w),N=w-S;m.style.height=S+"px",m.style.bottom==="0px"&&(h.scrollTop=N>0?N:0,m.style.justifyContent="flex-end")}}}u.current=h.scrollTop})})})]})});Ur.displayName=qt;var Hr="SelectGroup",[Ta,Ia]=Xe(Hr),_a=d.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=Zt();return o.jsx(Ta,{scope:n,id:s,children:o.jsx(Y.div,{role:"group","aria-labelledby":s,...r,ref:t})})});_a.displayName=Hr;var Kr="SelectLabel",Ma=d.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=Ia(Kr,n);return o.jsx(Y.div,{id:s.id,...r,ref:t})});Ma.displayName=Kr;var vt="SelectItem",[Da,qr]=Xe(vt),Yr=d.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:s=!1,textValue:a,...c}=e,i=Re(vt,n),u=Te(vt,n),l=i.value===r,[h,m]=d.useState(a??""),[p,v]=d.useState(!1),y=ee(t,w=>{var S;return(S=u.itemRefCallback)==null?void 0:S.call(u,w,r,s)}),f=Zt(),b=d.useRef("touch"),x=()=>{s||(i.onValueChange(r),i.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return o.jsx(Da,{scope:n,value:r,disabled:s,textId:f,isSelected:l,onItemTextChange:d.useCallback(w=>{m(S=>S||((w==null?void 0:w.textContent)??"").trim())},[]),children:o.jsx(Pt.ItemSlot,{scope:n,value:r,disabled:s,textValue:h,children:o.jsx(Y.div,{role:"option","aria-labelledby":f,"data-highlighted":p?"":void 0,"aria-selected":l&&p,"data-state":l?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...c,ref:y,onFocus:J(c.onFocus,()=>v(!0)),onBlur:J(c.onBlur,()=>v(!1)),onClick:J(c.onClick,()=>{b.current!=="mouse"&&x()}),onPointerUp:J(c.onPointerUp,()=>{b.current==="mouse"&&x()}),onPointerDown:J(c.onPointerDown,w=>{b.current=w.pointerType}),onPointerMove:J(c.onPointerMove,w=>{var S;b.current=w.pointerType,s?(S=u.onItemLeave)==null||S.call(u):b.current==="mouse"&&w.currentTarget.focus({preventScroll:!0})}),onPointerLeave:J(c.onPointerLeave,w=>{var S;w.currentTarget===document.activeElement&&((S=u.onItemLeave)==null||S.call(u))}),onKeyDown:J(c.onKeyDown,w=>{var N;((N=u.searchRef)==null?void 0:N.current)!==""&&w.key===" "||(ba.includes(w.key)&&x(),w.key===" "&&w.preventDefault())})})})})});Yr.displayName=vt;var Ge="SelectItemText",Xr=d.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:s,...a}=e,c=Re(Ge,n),i=Te(Ge,n),u=qr(Ge,n),l=Ca(Ge,n),[h,m]=d.useState(null),p=ee(t,x=>m(x),u.onItemTextChange,x=>{var w;return(w=i.itemTextRefCallback)==null?void 0:w.call(i,x,u.value,u.disabled)}),v=h==null?void 0:h.textContent,y=d.useMemo(()=>o.jsx("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:f,onNativeOptionRemove:b}=l;return se(()=>(f(y),()=>b(y)),[f,b,y]),o.jsxs(o.Fragment,{children:[o.jsx(Y.span,{id:u.textId,...a,ref:p}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?bt.createPortal(a.children,c.valueNode):null]})});Xr.displayName=Ge;var Gr="SelectItemIndicator",Zr=d.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return qr(Gr,n).isSelected?o.jsx(Y.span,{"aria-hidden":!0,...r,ref:t}):null});Zr.displayName=Gr;var Yt="SelectScrollUpButton",Jr=d.forwardRef((e,t)=>{const n=Te(Yt,e.__scopeSelect),r=cn(Yt,e.__scopeSelect),[s,a]=d.useState(!1),c=ee(t,r.onScrollButtonChange);return se(()=>{if(n.viewport&&n.isPositioned){let i=function(){const l=u.scrollTop>0;a(l)};const u=n.viewport;return i(),u.addEventListener("scroll",i),()=>u.removeEventListener("scroll",i)}},[n.viewport,n.isPositioned]),s?o.jsx(eo,{...e,ref:c,onAutoScroll:()=>{const{viewport:i,selectedItem:u}=n;i&&u&&(i.scrollTop=i.scrollTop-u.offsetHeight)}}):null});Jr.displayName=Yt;var Xt="SelectScrollDownButton",Qr=d.forwardRef((e,t)=>{const n=Te(Xt,e.__scopeSelect),r=cn(Xt,e.__scopeSelect),[s,a]=d.useState(!1),c=ee(t,r.onScrollButtonChange);return se(()=>{if(n.viewport&&n.isPositioned){let i=function(){const l=u.scrollHeight-u.clientHeight,h=Math.ceil(u.scrollTop)<l;a(h)};const u=n.viewport;return i(),u.addEventListener("scroll",i),()=>u.removeEventListener("scroll",i)}},[n.viewport,n.isPositioned]),s?o.jsx(eo,{...e,ref:c,onAutoScroll:()=>{const{viewport:i,selectedItem:u}=n;i&&u&&(i.scrollTop=i.scrollTop+u.offsetHeight)}}):null});Qr.displayName=Xt;var eo=d.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...s}=e,a=Te("SelectScrollButton",n),c=d.useRef(null),i=jt(n),u=d.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return d.useEffect(()=>()=>u(),[u]),se(()=>{var h;const l=i().find(m=>m.ref.current===document.activeElement);(h=l==null?void 0:l.ref.current)==null||h.scrollIntoView({block:"nearest"})},[i]),o.jsx(Y.div,{"aria-hidden":!0,...s,ref:t,style:{flexShrink:0,...s.style},onPointerDown:J(s.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(r,50))}),onPointerMove:J(s.onPointerMove,()=>{var l;(l=a.onItemLeave)==null||l.call(a),c.current===null&&(c.current=window.setInterval(r,50))}),onPointerLeave:J(s.onPointerLeave,()=>{u()})})}),La="SelectSeparator",za=d.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return o.jsx(Y.div,{"aria-hidden":!0,...r,ref:t})});za.displayName=La;var Gt="SelectArrow",$a=d.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,s=Et(n),a=Re(Gt,n),c=Te(Gt,n);return a.open&&c.position==="popper"?o.jsx(ji,{...s,...r,ref:t}):null});$a.displayName=Gt;var Fa="SelectBubbleInput",to=d.forwardRef(({__scopeSelect:e,value:t,...n},r)=>{const s=d.useRef(null),a=ee(r,s),c=Sr(t);return d.useEffect(()=>{const i=s.current;if(!i)return;const u=window.HTMLSelectElement.prototype,h=Object.getOwnPropertyDescriptor(u,"value").set;if(c!==t&&h){const m=new Event("change",{bubbles:!0});h.call(i,t),i.dispatchEvent(m)}},[c,t]),o.jsx(Y.select,{...n,style:{...Nr,...n.style},ref:a,defaultValue:t})});to.displayName=Fa;function no(e){return e===""||e===void 0}function ro(e){const t=De(e),n=d.useRef(""),r=d.useRef(0),s=d.useCallback(c=>{const i=n.current+c;t(i),function u(l){n.current=l,window.clearTimeout(r.current),l!==""&&(r.current=window.setTimeout(()=>u(""),1e3))}(i)},[t]),a=d.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return d.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,s,a]}function oo(e,t,n){const s=t.length>1&&Array.from(t).every(l=>l===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let c=Ba(e,Math.max(a,0));s.length===1&&(c=c.filter(l=>l!==n));const u=c.find(l=>l.textValue.toLowerCase().startsWith(s.toLowerCase()));return u!==n?u:void 0}function Ba(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var Wa=Ir,Va=Mr,Ua=Lr,Ha=zr,Ka=$r,qa=Fr,Ya=Ur,Xa=Yr,Ga=Xr,Za=Zr,Ja=Jr,Qa=Qr;/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ec=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],so=Fe("chevron-down",ec);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tc=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],nc=Fe("chevron-up",tc);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rc=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],Ze=Fe("circle-check-big",rc);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oc=[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]],$n=Fe("credit-card",oc);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]],ic=Fe("shield",sc);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ac=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],cc=Fe("triangle-alert",ac);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lc=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],dc=Fe("x",lc);function uc({...e}){return o.jsx(Wa,{"data-slot":"select",...e})}function fc({...e}){return o.jsx(Ua,{"data-slot":"select-value",...e})}function hc({className:e,size:t="default",children:n,...r}){return o.jsxs(Va,{"data-slot":"select-trigger","data-size":t,className:oe("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[n,o.jsx(Ha,{asChild:!0,children:o.jsx(so,{className:"size-4 opacity-50"})})]})}function mc({className:e,children:t,position:n="popper",...r}){return o.jsx(Ka,{children:o.jsxs(qa,{"data-slot":"select-content",className:oe("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[o.jsx(pc,{}),o.jsx(Ya,{className:oe("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),o.jsx(xc,{})]})})}function Fn({className:e,children:t,...n}){return o.jsxs(Xa,{"data-slot":"select-item",className:oe("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...n,children:[o.jsx("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:o.jsx(Za,{children:o.jsx(Hn,{className:"size-4"})})}),o.jsx(Ga,{children:t})]})}function pc({className:e,...t}){return o.jsx(Ja,{"data-slot":"select-scroll-up-button",className:oe("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(nc,{className:"size-4"})})}function xc({className:e,...t}){return o.jsx(Qa,{"data-slot":"select-scroll-down-button",className:oe("flex cursor-default items-center justify-center py-1",e),...t,children:o.jsx(so,{className:"size-4"})})}var ln="Progress",dn=100,[gc,tl]=et(ln),[vc,wc]=gc(ln),io=d.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:s,getValueLabel:a=bc,...c}=e;(s||s===0)&&!Bn(s)&&console.error(yc(`${s}`,"Progress"));const i=Bn(s)?s:dn;r!==null&&!Wn(r,i)&&console.error(Sc(`${r}`,"Progress"));const u=Wn(r,i)?r:null,l=wt(u)?a(u,i):void 0;return o.jsx(vc,{scope:n,value:u,max:i,children:o.jsx(Y.div,{"aria-valuemax":i,"aria-valuemin":0,"aria-valuenow":wt(u)?u:void 0,"aria-valuetext":l,role:"progressbar","data-state":lo(u,i),"data-value":u??void 0,"data-max":i,...c,ref:t})})});io.displayName=ln;var ao="ProgressIndicator",co=d.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,s=wc(ao,n);return o.jsx(Y.div,{"data-state":lo(s.value,s.max),"data-value":s.value??void 0,"data-max":s.max,...r,ref:t})});co.displayName=ao;function bc(e,t){return`${Math.round(e/t*100)}%`}function lo(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function wt(e){return typeof e=="number"}function Bn(e){return wt(e)&&!isNaN(e)&&e>0}function Wn(e,t){return wt(e)&&!isNaN(e)&&e<=t&&e>=0}function yc(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${dn}\`.`}function Sc(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${dn} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var Nc=io,Cc=co;function Pc({className:e,value:t,...n}){return o.jsx(Nc,{"data-slot":"progress",className:oe("bg-gray-200 relative h-3 w-full overflow-hidden rounded-full shadow-inner",e),...n,children:o.jsx(Cc,{"data-slot":"progress-indicator",className:"bg-black h-full w-full flex-1 transition-all duration-500 ease-out rounded-full shadow-lg",style:{transform:`translateX(-${100-(t||0)}%)`}})})}var jc="Separator",Vn="horizontal",Ec=["horizontal","vertical"],uo=d.forwardRef((e,t)=>{const{decorative:n,orientation:r=Vn,...s}=e,a=Oc(r)?r:Vn,i=n?{role:"none"}:{"aria-orientation":a==="vertical"?a:void 0,role:"separator"};return o.jsx(Y.div,{"data-orientation":a,...i,...s,ref:t})});uo.displayName=jc;function Oc(e){return Ec.includes(e)}var kc=uo;function Ac({className:e,orientation:t="horizontal",decorative:n=!0,...r}){return o.jsx(kc,{"data-slot":"separator",decorative:n,orientation:t,className:oe("bg-gray-200 shrink-0 data-[orientation=horizontal]:h-[2px] data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-[2px] rounded-full",e),...r})}function Rc(e,t){return d.useReducer((n,r)=>t[n][r]??n,e)}var fo=e=>{const{present:t,children:n}=e,r=Tc(t),s=typeof n=="function"?n({present:r.isPresent}):d.Children.only(n),a=ee(r.ref,Ic(s));return typeof n=="function"||r.isPresent?d.cloneElement(s,{ref:a}):null};fo.displayName="Presence";function Tc(e){const[t,n]=d.useState(),r=d.useRef(null),s=d.useRef(e),a=d.useRef("none"),c=e?"mounted":"unmounted",[i,u]=Rc(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return d.useEffect(()=>{const l=lt(r.current);a.current=i==="mounted"?l:"none"},[i]),se(()=>{const l=r.current,h=s.current;if(h!==e){const p=a.current,v=lt(l);e?u("MOUNT"):v==="none"||(l==null?void 0:l.display)==="none"?u("UNMOUNT"):u(h&&p!==v?"ANIMATION_OUT":"UNMOUNT"),s.current=e}},[e,u]),se(()=>{if(t){let l;const h=t.ownerDocument.defaultView??window,m=v=>{const f=lt(r.current).includes(v.animationName);if(v.target===t&&f&&(u("ANIMATION_END"),!s.current)){const b=t.style.animationFillMode;t.style.animationFillMode="forwards",l=h.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=b)})}},p=v=>{v.target===t&&(a.current=lt(r.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",m),t.addEventListener("animationend",m),()=>{h.clearTimeout(l),t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",m),t.removeEventListener("animationend",m)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:d.useCallback(l=>{r.current=l?getComputedStyle(l):null,n(l)},[])}}function lt(e){return(e==null?void 0:e.animationName)||"none"}function Ic(e){var r,s;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(s=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:s.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var Ot="Checkbox",[_c,nl]=et(Ot),[Mc,un]=_c(Ot);function Dc(e){const{__scopeCheckbox:t,checked:n,children:r,defaultChecked:s,disabled:a,form:c,name:i,onCheckedChange:u,required:l,value:h="on",internal_do_not_use_render:m}=e,[p,v]=Ut({prop:n,defaultProp:s??!1,onChange:u,caller:Ot}),[y,f]=d.useState(null),[b,x]=d.useState(null),w=d.useRef(!1),S=y?!!c||!!y.closest("form"):!0,N={checked:p,disabled:a,setChecked:v,control:y,setControl:f,name:i,form:c,value:h,hasConsumerStoppedPropagationRef:w,required:l,defaultChecked:Oe(s)?!1:s,isFormControl:S,bubbleInput:b,setBubbleInput:x};return o.jsx(Mc,{scope:t,...N,children:Lc(m)?m(N):r})}var ho="CheckboxTrigger",mo=d.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:n,...r},s)=>{const{control:a,value:c,disabled:i,checked:u,required:l,setControl:h,setChecked:m,hasConsumerStoppedPropagationRef:p,isFormControl:v,bubbleInput:y}=un(ho,e),f=ee(s,h),b=d.useRef(u);return d.useEffect(()=>{const x=a==null?void 0:a.form;if(x){const w=()=>m(b.current);return x.addEventListener("reset",w),()=>x.removeEventListener("reset",w)}},[a,m]),o.jsx(Y.button,{type:"button",role:"checkbox","aria-checked":Oe(u)?"mixed":u,"aria-required":l,"data-state":bo(u),"data-disabled":i?"":void 0,disabled:i,value:c,...r,ref:f,onKeyDown:J(t,x=>{x.key==="Enter"&&x.preventDefault()}),onClick:J(n,x=>{m(w=>Oe(w)?!0:!w),y&&v&&(p.current=x.isPropagationStopped(),p.current||x.stopPropagation())})})});mo.displayName=ho;var po=d.forwardRef((e,t)=>{const{__scopeCheckbox:n,name:r,checked:s,defaultChecked:a,required:c,disabled:i,value:u,onCheckedChange:l,form:h,...m}=e;return o.jsx(Dc,{__scopeCheckbox:n,checked:s,defaultChecked:a,disabled:i,required:c,onCheckedChange:l,name:r,form:h,value:u,internal_do_not_use_render:({isFormControl:p})=>o.jsxs(o.Fragment,{children:[o.jsx(mo,{...m,ref:t,__scopeCheckbox:n}),p&&o.jsx(wo,{__scopeCheckbox:n})]})})});po.displayName=Ot;var xo="CheckboxIndicator",go=d.forwardRef((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...s}=e,a=un(xo,n);return o.jsx(fo,{present:r||Oe(a.checked)||a.checked===!0,children:o.jsx(Y.span,{"data-state":bo(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});go.displayName=xo;var vo="CheckboxBubbleInput",wo=d.forwardRef(({__scopeCheckbox:e,...t},n)=>{const{control:r,hasConsumerStoppedPropagationRef:s,checked:a,defaultChecked:c,required:i,disabled:u,name:l,value:h,form:m,bubbleInput:p,setBubbleInput:v}=un(vo,e),y=ee(n,v),f=Sr(a),b=dr(r);d.useEffect(()=>{const w=p;if(!w)return;const S=window.HTMLInputElement.prototype,j=Object.getOwnPropertyDescriptor(S,"checked").set,O=!s.current;if(f!==a&&j){const C=new Event("click",{bubbles:O});w.indeterminate=Oe(a),j.call(w,Oe(a)?!1:a),w.dispatchEvent(C)}},[p,f,a,s]);const x=d.useRef(Oe(a)?!1:a);return o.jsx(Y.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??x.current,required:i,disabled:u,name:l,value:h,form:m,...t,tabIndex:-1,ref:y,style:{...t.style,...b,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});wo.displayName=vo;function Lc(e){return typeof e=="function"}function Oe(e){return e==="indeterminate"}function bo(e){return Oe(e)?"indeterminate":e?"checked":"unchecked"}function zc({className:e,...t}){return o.jsx(po,{"data-slot":"checkbox",className:oe("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:o.jsx(go,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:o.jsx(Hn,{className:"size-3.5"})})})}function $c({totalProducts:e,selectedCount:t,averageSeoScore:n,onOptimizeSelected:r,onSelectAll:s,isOptimizing:a}){return o.jsx("div",{className:"bg-black text-white",children:o.jsx("div",{className:"bg-black py-20 px-6",children:o.jsxs("div",{className:"max-w-6xl mx-auto text-center",children:[o.jsxs("div",{className:"flex flex-col items-center mb-12",children:[o.jsx("img",{src:"/logo.png",alt:"AI BULK SEO Logo",className:"w-16 h-16 mb-6 rounded-2xl shadow-2xl",style:{filter:"brightness(1.1) contrast(1.1)"}}),o.jsx("div",{className:"text-sm font-semibold tracking-widest uppercase text-white/70 mb-4",children:"AI BULK SEO"}),o.jsx("h1",{style:{fontSize:"clamp(3rem, 8vw, 6rem)",fontWeight:900,lineHeight:.9,letterSpacing:"-0.05em",marginBottom:"1rem",color:"white"},children:"OPTIMIZE"}),o.jsx("p",{style:{fontSize:"clamp(1.25rem, 3vw, 1.75rem)",fontWeight:300,color:"#cbd5e1",maxWidth:"40rem",margin:"0 auto"},children:"AI-powered SEO optimization for your Shopify products"})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-4xl font-bold mb-2",children:e.toLocaleString()}),o.jsx("div",{className:"text-white/70",children:"Total Products"})]}),o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-4xl font-bold mb-2",children:t.toLocaleString()}),o.jsx("div",{className:"text-white/70",children:"Selected"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-4xl font-bold mb-2",children:[Math.round(n),"%"]}),o.jsx("div",{className:"text-white/70",children:"Avg SEO Score"})]})]}),o.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[o.jsx(fe,{onClick:r,disabled:t===0||a,className:"bg-white text-black hover:bg-gray-100 font-bold py-3 px-8 rounded-2xl",children:a?"Optimizing...":`Optimize ${t} Selected`}),o.jsx(fe,{onClick:s,className:"bg-transparent border-2 border-white/40 text-white hover:bg-white hover:text-black font-bold py-3 px-8 rounded-2xl",children:"Select All Products"})]})]})})})}function Fc({subscription:e,plan:t,hasAccess:n,monthlyUsage:r}){const s=()=>{if(!e)return o.jsx($n,{className:"w-5 h-5 text-gray-500"});switch(e.status){case"ACTIVE":return o.jsx(Ze,{className:"w-5 h-5 text-green-500"});case"PENDING":return o.jsx(cc,{className:"w-5 h-5 text-red-500"});default:return o.jsx($n,{className:"w-5 h-5 text-gray-500"})}},a=()=>{if(!e)return o.jsx(Ee,{variant:"secondary",children:"No Plan"});switch(e.status){case"ACTIVE":return o.jsx(Ee,{variant:"default",className:"bg-green-500",children:"Active"});case"PENDING":return o.jsx(Ee,{variant:"destructive",children:"Payment Required"});case"CANCELLED":return o.jsx(Ee,{variant:"secondary",children:"Cancelled"});default:return o.jsx(Ee,{variant:"secondary",children:e.status})}};return n?o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[s(),o.jsx("h3",{className:"text-lg font-bold text-white",children:(t==null?void 0:t.name)||"Current Plan"})]}),a()]}),o.jsx("p",{className:"text-white/70 mb-6",children:(t==null?void 0:t.description)||"Your billing plan and status"}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[o.jsxs("div",{children:[o.jsx("div",{className:"text-sm text-white/70",children:"Plan Type"}),o.jsx("div",{className:"font-semibold text-white",children:(t==null?void 0:t.type)==="pay_per_use"?"Pay-Per-Use":(t==null?void 0:t.type)==="annual"?"Annual Subscription":"Monthly Subscription"})]}),o.jsxs("div",{children:[o.jsx("div",{className:"text-sm text-white/70",children:"Cost"}),o.jsx("div",{className:"font-semibold text-white",children:(t==null?void 0:t.type)==="pay_per_use"?`$${t.price} per product`:`$${t==null?void 0:t.price}/${(t==null?void 0:t.type)==="annual"?"year":"month"}`})]})]}),r&&o.jsxs("div",{className:"grid grid-cols-2 gap-4 pt-2 border-t border-white/20",children:[o.jsxs("div",{className:"text-center",children:[o.jsx("div",{className:"text-2xl font-bold text-blue-400",children:r.productsOptimized}),o.jsx("div",{className:"text-sm text-white/70",children:"Products Optimized"})]}),o.jsxs("div",{className:"text-center",children:[o.jsxs("div",{className:"text-2xl font-bold text-green-400",children:["$",r.totalSpent.toFixed(2)]}),o.jsx("div",{className:"text-sm text-white/70",children:(t==null?void 0:t.type)==="pay_per_use"?"Total Spent":"Plan Value"})]})]}),o.jsxs("div",{className:"flex gap-2 pt-2",children:[o.jsx(fe,{asChild:!0,className:"flex-1 h-10 px-6 py-2 text-xs border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black",children:o.jsx(rt,{to:"/app/billing",children:"Manage Billing"})}),(t==null?void 0:t.type)!=="annual"&&o.jsx(fe,{asChild:!0,className:"h-10 px-6 py-2 text-xs bg-white text-black hover:bg-gray-100",children:o.jsx(rt,{to:"/app/billing/pricing",children:"Upgrade"})})]})]})]}):o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[o.jsxs("div",{className:"flex items-center justify-between mb-4",children:[o.jsxs("div",{className:"flex items-center gap-2",children:[o.jsx(No,{className:"w-5 h-5 text-white"}),o.jsx("h3",{className:"text-lg font-bold text-white",children:"Upgrade Required"})]}),a()]}),o.jsx("p",{className:"text-white/70 mb-6",children:"Choose a plan to start optimizing your products with AI-powered SEO tools."}),o.jsxs("div",{className:"space-y-4",children:[o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[o.jsxs("div",{className:"p-3 bg-white/10 border border-white/20 rounded-2xl",children:[o.jsx("div",{className:"text-lg font-bold text-white",children:"$199.99/year"}),o.jsx("div",{className:"text-sm text-white/70",children:"Annual Plan"}),o.jsx("div",{className:"text-xs text-green-400",children:"Best Value"})]}),o.jsxs("div",{className:"p-3 bg-white/10 border border-white/20 rounded-2xl",children:[o.jsx("div",{className:"text-lg font-bold text-white",children:"$19.99/month"}),o.jsx("div",{className:"text-sm text-white/70",children:"Monthly Plan"})]}),o.jsxs("div",{className:"p-3 bg-white/10 border border-white/20 rounded-2xl",children:[o.jsx("div",{className:"text-lg font-bold text-white",children:"$0.10"}),o.jsx("div",{className:"text-sm text-white/70",children:"Per Product"})]})]}),o.jsxs("div",{className:"flex gap-3",children:[o.jsx(fe,{asChild:!0,className:"flex-1 bg-white text-black hover:bg-gray-100",children:o.jsx(rt,{to:"/app/billing/pricing",children:"Choose Plan"})}),o.jsx(fe,{asChild:!0,className:"border-2 border-white/40 bg-transparent text-white hover:bg-white hover:text-black",children:o.jsx(rt,{to:"/app/billing",children:"View Details"})})]})]})]})}function Bc({productCount:e,selectedProducts:t,onConfirm:n,onCancel:r,isProcessing:s=!1,csrfToken:a}){const c=e*.1,i=$t();d.useEffect(()=>{if(console.log("🔄 PayPerUse fetcher state:",i.state),console.log("📋 PayPerUse fetcher data:",i.data),i.data&&typeof i.data=="object"){const l=i.data;if(l.error)console.error("❌ Pay-per-use billing error:",l.error),alert(`Payment Error: ${l.error}`);else if(l.success&&l.confirmationUrl){console.log("✅ Pay-per-use purchase created successfully, redirecting...");try{window.top?window.top.location.href=l.confirmationUrl:window.location.href=l.confirmationUrl}catch(h){console.error("❌ Failed to redirect to confirmation URL:",h),alert("Purchase created successfully, but failed to redirect. Please refresh the page.")}}}},[i.data,i.state]);const u=()=>{if(!e||e<=0){alert("Invalid product count");return}if(!t||t.length===0){alert("No products selected");return}if(t.length!==e){alert("Selected products count does not match product count");return}if(i.state==="submitting"||i.state==="loading"){console.log("⏳ Already processing purchase request...");return}console.log("🔄 Creating pay-per-use purchase:",{productCount:e,selectedProductsCount:t.length,totalCost:c});const l=t.map(m=>m.startsWith("gid://shopify/Product/")?m:`gid://shopify/Product/${m}`);console.log("🔄 Converting product IDs to GID format:",{original:t.slice(0,3),formatted:l.slice(0,3)});const h=new FormData;h.append("action","create_pay_per_use_purchase"),h.append("productCount",e.toString()),h.append("selectedProducts",JSON.stringify(l)),h.append("csrfToken",a),console.log("🔐 PayPerUseConfirmation - Submitting with CSRF token"),console.log("🔐 CSRF token length:",(a==null?void 0:a.length)||0),console.log("📋 Pay-per-use form data:",{action:"create_pay_per_use_purchase",productCount:e.toString(),selectedProducts:JSON.stringify(t),csrfTokenPresent:!!a}),i.submit(h,{method:"POST",action:"/app/billing/pay-per-use"})};return o.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4",children:o.jsxs(V.div,{initial:{opacity:0,scale:.95,y:10},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:10},transition:{duration:.2,ease:"easeOut"},className:"bg-black border border-white/20 rounded-3xl shadow-2xl w-full max-w-sm",children:[o.jsx("div",{className:"px-6 py-5 border-b border-white/20",children:o.jsxs("div",{className:"flex items-center justify-between",children:[o.jsxs("div",{className:"flex items-center space-x-3",children:[o.jsx("div",{className:"w-10 h-10 bg-white rounded-full flex items-center justify-center",children:o.jsx(Co,{className:"w-5 h-5 text-black"})}),o.jsxs("div",{children:[o.jsx("h3",{className:"text-lg font-semibold text-white",children:"Pay & Optimize"}),o.jsxs("p",{className:"text-sm text-white/70",children:[e," product",e>1?"s":""]})]})]}),o.jsx("button",{onClick:r,className:"w-8 h-8 rounded-full bg-white/10 hover:bg-white/20 flex items-center justify-center transition-colors",children:o.jsx(dc,{className:"w-4 h-4 text-white/70"})})]})}),o.jsxs("div",{className:"px-6 py-5 space-y-5",children:[o.jsxs("div",{className:"bg-black border border-white/20 rounded-2xl p-4",children:[o.jsxs("div",{className:"flex items-center justify-between mb-3",children:[o.jsx("span",{className:"text-sm font-medium text-white/70",children:"Total Cost"}),o.jsxs("div",{className:"text-right",children:[o.jsxs("div",{className:"text-2xl font-bold text-white",children:["$",c.toFixed(2)]}),o.jsxs("div",{className:"text-xs text-white/70",children:["$",e," × $0.10"]})]})]}),o.jsxs("div",{className:"flex items-center text-xs text-white/70",children:[o.jsx(Ze,{className:"w-3 h-3 mr-1"}),"Instant optimization • No subscription required"]})]}),o.jsxs("div",{className:"flex items-center space-x-3 p-3 bg-white/10 border border-white/20 rounded-2xl",children:[o.jsx(ic,{className:"w-4 h-4 text-white/70"}),o.jsxs("div",{className:"text-xs text-white/70",children:[o.jsx("span",{className:"font-medium",children:"Secure payment"})," via Shopify billing"]})]}),o.jsxs("div",{className:"space-y-2",children:[o.jsx("h4",{className:"text-sm font-medium text-white",children:"What's included:"}),o.jsxs("div",{className:"space-y-1.5 text-xs text-white/70",children:[o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Ze,{className:"w-3 h-3 text-green-400"}),o.jsx("span",{children:"AI-powered SEO optimization"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Ze,{className:"w-3 h-3 text-green-400"}),o.jsx("span",{children:"Meta descriptions & titles"})]}),o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Ze,{className:"w-3 h-3 text-green-400"}),o.jsx("span",{children:"Instant results"})]})]})]})]}),o.jsxs("div",{className:"px-6 py-4 border-t border-white/20 space-y-3",children:[o.jsxs("div",{className:"flex space-x-3",children:[o.jsx(fe,{onClick:r,disabled:s||i.state==="submitting",className:"flex-1 h-10 bg-white/10 hover:bg-white/20 text-white border-0",children:"Cancel"}),o.jsx(fe,{onClick:u,disabled:s||i.state==="submitting",className:"flex-1 h-10 bg-white hover:bg-gray-100 text-black border-0",children:i.state==="submitting"?o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx("div",{className:"w-4 h-4 border-2 border-black/30 border-t-black rounded-full animate-spin"}),o.jsx("span",{children:"Processing..."})]}):o.jsxs("div",{className:"flex items-center space-x-2",children:[o.jsx(Po,{className:"w-4 h-4"}),o.jsxs("span",{children:["Pay $",c.toFixed(2)]})]})})]}),o.jsx("p",{className:"text-xs text-white/70 text-center",children:"Secure payment via Shopify • Charges are non-refundable"})]})]})})}function Wc(){const[e,t]=d.useState(!1),[n,r]=d.useState(null);return{isProcessing:e,lastPurchase:n,createPurchase:async(c,i)=>{t(!0);try{const u=new FormData;u.append("action","create_pay_per_use_purchase"),u.append("productCount",c.toString()),u.append("selectedProducts",JSON.stringify(i));const l=await fetch("/app/billing/pay-per-use",{method:"POST",body:u});if(l.ok)window.location.href=l.url;else{const h=await l.json();throw new Error(h.error||"Failed to create purchase")}}catch(u){throw console.error("Pay-per-use purchase error:",u),u}finally{t(!1)}},checkPurchaseStatus:async c=>{try{const i=new FormData;i.append("action","check_purchase_status"),i.append("purchaseId",c);const u=await fetch("/app/billing/pay-per-use",{method:"POST",body:i});if(u.ok){const l=await u.json();return r({id:c,amount:l.amount,productCount:l.productCount,status:l.status}),l}else throw new Error("Failed to check purchase status")}catch(i){throw console.error("Purchase status check error:",i),i}}}}function rl(){var nt,_e,fn,hn,mn;const e=jo(),{products:t,productTypes:n,totalProducts:r,billing:s}=e,a=e.creditBalance,c=e.csrfToken,[i,u]=d.useState(a);d.useEffect(()=>{u(a)},[a]);const l=$t(),h=$t(),m=Eo(),[p,v]=d.useState(null);d.useEffect(()=>{typeof window<"u"&&Kn(async()=>{const{useAppBridge:g}=await import("./index-BOY1O_0a.js");return{useAppBridge:g}},__vite__mapDeps([3,4,5,6])).then(({useAppBridge:g})=>{try{v(g())}catch(P){console.warn("Failed to initialize app bridge:",P)}})},[]);const y=d.useRef(null),f=d.useCallback((g,P)=>{try{p!=null&&p.toast?p.toast.show(g,P):console.log(`Toast: ${g}`,P)}catch(A){console.warn("Toast error:",A),console.log(`Toast: ${g}`,P)}},[p]),b=Wc(),[x,w]=d.useState([]),[S,N]=d.useState(""),[j,O]=d.useState("all"),[C,R]=d.useState(!1),[I,L]=d.useState(!1),[$,U]=d.useState(null),[_,z]=d.useState(null),[F,M]=d.useState(0),[D,T]=d.useState(null),[q,ne]=d.useState(!1),[he,me]=d.useState(!1),[X,G]=d.useState(null),[re,pe]=d.useState({updateProductTitle:!1,updateProductDescription:!1,updateSeoFields:!0,updateHandle:!1,updateImageAlts:!1,autoApply:!0,batchSize:10});d.useEffect(()=>{if(C&&X&&!I){const g=setInterval(()=>{G(P=>{if(!P)return null;const A=Date.now()-P.startTime,ue=P.totalProducts*3e3,E=Math.min(95,A/ue*100),Z=Math.floor(E/100*P.totalBatches)+1,kt=Math.floor(E/100*P.totalProducts);return{...P,currentBatch:Math.min(Z,P.totalBatches),completedProducts:Math.min(kt,P.totalProducts),stage:Z<=P.totalBatches?`Processing batch ${Z}/${P.totalBatches}...`:"Finalizing optimizations..."}})},500);return()=>clearInterval(g)}},[C,X,I]),d.useEffect(()=>{if(l.state==="idle"&&l.data&&!I){const g=l.data;if((g.success||g.results&&g.results.length>0)&&g.results){const P=g.results||[],A=P.filter(E=>!E.error).length,ue=P.filter(E=>E.error).length;T({totalProcessed:P.length,successCount:A,errorCount:ue,results:P,processingTime:Date.now()-($?parseInt($):Date.now()),timestamp:new Date().toLocaleString(),summary:g.summary||{},message:g.message||`Optimized ${A} products successfully!`}),ne(!0),R(!1),G(null),A>0?ue>0?f(`✅ Optimized ${A} products successfully! ${ue} had issues.`,{duration:5e3}):f(`🎉 Successfully optimized ${A} products!`,{duration:5e3}):ue>0&&f(`⚠️ Optimization completed with ${ue} errors. Check results for details.`,{isError:!0,duration:5e3}),w([])}else if(g.error){if(g.isDuplicate){f("Optimization already in progress. Please wait...",{duration:3e3});return}console.error("❌ Optimization error:",g.error),f(`Optimization failed: ${g.error}`,{isError:!0,duration:5e3}),R(!1),X&&(G({...X,stage:"❌ Error occurred during optimization",currentProduct:"Process failed"}),setTimeout(()=>{G(null)},5e3))}}},[l.state,l.data,p,$,I]);const k=d.useCallback(()=>t.filter(g=>{const P=!S||g.title.toLowerCase().includes(S.toLowerCase())||g.description.toLowerCase().includes(S.toLowerCase()),A=j==="all"||g.type===j;return P&&A}),[t,S,j]),H=d.useCallback(g=>{w(P=>P.includes(g)?P.filter(A=>A!==g):[...P,g])},[]),te=d.useCallback(()=>{console.log("🔵🔵🔵 handleSelectAll called (BLUE Select All button) 🔵🔵🔵"),console.log("🔵 This should select ALL products or deselect all");const g=k();g.every(A=>x.includes(A.id))?(console.log("🔵 Deselecting all products"),w([])):(console.log("🔵 Selecting all products:",g.length),w(g.map(A=>A.id)))},[x,k]),K=d.useCallback(()=>{console.log("🟢🟢🟢 handleSelectNonOptimized called (GREEN Select Needs Optimization button) 🟢🟢🟢"),console.log("🟢 This should ONLY select products with SEO score < 80 or status pending/failed");const g=k(),P=g.filter(E=>E!=null).map(E=>E.seoScore).sort((E,Z)=>E-Z);console.log("📊 SEO Score Distribution:",{total:g.length,min:P.length>0?Math.min(...P):0,max:P.length>0?Math.max(...P):0,avg:P.length>0?Math.round(P.reduce((E,Z)=>E+Z,0)/P.length):0,below80:P.filter(E=>E<80).length,below70:P.filter(E=>E<70).length,scores:P.slice(0,20)}),console.log("🔍 Sample products (first 3):",g.slice(0,3).map(E=>{var Z;return{id:E==null?void 0:E.id,title:((Z=E==null?void 0:E.title)==null?void 0:Z.substring(0,20))+"...",status:E==null?void 0:E.status,seoScore:E==null?void 0:E.seoScore,seoScoreType:typeof(E==null?void 0:E.seoScore),needsOptimization:(E==null?void 0:E.seoScore)!=null&&E.seoScore<80}}));const A=g.filter(E=>{if(!E)return!1;const Z=Number(E.seoScore)<80;return g.indexOf(E)<5&&console.log(`🔍 Product ${E.id}: status=${E.status}, seoScore=${E.seoScore} (${typeof E.seoScore}), needsOptimization=${Z} (ONLY checking score < 80)`),Z});if(console.log("🎯 Products that need optimization:",A.length,"out of",g.length),console.log("🎯 Should be selecting:",A.length,"products"),console.log("🎯 First 5 products needing optimization:",A.slice(0,5).filter(E=>E!=null).map(E=>{var Z;return{id:E.id,title:((Z=E.title)==null?void 0:Z.substring(0,30))+"...",status:E.status,seoScore:E.seoScore}})),A.every(E=>x.includes(E.id))&&A.length>0){const E=A.map(Z=>Z.id);console.log("🔄 Deselecting products that need optimization:",E.length),w(Z=>Z.filter(kt=>!E.includes(kt)))}else{const E=A.map(Z=>Z.id);console.log("✅ Selecting products that need optimization:",E.length),console.log("✅ Product IDs being selected:",E.slice(0,10)),console.log("✅ About to call setSelectedProducts with",E.length,"product IDs"),w(E),console.log("✅ setSelectedProducts called successfully")}},[x,k]),B=d.useCallback(()=>{if(console.log("🎯 handleOptimizeWithCredits called with:",{selectedProductsLength:x.length,isProcessing:C,isUsingCredits:I}),x.length===0){f("Please select products to optimize",{isError:!0});return}R(!0),L(!0),G({startTime:Date.now(),totalProducts:x.length,completedProducts:0,currentBatch:1,totalBatches:1,currentProduct:"Initializing...",stage:"Starting optimization with credits..."});const g=new FormData;g.append("action","optimize_with_credits"),g.append("productIds",JSON.stringify(x)),g.append("settings",JSON.stringify(re)),g.append("csrfToken",c),console.log("📤 About to submit form with data:",{action:"optimize_with_credits",productIds:x,settings:re,csrfToken:c}),console.log("📤 FormData entries:",Array.from(g.entries())),console.log("📤 Fetcher state before submit:",l.state),l.submit(g,{method:"POST",action:"/app/seo-dashboard"}),console.log("📤 Fetcher state after submit:",l.state),setTimeout(()=>{y.current&&y.current.scrollIntoView({behavior:"smooth",block:"center"})},100)},[x,re,c,f,l]),W=d.useCallback(g=>{console.log(`🔄 Starting progress polling for request: ${g}`),_&&clearInterval(_),M(0);const P=setInterval(()=>{try{if(h.state==="idle"){const A=new FormData;A.append("action","check_progress"),A.append("requestId",g),console.log(`🔄 Polling progress for request: ${g} (errors: ${F})`),h.submit(A,{method:"POST",action:"/app/seo-dashboard"})}else console.log(`⏳ Progress fetcher busy (${h.state}), skipping poll`)}catch(A){console.error("❌ Progress polling error:",A),M(ue=>ue+1),F>=5&&(console.error(`❌ Too many polling errors (${F}), stopping`),_&&(clearInterval(_),z(null)),f("Progress tracking failed. The optimization is still running in the background.",{isError:!0}))}},5e3);z(P)},[_,h,f,F,M]);d.useEffect(()=>{if(h.state==="idle"&&h.data){const g=h.data;if(console.log("📊 Progress polling response:",g),g.success&&g.status){const P=g.status;console.log("📊 Progress update:",P),M(0),G(A=>A?{...A,currentBatch:P.progress.currentBatch,totalBatches:P.progress.totalBatches,completedProducts:P.progress.processedProducts,currentProduct:`Processing batch ${P.progress.currentBatch}/${P.progress.totalBatches}`,stage:P.status==="processing"?"Optimizing products...":P.status}:null),(P.status==="completed"||P.status==="failed")&&(_&&(clearInterval(_),z(null)),P.status==="completed"?(G(A=>A?{...A,completedProducts:A.totalProducts,currentProduct:`Successfully optimized ${A.totalProducts} products!`,stage:"Optimization completed successfully!"}:null),setTimeout(()=>{f(`Successfully optimized ${x.length} products!`),R(!1),L(!1),G(null)},2e3)):(f(`Optimization failed: ${P.error||"Unknown error"}`,{isError:!0}),R(!1),L(!1),G(null)))}else g.error&&(console.error("❌ Progress polling error response:",g.error),M(P=>P+1),(g.error.includes("502")||g.error.includes("Bad Gateway"))&&console.log("🔄 Network error detected, continuing to poll..."),F>=5&&(console.error(`❌ Too many polling errors (${F}), stopping`),_&&(clearInterval(_),z(null)),f("Progress tracking failed due to network issues. The optimization is still running.",{isError:!0})))}h.state==="idle"&&!h.data&&C&&I&&console.log("⚠️ Progress fetcher returned no data, likely a network error")},[h.state,h.data,_,f,x.length,C,I]),d.useEffect(()=>{I&&(l.state==="submitting"?G(g=>g?{...g,stage:"Processing your request...",currentProduct:"Validating credits and permissions..."}:null):l.state==="loading"?G(g=>g?{...g,stage:"Optimizing products...",currentProduct:`Processing ${g.totalProducts} selected products...`,completedProducts:Math.floor(g.totalProducts*.3)}:null):l.state==="idle"&&l.data&&G(g=>g?{...g,stage:"Request completed, starting optimization...",currentProduct:"Initializing optimization process..."}:null))},[l.state,I,l.data]),d.useEffect(()=>{if(console.log(`🔄 Fetcher state changed: ${l.state}`,{data:l.data,isUsingCredits:I,hasData:!!l.data,dataKeys:l.data?Object.keys(l.data):[]}),l.state==="idle"&&l.data&&I){const g=l.data;console.log("📊 Credit optimization response data:",g),g.success&&g.requestId?(console.log(`✅ Optimization started with requestId: ${g.requestId}`),U(g.requestId),g.remainingCredits!==void 0&&(u(P=>({...P,remainingCredits:g.remainingCredits,usedCredits:P.usedCredits+(g.creditsUsed||0)})),console.log(`💰 Updated credit balance: ${g.remainingCredits} remaining`)),W(g.requestId),G(P=>P?{...P,currentProduct:"Connecting to optimization process...",stage:"Starting real-time progress tracking..."}:null)):g.error&&(f(g.error,{isError:!0}),R(!1),L(!1),G(null))}else l.state==="idle"&&C&&!l.data&&(R(!1),L(!1),G(null))},[l.state,l.data,x.length,f,C,I]),d.useEffect(()=>()=>{_&&clearInterval(_)},[_]);const ie=d.useCallback(()=>{if(x.length===0){f("Please select products to optimize",{isError:!0});return}if(C){console.log("⚠️ Already processing - please wait");return}const g=s;if(console.log("🔍 Billing check - hasAccess:",s.hasAccess),console.log("🔍 Billing check - plan:",g.plan),console.log("🔍 Billing check - subscription:",g.subscription),!g.plan){console.log("💳 No plan selected, showing pay-per-use modal as default"),me(!0);return}if(g.plan.id==="pay_per_use")if(console.log("💳 Pay-per-use plan detected, checking credit balance..."),console.log("💰 Credit balance:",i),console.log("💰 Credit balance details:",{remainingCredits:i==null?void 0:i.remainingCredits,totalCredits:i==null?void 0:i.totalCredits,usedCredits:i==null?void 0:i.usedCredits,selectedProductsLength:x.length}),i&&i.remainingCredits>=x.length){console.log("✅ Sufficient credits available, proceeding with optimization"),console.log("🎯 About to call handleOptimizeWithCredits..."),B(),console.log("🎯 handleOptimizeWithCredits call completed");return}else{console.log("❌ Insufficient credits, showing payment modal"),console.log(`Required: ${x.length}, Available: ${(i==null?void 0:i.remainingCredits)||0}`),me(!0);return}if(!g.subscription||g.subscription.status!=="ACTIVE"){console.log("💳 No active subscription, showing pay-per-use modal"),me(!0);return}console.log("✅ User has active subscription, proceeding with optimization");const P=Date.now().toString();if($&&Date.now()-parseInt($)<1e4){console.log("⚠️ Preventing duplicate request - please wait 10 seconds between requests"),f("Please wait 10 seconds between optimization requests",{isError:!0});return}U(P),R(!0);const A=x.length,ue=Math.ceil(A/10);G({currentBatch:0,totalBatches:ue,currentProduct:"",completedProducts:0,totalProducts:A,stage:"Initializing...",startTime:Date.now()}),setTimeout(()=>{y.current&&y.current.scrollIntoView({behavior:"smooth",block:"start"})},100);const E=new FormData;E.append("action","optimize_comprehensive"),E.append("productIds",JSON.stringify(x)),E.append("settings",JSON.stringify(re)),E.append("requestId",P),l.submit(E,{method:"POST"})},[x,re,l,p,m,C,$,s]),de=d.useCallback(()=>{me(!1)},[]),Ie=d.useCallback(()=>{me(!1)},[]),Q={totalProducts:r,selectedCount:x.length,averageSeoScore:t.length>0?Math.round(t.reduce((g,P)=>g+((P==null?void 0:P.seoScore)||0),0)/t.length):0,needsOptimization:t.filter(g=>(g==null?void 0:g.seoScore)<80).length,optimizedCount:t.filter(g=>(g==null?void 0:g.status)==="optimized").length},ae=k();return o.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,ease:"easeOut"},className:"min-h-screen bg-black text-white",children:[o.jsx(Oo,{title:"AI BULK SEO - SEO Optimizer"}),o.jsx($c,{totalProducts:Q.totalProducts,selectedCount:x.length,averageSeoScore:Q.averageSeoScore,onOptimizeSelected:ie,onSelectAll:te,isOptimizing:C}),o.jsxs("div",{className:"bg-black py-20 px-6",children:[o.jsxs(V.div,{className:"max-w-6xl mx-auto",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,ease:"easeOut"},children:[o.jsxs(V.div,{className:"flex flex-col gap-8",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},children:[o.jsxs(V.div,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},children:[o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.1},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all",children:[o.jsxs("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx("div",{className:"text-sm font-medium text-white/70",children:"Total Products"}),o.jsx("div",{className:"w-8 h-8 bg-white/20 rounded-2xl flex items-center justify-center",children:o.jsx(_o,{size:16})})]}),o.jsxs("div",{className:"pt-2",children:[o.jsx("div",{className:"text-2xl font-bold text-white",children:Q.totalProducts.toLocaleString()}),o.jsx("p",{className:"text-xs text-white/70",children:"Ready for optimization"})]})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all",children:[o.jsxs("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx("div",{className:"text-sm font-medium text-white/70",children:"Average SEO Score"}),o.jsx("div",{className:oe("w-8 h-8 rounded-2xl flex items-center justify-center",Q.averageSeoScore>=80?"bg-white/30":"bg-white/20"),children:o.jsx("div",{className:oe("w-3 h-3 rounded-full",Q.averageSeoScore>=80?"bg-white":"bg-white/70")})})]}),o.jsxs("div",{className:"pt-2",children:[o.jsxs("div",{className:"text-2xl font-bold text-white",children:[Q.averageSeoScore,"%"]}),o.jsx("p",{className:"text-xs text-white/70",children:Q.averageSeoScore>=80?"Excellent performance":Q.averageSeoScore>=60?"Good, can improve":"Needs attention"})]})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.3},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all",children:[o.jsxs("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx("div",{className:"text-sm font-medium text-white/70",children:"Need Optimization"}),o.jsx("div",{className:oe("w-8 h-8 rounded-2xl flex items-center justify-center",Q.needsOptimization>0?"bg-white/30":"bg-white/20"),children:o.jsx("div",{className:oe("w-2 h-2 rotate-45",Q.needsOptimization>0?"bg-white":"bg-white/70")})})]}),o.jsxs("div",{className:"pt-2",children:[o.jsx("div",{className:"text-2xl font-bold text-white",children:Q.needsOptimization.toLocaleString()}),o.jsx("p",{className:"text-xs text-white/70",children:"Products below 80% score"})]})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.4},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all",children:[o.jsxs("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx("div",{className:"text-sm font-medium text-white/70",children:"Optimized"}),o.jsx("div",{className:"w-8 h-8 bg-green-500/30 rounded-2xl flex items-center justify-center",children:o.jsx(Io,{size:16})})]}),o.jsxs("div",{className:"pt-2",children:[o.jsx("div",{className:"text-2xl font-bold text-white",children:Q.optimizedCount.toLocaleString()}),o.jsx("p",{className:"text-xs text-white/70",children:"Successfully optimized"})]})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.5},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-6 hover:bg-white/15 transition-all",children:[o.jsxs("div",{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[o.jsx("div",{className:"text-sm font-medium text-white/70",children:"Credits Remaining"}),o.jsx("div",{className:oe("w-8 h-8 rounded-2xl flex items-center justify-center",(i==null?void 0:i.remainingCredits)>0?"bg-green-500/30":"bg-red-500/30"),children:o.jsx("div",{className:oe("w-3 h-3 rounded-full",(i==null?void 0:i.remainingCredits)>0?"bg-green-400":"bg-red-400")})})]}),o.jsxs("div",{className:"pt-2",children:[o.jsx("div",{className:"text-2xl font-bold text-white",children:(i==null?void 0:i.remainingCredits)||0}),o.jsx("p",{className:"text-xs text-white/70",children:(i==null?void 0:i.remainingCredits)>0?`${i.usedCredits}/${i.totalCredits} used`:"Purchase credits to optimize"})]})]})})]}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.5},children:o.jsx(Fc,{subscription:s.subscription,plan:s.plan,hasAccess:s.hasAccess,monthlyUsage:{productsOptimized:Q.optimizedCount,totalSpent:((nt=s.plan)==null?void 0:nt.type)==="pay_per_use"?Q.optimizedCount*.1:((_e=s.plan)==null?void 0:_e.price)||0}})}),o.jsx("div",{className:"bg-yellow-500/20 border border-yellow-500/40 rounded-3xl p-6",children:o.jsxs("div",{className:"flex items-start gap-3",children:[o.jsx("div",{className:"w-5 h-5 bg-yellow-400 rounded-2xl flex items-center justify-center flex-shrink-0 mt-0.5",children:o.jsx("div",{className:"w-2 h-2 bg-black rounded-full"})}),o.jsxs("div",{children:[o.jsx("div",{className:"text-sm font-medium text-yellow-300 mb-1",children:"Pro Tip"}),o.jsx("div",{className:"text-sm text-yellow-200 leading-relaxed",children:"Run these audits on your store's main pages to identify SEO opportunities before optimizing your products. Both tools are completely free and provide actionable insights."})]})]})})]}),o.jsxs(V.div,{className:"bg-white/10 border border-white/20 rounded-3xl p-8 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.3},children:[o.jsxs("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-2xl font-bold mb-2 text-white",children:"Optimization Settings"}),o.jsx("p",{className:"text-white/70",children:"Configure which fields to optimize and how to process your products"})]}),o.jsxs(V.div,{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.4},children:[o.jsxs(V.div,{className:"space-y-4",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.5},children:[o.jsx("h3",{className:"text-base font-semibold mb-4 text-white",children:"Fields to Update"}),o.jsx("div",{className:"space-y-3",children:[{key:"productTitle",label:"Product Title",desc:"Optimize main product titles"},{key:"productDescription",label:"Product Description",desc:"Enhance product descriptions"},{key:"seoFields",label:"SEO Meta Tags",desc:"Page title & meta description"},{key:"handle",label:"URL Handle",desc:"SEO-friendly URLs"},{key:"imageAlts",label:"Image Alt Texts",desc:"Accessibility & SEO"}].map(g=>o.jsxs("label",{className:oe("flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",re[`update${g.key.charAt(0).toUpperCase()+g.key.slice(1)}`]?"border-white/40 bg-white/10":"border-white/20 bg-white/5 hover:bg-white/10"),children:[o.jsx("input",{type:"checkbox",checked:re[`update${g.key.charAt(0).toUpperCase()+g.key.slice(1)}`],onChange:P=>{pe(A=>({...A,[`update${g.key.charAt(0).toUpperCase()+g.key.slice(1)}`]:P.target.checked}))},className:"w-4 h-4 accent-white"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"text-sm font-medium text-white",children:g.label}),o.jsx("div",{className:"text-xs text-white/70",children:g.desc})]})]},g.key))})]}),o.jsxs(V.div,{className:"space-y-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:.5},children:[o.jsx("h3",{className:"text-base font-semibold mb-4 text-white",children:"Processing Options"}),o.jsxs("div",{className:"space-y-6",children:[o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{children:[o.jsx("div",{className:"text-sm font-medium mb-1 text-white",children:"Application Mode"}),o.jsx("div",{className:"text-xs text-white/70",children:"Choose how to apply optimizations"})]}),o.jsx("div",{className:"space-y-3",children:[{value:!0,label:"Auto-apply changes immediately",desc:"Faster processing"},{value:!1,label:"Review before applying",desc:"Manual approval"}].map(g=>o.jsxs("label",{className:oe("flex items-center gap-3 p-4 rounded-2xl border cursor-pointer transition-all",re.autoApply===g.value?"border-white/40 bg-white/10":"border-white/20 bg-white/5 hover:bg-white/10"),children:[o.jsx("input",{type:"radio",name:"autoApply",checked:re.autoApply===g.value,onChange:()=>{pe(P=>({...P,autoApply:g.value}))},className:"w-4 h-4 accent-white"}),o.jsxs("div",{className:"flex-1",children:[o.jsx("div",{className:"text-sm font-medium text-white",children:g.label}),o.jsx("div",{className:"text-xs text-white/70",children:g.desc})]})]},g.value.toString()))})]}),o.jsxs("div",{className:"space-y-3",children:[o.jsxs("div",{children:[o.jsx("div",{className:"text-sm font-medium mb-1 text-white",children:"Batch Size"}),o.jsx("div",{className:"text-xs text-white/70",children:"Products processed per batch"})]}),o.jsxs("select",{value:re.batchSize,onChange:g=>{pe(P=>({...P,batchSize:parseInt(g.target.value)}))},className:"w-full p-4 rounded-2xl border border-white/20 bg-white/5 text-white text-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-white/40 focus:border-white/40 hover:bg-white/10",children:[o.jsx("option",{value:5,className:"bg-black text-white",children:"5 products (Slower, more stable)"}),o.jsx("option",{value:10,className:"bg-black text-white",children:"10 products (Recommended)"}),o.jsx("option",{value:20,className:"bg-black text-white",children:"20 products (Faster, higher load)"})]})]})]})]})]})]})]}),o.jsx(ko,{children:C&&X&&o.jsx(V.div,{ref:y,initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:-20},transition:{duration:.4,ease:"easeOut"},children:o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8 mb-8 relative overflow-hidden",children:[o.jsx("div",{className:"absolute inset-0 opacity-10 pointer-events-none"}),o.jsxs("div",{className:"relative z-10",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx(V.div,{className:"w-20 h-20 bg-white/20 rounded-full mx-auto mb-5 flex items-center justify-center border border-white/30 shadow-lg",whileHover:{scale:1.05},transition:{type:"spring",stiffness:300},children:o.jsx("div",{className:"w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"})}),o.jsx("h2",{className:"text-2xl font-bold mb-2 text-white",children:"SEO Optimization in Progress"}),o.jsx("p",{className:"text-sm text-white/70",children:X.stage})]}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6",children:[o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.1},whileHover:{scale:1.05},children:o.jsxs("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:[o.jsx("div",{className:"text-3xl font-bold mb-1 text-white",children:X.completedProducts}),o.jsx("div",{className:"text-xs text-white/70 font-medium uppercase tracking-wider",children:"Products Completed"})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.2},whileHover:{scale:1.05},children:o.jsxs("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:[o.jsx("div",{className:"text-3xl font-bold mb-1 text-white",children:X.totalProducts}),o.jsx("div",{className:"text-xs text-white/70 font-medium uppercase tracking-wider",children:"Total Products"})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.3},whileHover:{scale:1.05},children:o.jsxs("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:[o.jsxs("div",{className:"text-3xl font-bold mb-1 text-white",children:[Math.round((Date.now()-X.startTime)/1e3),"s"]}),o.jsx("div",{className:"text-xs text-white/70 font-medium uppercase tracking-wider",children:"Elapsed Time"})]})}),o.jsx(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.4,delay:.4},whileHover:{scale:1.05},children:o.jsxs("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:[o.jsxs("div",{className:"text-3xl font-bold mb-1 text-white",children:[X.completedProducts>0?Math.round((X.totalProducts-X.completedProducts)*((Date.now()-X.startTime)/X.completedProducts)/1e3):Math.round(X.totalProducts*2.5),"s"]}),o.jsx("div",{className:"text-xs text-white/70 font-medium uppercase tracking-wider",children:"Est. Remaining"})]})})]}),o.jsx("div",{className:"mb-6",children:o.jsx(Pc,{value:Math.round(X.completedProducts/X.totalProducts*100),className:"h-3"})}),X.currentProduct&&o.jsxs("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 mb-5 text-center",children:[o.jsx("div",{className:"text-xs text-white/70 mb-2 font-medium uppercase tracking-wider",children:"Currently Processing:"}),o.jsx("div",{className:"text-base font-semibold truncate text-white",children:X.currentProduct})]}),o.jsx("div",{className:"bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:o.jsx("div",{className:"text-sm text-white/70 italic",children:"💡 Keep this tab open for the best experience. Processing typically takes 2-3 seconds per product."})})]})]})})}),!C&&o.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8 mb-8",children:[o.jsxs("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-2xl font-bold mb-2 text-white",children:"Product Selection"}),o.jsx("p",{className:"text-white/70",children:"Select products to optimize their SEO titles, descriptions, and viral keywords."})]}),o.jsxs("div",{children:[o.jsxs(V.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.3,delay:.2},className:"flex gap-4 mb-6 flex-wrap",children:[o.jsx(V.div,{whileFocus:{scale:1.02},className:"flex-1 min-w-[300px]",children:o.jsx(yo,{type:"text",placeholder:"Search products...",value:S,onChange:g=>N(g.target.value),className:"w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"})}),o.jsx(V.div,{whileHover:{scale:1.02},className:"min-w-[200px]",children:o.jsxs(uc,{value:j,onValueChange:O,children:[o.jsx(hc,{className:"bg-white/5 border-white/20 text-white focus:border-white/40",children:o.jsx(fc,{placeholder:"All Types"})}),o.jsxs(mc,{className:"bg-black border-white/20",children:[o.jsx(Fn,{value:"all",className:"text-white hover:bg-white/10",children:"All Types"}),n.filter(g=>g!=null).map(g=>o.jsx(Fn,{value:g,className:"text-white hover:bg-white/10",children:g},g))]})]})})]}),o.jsxs("div",{className:"flex justify-between items-center mb-4 py-3 border-b border-white/20",children:[o.jsxs("div",{className:"flex items-center gap-3",children:[o.jsx(V.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:o.jsx(fe,{onClick:te,size:"sm",className:"text-blue-400 hover:text-blue-300 hover:bg-blue-500/20 bg-transparent border-blue-400/50",children:ae.every(P=>x.includes(P.id))?"Deselect All":"Select All"})}),o.jsx(Ac,{orientation:"vertical",className:"h-4 bg-white/20"}),o.jsx(V.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:17},children:o.jsx(fe,{onClick:K,size:"sm",className:"text-green-400 hover:text-green-300 hover:bg-green-500/20 bg-transparent border-green-400/50",children:(()=>{const g=ae.filter(A=>A.seoScore<80);return g.every(A=>x.includes(A.id))&&g.length>0?"Deselect Needs Optimization":"Select Needs Optimization"})()})}),o.jsxs("span",{className:"text-sm text-white/70",children:[x.length," of ",ae.length," selected",(()=>{const g=ae.filter(P=>P.seoScore<80).length;return g>0?` (${g} need optimization)`:""})()]})]}),o.jsxs("div",{className:"text-sm text-white/70",children:["Showing ",ae.length," products"]})]}),o.jsxs("div",{className:"border border-white/20 rounded-2xl overflow-hidden",children:[o.jsxs("div",{className:"bg-white/5 px-4 py-3 border-b border-white/20 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center text-xs font-semibold text-white/70 uppercase tracking-wider min-w-[600px]",children:[o.jsx("div",{}),o.jsx("div",{children:"Product"}),o.jsx("div",{children:"Type"}),o.jsx("div",{children:"SEO Score"}),o.jsx("div",{children:"Status"})]}),o.jsx("div",{className:"max-h-96 overflow-y-auto overflow-x-auto",children:ae.length===0?o.jsxs("div",{className:"py-12 px-4 text-center text-white/70",children:[o.jsx("div",{className:"text-base mb-2",children:"No products found"}),o.jsx("div",{className:"text-sm",children:"Try adjusting your search or filter criteria"})]}):ae.map((g,P)=>o.jsxs(V.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:P*.05,ease:"easeOut"},whileHover:{scale:1.01,transition:{duration:.2}},className:"p-4 border-b border-white/10 grid grid-cols-[40px_1fr_120px_100px_120px] gap-4 items-center cursor-pointer min-w-[600px] rounded-sm hover:bg-white/5",onClick:()=>H(g.id),children:[o.jsx("div",{children:o.jsx(zc,{checked:x.includes(g.id),onCheckedChange:()=>H(g.id),className:"border-white/30 data-[state=checked]:bg-white data-[state=checked]:text-black"})}),o.jsxs("div",{className:"min-w-0 overflow-hidden",children:[o.jsx("div",{className:"text-sm font-medium mb-1 truncate text-white",children:g.title}),o.jsx("div",{className:"text-xs text-white/70 truncate",children:g.description||"No description"})]}),o.jsx("div",{className:"text-xs text-white/70 truncate min-w-0",children:g.type}),o.jsx("div",{className:"min-w-0 overflow-hidden",children:o.jsxs(Ee,{variant:g.seoScore>=80?"default":g.seoScore>=60?"secondary":"destructive",className:"text-xs",children:[g.seoScore,"%"]})}),o.jsx("div",{className:"min-w-0 overflow-hidden",children:o.jsx(Ee,{variant:g.status==="optimized"?"default":g.status==="processing"?"secondary":"outline",className:"text-xs",children:g.status==="pending"?"Ready":g.status==="processing"?"Processing":g.status==="optimized"?"Optimized":"Failed"})})]},g.id))})]})]})]})]}),q&&D?o.jsx("div",{className:"bg-black py-20 px-6",children:o.jsx("div",{className:"max-w-6xl mx-auto",children:o.jsx("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8 mb-8",children:o.jsxs("div",{className:"pt-2",children:[o.jsxs("div",{className:"text-center mb-8",children:[o.jsx("div",{className:"w-20 h-20 bg-white rounded-full mx-auto mb-5 flex items-center justify-center",children:o.jsx("div",{className:"text-2xl text-black",children:"✓"})}),o.jsx("h1",{className:"text-3xl font-bold mb-2 tracking-tight text-white",children:"SEO Optimization Complete!"}),o.jsx("p",{className:"text-base text-white/70",children:"Your products are now optimized with viral keywords and enhanced SEO"})]}),o.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8",children:[o.jsxs("div",{className:"text-center bg-white/5 border border-white/20 rounded-2xl p-6",children:[o.jsx("div",{className:"text-4xl font-bold mb-2 text-white",children:D.successCount}),o.jsx("div",{className:"text-sm font-semibold mb-1 text-white",children:"Products Optimized"}),o.jsx("div",{className:"text-xs text-white/70",children:"Successfully enhanced"})]}),o.jsxs("div",{className:"text-center bg-white/5 border border-white/20 rounded-2xl p-6",children:[o.jsxs("div",{className:"text-4xl font-bold mb-2 text-white",children:[Math.round((D.processingTime||0)/1e3),"s"]}),o.jsx("div",{className:"text-sm font-semibold mb-1 text-white",children:"Processing Time"}),o.jsx("div",{className:"text-xs text-white/70",children:"Lightning fast"})]}),o.jsxs("div",{className:"text-center bg-white/5 border border-white/20 rounded-2xl p-6",children:[o.jsxs("div",{className:"text-4xl font-bold mb-2 text-white",children:[((fn=D.summary)==null?void 0:fn.averageImprovementScore)||85,"%"]}),o.jsx("div",{className:"text-sm font-semibold mb-1 text-white",children:"SEO Score"}),o.jsx("div",{className:"text-xs text-white/70",children:"Average improvement"})]})]}),((mn=(hn=D.summary)==null?void 0:hn.topViralKeywords)==null?void 0:mn.length)>0&&o.jsxs("div",{className:"mb-6 bg-white/5 border border-white/20 rounded-2xl p-6",children:[o.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[o.jsx("div",{className:"w-6 h-6 bg-white rounded flex items-center justify-center text-xs text-black font-medium",children:"#"}),o.jsx("div",{className:"text-base font-semibold text-white",children:"Top Viral Keywords Added:"})]}),o.jsx("div",{className:"flex gap-2 flex-wrap",children:D.summary.topViralKeywords.slice(0,5).map((g,P)=>o.jsx(Ee,{variant:"secondary",className:"text-xs bg-white/20 text-white border-white/30",children:g},P))})]}),o.jsxs("div",{className:"mb-6 bg-white/5 border border-white/20 rounded-2xl p-6 text-center",children:[o.jsx("div",{className:"text-lg font-semibold mb-2 text-white",children:"🚀 Your Products Are Now SEO Optimized!"}),o.jsx("div",{className:"text-sm text-white/70 leading-relaxed mb-3",children:"Each product now has optimized titles, descriptions, and viral keywords designed to boost your search rankings and drive more traffic to your store."}),o.jsx("div",{className:"text-xs text-white/70 italic",children:"💡 Changes have been automatically applied to your Shopify store"})]}),o.jsxs("div",{className:"flex gap-3 justify-center flex-wrap",children:[o.jsx(fe,{onClick:()=>window.location.reload(),size:"lg",className:"bg-white text-black hover:bg-gray-100 font-bold rounded-2xl",children:"Optimize More Products"}),o.jsx(fe,{onClick:()=>ne(!1),size:"lg",className:"border border-white/20 bg-white/10 text-white hover:bg-white/20 rounded-2xl",children:"Continue Optimizing"})]})]})})})}):null,he&&o.jsx(Bc,{productCount:x.length,selectedProducts:x,onConfirm:de,onCancel:Ie,isProcessing:b.isProcessing,csrfToken:c})]})}export{rl as default};
