/**
 * Enhanced Input Validation Utilities
 * Comprehensive validation for all billing-related inputs with error handling integration
 */

import { createError, type ErrorContext } from "./error-handling.server";
// import { FormValidationError, FormState } from "../types";

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue?: any;
}

export class ValidationError extends Error {
  constructor(message: string, public errors: string[]) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Enhanced validation with error context
 */
export function validateWithContext<T>(
  value: unknown,
  validator: (value: unknown) => ValidationResult,
  fieldName: string,
  context?: ErrorContext
): T {
  const result = validator(value);

  if (!result.isValid) {
    throw createError('VALIDATION_INVALID_INPUT', {
      ...context,
      metadata: {
        field: fieldName,
        errors: result.errors,
        value: typeof value === 'string' ? value.substring(0, 100) : value
      }
    });
  }

  return result.sanitizedValue as T;
}

/**
 * Validate form data with enhanced error handling
 */
export function validateFormDataWithContext(
  formData: FormData,
  requiredFields: string[],
  context?: ErrorContext
): Record<string, string> {
  const data: Record<string, string> = {};
  const missingFields: string[] = [];

  for (const field of requiredFields) {
    const value = formData.get(field)?.toString();
    if (!value || value.trim() === '') {
      missingFields.push(field);
    } else {
      data[field] = value.trim();
    }
  }

  if (missingFields.length > 0) {
    throw createError('VALIDATION_MISSING_REQUIRED_FIELD', {
      ...context,
      metadata: { missingFields }
    });
  }

  return data;
}

/**
 * Validate plan ID
 */
export function validatePlanId(planId: unknown): ValidationResult {
  const errors: string[] = [];

  if (!planId) {
    errors.push('Plan ID is required');
    return { isValid: false, errors };
  }

  if (typeof planId !== 'string') {
    errors.push('Plan ID must be a string');
    return { isValid: false, errors };
  }

  const trimmedPlanId = planId.trim();
  
  if (trimmedPlanId.length === 0) {
    errors.push('Plan ID cannot be empty');
    return { isValid: false, errors };
  }

  // Valid plan IDs
  const validPlanIds = ['monthly', 'annual', 'pay_per_use'];
  if (!validPlanIds.includes(trimmedPlanId)) {
    errors.push(`Invalid plan ID. Must be one of: ${validPlanIds.join(', ')}`);
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedPlanId
  };
}

/**
 * Validate product count for pay-per-use
 */
export function validateProductCount(productCount: unknown): ValidationResult {
  const errors: string[] = [];

  if (productCount === null || productCount === undefined) {
    errors.push('Product count is required');
    return { isValid: false, errors };
  }

  let numericCount: number;

  if (typeof productCount === 'string') {
    const parsed = parseInt(productCount.trim(), 10);
    if (isNaN(parsed)) {
      errors.push('Product count must be a valid number');
      return { isValid: false, errors };
    }
    numericCount = parsed;
  } else if (typeof productCount === 'number') {
    if (!Number.isInteger(productCount)) {
      errors.push('Product count must be an integer');
      return { isValid: false, errors };
    }
    numericCount = productCount;
  } else {
    errors.push('Product count must be a number');
    return { isValid: false, errors };
  }

  if (numericCount <= 0) {
    errors.push('Product count must be greater than 0');
  }

  if (numericCount > 1000) {
    errors.push('Product count cannot exceed 1000 per purchase');
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: numericCount
  };
}

/**
 * Validate selected products array
 */
export function validateSelectedProducts(selectedProducts: unknown, expectedCount?: number): ValidationResult {
  const errors: string[] = [];

  if (!selectedProducts) {
    errors.push('Selected products are required');
    return { isValid: false, errors };
  }

  let parsedProducts: string[];

  if (typeof selectedProducts === 'string') {
    try {
      parsedProducts = JSON.parse(selectedProducts);
    } catch {
      errors.push('Selected products must be valid JSON');
      return { isValid: false, errors };
    }
  } else if (Array.isArray(selectedProducts)) {
    parsedProducts = selectedProducts;
  } else {
    errors.push('Selected products must be an array');
    return { isValid: false, errors };
  }

  if (!Array.isArray(parsedProducts)) {
    errors.push('Selected products must be an array');
    return { isValid: false, errors };
  }

  if (parsedProducts.length === 0) {
    errors.push('At least one product must be selected');
    return { isValid: false, errors };
  }

  // Validate each product ID
  for (let i = 0; i < parsedProducts.length; i++) {
    const productId = parsedProducts[i];
    
    if (typeof productId !== 'string') {
      errors.push(`Product ID at index ${i} must be a string`);
      continue;
    }

    if (productId.trim().length === 0) {
      errors.push(`Product ID at index ${i} cannot be empty`);
      continue;
    }

    // Basic Shopify product ID validation (should start with gid://shopify/Product/)
    if (!productId.startsWith('gid://shopify/Product/')) {
      errors.push(`Product ID at index ${i} has invalid format`);
    }
  }

  // Check count matches expected
  if (expectedCount !== undefined && parsedProducts.length !== expectedCount) {
    errors.push(`Expected ${expectedCount} products, but got ${parsedProducts.length}`);
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: parsedProducts.map(id => id.trim())
  };
}

/**
 * Validate subscription ID
 */
export function validateSubscriptionId(subscriptionId: unknown): ValidationResult {
  const errors: string[] = [];

  if (!subscriptionId) {
    errors.push('Subscription ID is required');
    return { isValid: false, errors };
  }

  if (typeof subscriptionId !== 'string') {
    errors.push('Subscription ID must be a string');
    return { isValid: false, errors };
  }

  const trimmedId = subscriptionId.trim();
  
  if (trimmedId.length === 0) {
    errors.push('Subscription ID cannot be empty');
    return { isValid: false, errors };
  }

  // Shopify subscription ID format validation
  if (!trimmedId.startsWith('gid://shopify/AppSubscription/')) {
    errors.push('Invalid subscription ID format');
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedId
  };
}

/**
 * Validate return URL
 */
export function validateReturnUrl(returnUrl: unknown): ValidationResult {
  const errors: string[] = [];

  if (!returnUrl) {
    errors.push('Return URL is required');
    return { isValid: false, errors };
  }

  if (typeof returnUrl !== 'string') {
    errors.push('Return URL must be a string');
    return { isValid: false, errors };
  }

  const trimmedUrl = returnUrl.trim();
  
  if (trimmedUrl.length === 0) {
    errors.push('Return URL cannot be empty');
    return { isValid: false, errors };
  }

  // URL format validation
  try {
    const url = new URL(trimmedUrl);
    
    // Must be HTTPS
    if (url.protocol !== 'https:') {
      errors.push('Return URL must use HTTPS');
    }

    // Must be from the same app domain
    const appUrl = process.env.SHOPIFY_APP_URL;
    if (appUrl) {
      const appDomain = new URL(appUrl).hostname;
      if (url.hostname !== appDomain) {
        errors.push('Return URL must be from the same app domain');
      }
    }

  } catch {
    errors.push('Return URL must be a valid URL');
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedUrl
  };
}

/**
 * Validate billing action
 */
export function validateBillingAction(action: unknown): ValidationResult {
  const errors: string[] = [];

  if (!action) {
    errors.push('Action is required');
    return { isValid: false, errors };
  }

  if (typeof action !== 'string') {
    errors.push('Action must be a string');
    return { isValid: false, errors };
  }

  const trimmedAction = action.trim();
  
  if (trimmedAction.length === 0) {
    errors.push('Action cannot be empty');
    return { isValid: false, errors };
  }

  // Valid billing actions
  const validActions = [
    'create_subscription',
    'cancel_subscription',
    'create_pay_per_use_purchase',
    'check_purchase_status'
  ];

  if (!validActions.includes(trimmedAction)) {
    errors.push(`Invalid action. Must be one of: ${validActions.join(', ')}`);
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedAction
  };
}

/**
 * Validate all billing form data
 */
export function validateBillingFormData(formData: FormData): ValidationResult {
  const errors: string[] = [];
  const sanitizedData: Record<string, any> = {};

  // Validate action
  const actionResult = validateBillingAction(formData.get('action'));
  if (!actionResult.isValid) {
    errors.push(...actionResult.errors);
    return { isValid: false, errors };
  }

  const action = actionResult.sanitizedValue;
  sanitizedData.action = action;

  // Validate based on action type
  switch (action) {
    case 'create_subscription': {
      const planResult = validatePlanId(formData.get('planId'));
      if (!planResult.isValid) {
        errors.push(...planResult.errors);
      } else {
        sanitizedData.planId = planResult.sanitizedValue;
      }
      break;
    }

    case 'cancel_subscription': {
      const subResult = validateSubscriptionId(formData.get('subscriptionId'));
      if (!subResult.isValid) {
        errors.push(...subResult.errors);
      } else {
        sanitizedData.subscriptionId = subResult.sanitizedValue;
      }
      break;
    }

    case 'create_pay_per_use_purchase': {
      const countResult = validateProductCount(formData.get('productCount'));
      if (!countResult.isValid) {
        errors.push(...countResult.errors);
      } else {
        sanitizedData.productCount = countResult.sanitizedValue;
      }

      const productsResult = validateSelectedProducts(
        formData.get('selectedProducts'),
        sanitizedData.productCount
      );
      if (!productsResult.isValid) {
        errors.push(...productsResult.errors);
      } else {
        sanitizedData.selectedProducts = productsResult.sanitizedValue;
      }
      break;
    }
  }

  if (errors.length > 0) {
    return { isValid: false, errors };
  }

  return {
    isValid: true,
    errors: [],
    sanitizedValue: sanitizedData
  };
}
