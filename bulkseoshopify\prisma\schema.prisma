// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// PostgreSQL database configuration for Neon
datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id                  String    @id
  shop                String    @unique
  state               String
  isOnline            Bo<PERSON>an   @default(false)
  scope               String?
  expires             DateTime?
  accessToken         String
  userId              String?
  firstName           String?
  lastName            String?
  email               String?
  accountOwner        Boolean   @default(false)
  locale              String?
  collaborator        <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified       Boolean?  @default(false)
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Billing fields
  subscriptionId      String?
  subscriptionStatus  String?
  billingPlanId       String?
  trialEndsAt         DateTime?
  lastBillingCheck    DateTime?

  // Relations
  billingSubscriptions BillingSubscription[]
  billingPurchases     BillingPurchase[]
  billingUsage         BillingUsage[]
  billingEvents        BillingEvent[]

  @@index([shop])
  @@index([expires])
}

model BillingSubscription {
  id                  Int       @id @default(autoincrement())
  shop                String
  subscriptionId      String    @unique
  planId              String    // 'annual', 'monthly', 'pay_per_use'
  status              String    // 'ACTIVE', 'PENDING', 'CANCELLED', 'EXPIRED'
  trialDays           Int       @default(0)
  trialEndsAt         DateTime?
  currentPeriodStart  DateTime?
  currentPeriodEnd    DateTime?
  priceAmount         Float?
  priceCurrency       String    @default("USD")
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relations
  session             Session   @relation(fields: [shop], references: [shop], onDelete: Cascade)

  @@index([shop])
  @@index([status])
}

model BillingPurchase {
  id                    Int       @id @default(autoincrement())
  shop                  String
  purchaseId            String    @unique
  status                String    // 'PENDING', 'ACCEPTED', 'DECLINED'
  productCount          Int
  amount                Float
  currency              String    @default("USD")
  description           String?
  optimizationBatchId   String?   // Link to optimization session
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt

  // Relations
  session               Session   @relation(fields: [shop], references: [shop], onDelete: Cascade)

  @@index([shop])
  @@index([status])
}

model BillingUsage {
  id                  Int       @id @default(autoincrement())
  shop                String
  billingType         String    // 'subscription', 'pay_per_use'
  billingReferenceId  String?   // subscription_id or purchase_id
  productsOptimized   Int
  optimizationDate    DateTime  @default(now())
  batchId             String?   // Group optimizations by batch

  // Relations
  session             Session   @relation(fields: [shop], references: [shop], onDelete: Cascade)

  @@index([shop])
  @@index([optimizationDate])
}

model BillingEvent {
  id            Int       @id @default(autoincrement())
  shop          String
  eventType     String    // 'subscription_created', 'subscription_cancelled', 'purchase_created', etc.
  referenceId   String?   // subscription_id or purchase_id
  eventData     String?   // JSON string
  createdAt     DateTime  @default(now())

  // Relations
  session       Session   @relation(fields: [shop], references: [shop], onDelete: Cascade)

  @@index([shop])
  @@index([eventType])
}

model CSRFToken {
  id        String   @id @default(cuid())
  token     String   @unique
  shop      String
  expires   DateTime
  createdAt DateTime @default(now())

  @@index([shop])
  @@index([expires])
  @@index([token])
}

model RateLimitEntry {
  id         String   @id @default(cuid())
  identifier String   @unique
  count      Int      @default(1)
  resetTime  DateTime
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([identifier])
  @@index([resetTime])
}

model CacheEntry {
  id        String   @id @default(cuid())
  key       String   @unique
  data      String   // JSON stringified data
  expiresAt DateTime
  tags      String?  // Comma-separated tags for cache invalidation
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
  @@index([expiresAt])
  @@index([tags])
}
