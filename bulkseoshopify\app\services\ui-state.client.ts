/**
 * UI State Management Service
 * Centralized state management for UI components and user interactions
 */

import React from "react";

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
}

export interface LoadingState {
  id: string;
  message: string;
  progress?: number;
  canCancel?: boolean;
  onCancel?: () => void;
}

export interface ModalState {
  id: string;
  type: 'confirmation' | 'form' | 'info' | 'custom';
  title: string;
  content?: string;
  component?: React.ComponentType<any>;
  props?: Record<string, any>;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
  size?: 'small' | 'medium' | 'large' | 'fullscreen';
}

export interface UIState {
  toasts: ToastMessage[];
  loadingStates: LoadingState[];
  modals: ModalState[];
  sidebarOpen: boolean;
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    count: number;
    items: Array<{
      id: string;
      title: string;
      message: string;
      timestamp: Date;
      read: boolean;
      type: 'info' | 'success' | 'warning' | 'error';
    }>;
  };
}

type UIStateListener = (state: UIState) => void;

/**
 * Centralized UI state management service
 */
export class UIStateService {
  private static instance: UIStateService;
  private state: UIState = {
    toasts: [],
    loadingStates: [],
    modals: [],
    sidebarOpen: true,
    theme: 'light',
    notifications: {
      count: 0,
      items: []
    }
  };
  private listeners: Set<UIStateListener> = new Set();

  static getInstance(): UIStateService {
    if (!UIStateService.instance) {
      UIStateService.instance = new UIStateService();
      UIStateService.instance.initializeFromStorage();
    }
    return UIStateService.instance;
  }

  /**
   * Subscribe to state changes
   */
  subscribe(listener: UIStateListener): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * Get current state
   */
  getState(): UIState {
    return { ...this.state };
  }

  /**
   * Toast management
   */
  showToast(toast: Omit<ToastMessage, 'id'>): string {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newToast: ToastMessage = {
      id,
      duration: 5000,
      ...toast
    };

    this.state.toasts.push(newToast);
    this.notifyListeners();

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        this.removeToast(id);
      }, newToast.duration);
    }

    return id;
  }

  removeToast(id: string): void {
    this.state.toasts = this.state.toasts.filter(toast => toast.id !== id);
    this.notifyListeners();
  }

  clearAllToasts(): void {
    this.state.toasts = [];
    this.notifyListeners();
  }

  /**
   * Loading state management
   */
  showLoading(loading: Omit<LoadingState, 'id'>): string {
    const id = `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newLoading: LoadingState = {
      id,
      ...loading
    };

    this.state.loadingStates.push(newLoading);
    this.notifyListeners();

    return id;
  }

  updateLoading(id: string, updates: Partial<Omit<LoadingState, 'id'>>): void {
    const index = this.state.loadingStates.findIndex(loading => loading.id === id);
    if (index !== -1) {
      this.state.loadingStates[index] = {
        ...this.state.loadingStates[index],
        ...updates
      };
      this.notifyListeners();
    }
  }

  hideLoading(id: string): void {
    this.state.loadingStates = this.state.loadingStates.filter(loading => loading.id !== id);
    this.notifyListeners();
  }

  clearAllLoading(): void {
    this.state.loadingStates = [];
    this.notifyListeners();
  }

  /**
   * Modal management
   */
  showModal(modal: Omit<ModalState, 'id'>): string {
    const id = `modal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newModal: ModalState = {
      id,
      size: 'medium',
      confirmText: 'Confirm',
      cancelText: 'Cancel',
      ...modal
    };

    this.state.modals.push(newModal);
    this.notifyListeners();

    return id;
  }

  hideModal(id: string): void {
    this.state.modals = this.state.modals.filter(modal => modal.id !== id);
    this.notifyListeners();
  }

  hideAllModals(): void {
    this.state.modals = [];
    this.notifyListeners();
  }

  /**
   * Confirmation modal helper
   */
  showConfirmation(
    title: string,
    message: string,
    onConfirm: () => void,
    options?: {
      confirmText?: string;
      cancelText?: string;
      type?: 'danger' | 'warning' | 'info';
    }
  ): string {
    return this.showModal({
      type: 'confirmation',
      title,
      content: message,
      onConfirm,
      confirmText: options?.confirmText,
      cancelText: options?.cancelText
    });
  }

  /**
   * Sidebar management
   */
  toggleSidebar(): void {
    this.state.sidebarOpen = !this.state.sidebarOpen;
    this.saveToStorage();
    this.notifyListeners();
  }

  setSidebarOpen(open: boolean): void {
    this.state.sidebarOpen = open;
    this.saveToStorage();
    this.notifyListeners();
  }

  /**
   * Theme management
   */
  setTheme(theme: 'light' | 'dark' | 'auto'): void {
    this.state.theme = theme;
    this.saveToStorage();
    this.applyTheme();
    this.notifyListeners();
  }

  /**
   * Notification management
   */
  addNotification(notification: {
    title: string;
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
  }): string {
    const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newNotification = {
      id,
      title: notification.title,
      message: notification.message,
      type: notification.type || 'info',
      timestamp: new Date(),
      read: false
    };

    this.state.notifications.items.unshift(newNotification);
    this.state.notifications.count = this.state.notifications.items.filter(n => !n.read).length;
    
    // Limit to 50 notifications
    if (this.state.notifications.items.length > 50) {
      this.state.notifications.items = this.state.notifications.items.slice(0, 50);
    }

    this.notifyListeners();
    return id;
  }

  markNotificationRead(id: string): void {
    const notification = this.state.notifications.items.find(n => n.id === id);
    if (notification && !notification.read) {
      notification.read = true;
      this.state.notifications.count = this.state.notifications.items.filter(n => !n.read).length;
      this.notifyListeners();
    }
  }

  markAllNotificationsRead(): void {
    this.state.notifications.items.forEach(n => n.read = true);
    this.state.notifications.count = 0;
    this.notifyListeners();
  }

  clearNotifications(): void {
    this.state.notifications.items = [];
    this.state.notifications.count = 0;
    this.notifyListeners();
  }

  /**
   * Utility methods
   */
  showSuccessToast(title: string, message?: string): string {
    return this.showToast({
      type: 'success',
      title,
      message
    });
  }

  showErrorToast(title: string, message?: string): string {
    return this.showToast({
      type: 'error',
      title,
      message,
      duration: 8000 // Longer duration for errors
    });
  }

  showWarningToast(title: string, message?: string): string {
    return this.showToast({
      type: 'warning',
      title,
      message
    });
  }

  showInfoToast(title: string, message?: string): string {
    return this.showToast({
      type: 'info',
      title,
      message
    });
  }

  /**
   * Private methods
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error('❌ UI state listener error:', error);
      }
    });
  }

  private initializeFromStorage(): void {
    try {
      const stored = localStorage.getItem('ui-state');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.state.sidebarOpen = parsed.sidebarOpen ?? true;
        this.state.theme = parsed.theme ?? 'light';
      }
      this.applyTheme();
    } catch (error) {
      console.warn('⚠️ Failed to load UI state from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      const toSave = {
        sidebarOpen: this.state.sidebarOpen,
        theme: this.state.theme
      };
      localStorage.setItem('ui-state', JSON.stringify(toSave));
    } catch (error) {
      console.warn('⚠️ Failed to save UI state to storage:', error);
    }
  }

  private applyTheme(): void {
    try {
      const root = document.documentElement;
      
      if (this.state.theme === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        root.classList.toggle('dark', prefersDark);
      } else {
        root.classList.toggle('dark', this.state.theme === 'dark');
      }
    } catch (error) {
      console.warn('⚠️ Failed to apply theme:', error);
    }
  }
}

// Export singleton instance
export const uiStateService = UIStateService.getInstance();

// React hook for using UI state
export function useUIState() {
  const [state, setState] = React.useState(uiStateService.getState());

  React.useEffect(() => {
    return uiStateService.subscribe(setState);
  }, []);

  return {
    state,
    showToast: uiStateService.showToast.bind(uiStateService),
    showSuccessToast: uiStateService.showSuccessToast.bind(uiStateService),
    showErrorToast: uiStateService.showErrorToast.bind(uiStateService),
    showWarningToast: uiStateService.showWarningToast.bind(uiStateService),
    showInfoToast: uiStateService.showInfoToast.bind(uiStateService),
    removeToast: uiStateService.removeToast.bind(uiStateService),
    showLoading: uiStateService.showLoading.bind(uiStateService),
    updateLoading: uiStateService.updateLoading.bind(uiStateService),
    hideLoading: uiStateService.hideLoading.bind(uiStateService),
    showModal: uiStateService.showModal.bind(uiStateService),
    hideModal: uiStateService.hideModal.bind(uiStateService),
    showConfirmation: uiStateService.showConfirmation.bind(uiStateService),
    toggleSidebar: uiStateService.toggleSidebar.bind(uiStateService),
    setSidebarOpen: uiStateService.setSidebarOpen.bind(uiStateService),
    setTheme: uiStateService.setTheme.bind(uiStateService),
    addNotification: uiStateService.addNotification.bind(uiStateService),
    markNotificationRead: uiStateService.markNotificationRead.bind(uiStateService),
    markAllNotificationsRead: uiStateService.markAllNotificationsRead.bind(uiStateService)
  };
}
