import db from "../db.server";
import { getEnvironmentConfig } from "../utils/env-validation.server";
import { getCachedBillingStatus, getCachedSubscriptionData, getCachedPurchaseData, invalidateBillingCache } from "../utils/cache.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import type { Prisma } from "@prisma/client";

export interface BillingPlan {
  id: string;
  name: string;
  type: 'annual' | 'monthly' | 'pay_per_use';
  price: number;
  interval?: 'ANNUAL' | 'EVERY_30_DAYS';
  description: string;
  features: string[];
  recommended?: boolean;
}

export const BILLING_PLANS: BillingPlan[] = [
  {
    id: 'annual',
    name: 'Annual Subscription',
    type: 'annual',
    price: 199.99,
    interval: 'ANNUAL',
    description: 'Best Value - Save $40/year',
    features: [
      'Unlimited product optimizations',
      'Priority support',
      'Advanced analytics',
      'Bulk operations'
    ],
    recommended: true
  },
  {
    id: 'monthly',
    name: 'Monthly Subscription',
    type: 'monthly',
    price: 19.99,
    interval: 'EVERY_30_DAYS',
    description: 'Flexible monthly billing',
    features: [
      'Unlimited product optimizations',
      'Standard support',
      'Basic analytics',
      'Bulk operations'
    ]
  },
  {
    id: 'pay_per_use',
    name: 'Pay-Per-Use',
    type: 'pay_per_use',
    price: 0.10,
    description: 'Pay only for what you use',
    features: [
      '$0.10 per product optimized',
      'No monthly commitment',
      'Pre-payment required',
      'Standard support',
      'Basic analytics'
    ]
  }
];

export interface SubscriptionData {
  id: string;
  status: string;
  name: string;
  trialDays?: number;
  currentPeriodEnd?: string;
  lineItems: Array<{
    id: string;
    plan: {
      pricingDetails: any;
    };
  }>;
}

export interface OneTimePurchaseData {
  id: string;
  status: string;
  name: string;
  price: {
    amount: number;
    currencyCode: string;
  };
}

export class BillingService {
  private admin: any;
  private shop: string;

  constructor(admin: any, shop?: string) {
    // Validate inputs
    if (!admin) {
      throw new Error('Admin GraphQL client is required for billing operations');
    }

    if (!shop || typeof shop !== 'string' || shop.trim() === '') {
      throw new Error('Valid shop domain is required for billing operations');
    }

    this.admin = admin;
    this.shop = shop.trim();

    // Validate environment on construction
    this.validateEnvironment();

    console.log(`🏪 BillingService initialized for shop: ${this.shop}`);
  }

  private validateEnvironment() {
    try {
      getEnvironmentConfig(); // Validate environment config

      // Additional billing-specific validations
      if (!this.admin.graphql) {
        throw new Error('Admin client must have GraphQL capability');
      }

      console.log(`✅ BillingService environment validated for ${this.shop}`);
    } catch (error) {
      console.error(`❌ BillingService environment validation failed for ${this.shop}:`, error);
      throw error;
    }
  }

  /**
   * Execute GraphQL query with retry logic, rate limiting, and proper error handling
   */
  private async executeGraphQLWithRetry(
    query: string,
    variables: any,
    maxRetries: number = 3,
    context?: ErrorContext
  ): Promise<any> {
    // Apply rate limiting for billing API calls
    await applyRateLimit(RATE_LIMITERS.API_BILLING(this.shop), context);

    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 GraphQL attempt ${attempt}/${maxRetries} for shop: ${this.shop}`);

        const response = await this.admin.graphql(query, { variables });
        const result = await response.json();

        // Log the full response for debugging
        console.log(`📋 GraphQL Response (attempt ${attempt}):`, JSON.stringify(result, null, 2));

        // Check for GraphQL errors
        if (result.errors && result.errors.length > 0) {
          const error = result.errors[0];
          throw new Error(`GraphQL Error: ${error.message} (Code: ${error.extensions?.code || 'UNKNOWN'})`);
        }

        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`❌ GraphQL attempt ${attempt} failed:`, lastError.message);

        // Don't retry on certain errors
        if (lastError.message.includes('Invalid') ||
            lastError.message.includes('Unauthorized') ||
            lastError.message.includes('Forbidden')) {
          throw lastError;
        }

        // Log the error with context
        await logError(lastError, {
          ...context,
          shop: this.shop,
          action: 'graphql_query',
          metadata: {
            attempt: attempt + 1,
            maxRetries,
            query: query.substring(0, 100) + '...',
            variables: JSON.stringify(variables).substring(0, 200)
          }
        });

        // Wait before retry (exponential backoff)
        if (attempt < maxRetries) {
          const delay = Math.pow(2, attempt) * 1000; // 2s, 4s, 8s
          console.log(`⏳ Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // Create enhanced error for final failure
    const enhancedError = createError('API_SHOPIFY_ERROR', {
      ...context,
      shop: this.shop,
      action: 'graphql_query_final_failure',
      metadata: {
        maxRetries,
        query: query.substring(0, 100) + '...',
        lastErrorMessage: lastError?.message
      }
    }, lastError || undefined);

    throw enhancedError;
  }

  // Create annual or monthly subscription
  async createSubscription(planId: string, returnUrl: string): Promise<any> {
    console.log(`🔄 Creating subscription for shop: ${this.shop}, plan: ${planId}`);

    // Validate inputs
    if (!planId || typeof planId !== 'string') {
      throw new Error('Valid plan ID is required');
    }

    if (!returnUrl || typeof returnUrl !== 'string') {
      throw new Error('Valid return URL is required');
    }

    // Validate return URL format
    try {
      new URL(returnUrl);
    } catch {
      throw new Error('Return URL must be a valid URL');
    }

    const plan = BILLING_PLANS.find(p => p.id === planId);
    if (!plan || plan.type === 'pay_per_use') {
      throw new Error(`Invalid subscription plan: ${planId}. Available plans: ${BILLING_PLANS.filter(p => p.type !== 'pay_per_use').map(p => p.id).join(', ')}`);
    }

    if (!this.shop) {
      throw new Error('Shop is required for subscription creation');
    }

    console.log('🔄 Creating subscription with variables (TEST MODE ENABLED):', {
      name: `AI BULK SEO - ${plan.name}`,
      returnUrl,
      test: process.env.NODE_ENV !== 'production',
      lineItems: [{
        plan: {
          appRecurringPricingDetails: {
            price: {
              amount: plan.price,
              currencyCode: "USD"
            },
            interval: plan.interval
          }
        }
      }]
    });

    const result = await this.executeGraphQLWithRetry(`
      mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $test: Boolean) {
        appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, test: $test) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
            name
            currentPeriodEnd
            lineItems {
              id
              plan {
                pricingDetails {
                  __typename
                }
              }
            }
          }
          confirmationUrl
        }
      }
    `, {
      name: `AI BULK SEO - ${plan.name}`,
      returnUrl,
      test: true, // Enable Shopify test transactions
      lineItems: [{
        plan: {
          appRecurringPricingDetails: {
            price: {
              amount: plan.price,
              currencyCode: "USD"
            },
            interval: plan.interval
          }
        }
      }]
    }, 3, { shop: this.shop, action: 'create_subscription' });

    console.log('📋 GraphQL Response:', JSON.stringify(result, null, 2));

    // Check for GraphQL errors
    if (result.data?.appSubscriptionCreate?.userErrors?.length > 0) {
      const error = result.data.appSubscriptionCreate.userErrors[0];
      console.error('❌ Subscription creation error:', error);
      throw new Error(`Subscription creation failed: ${error.message} (Field: ${error.field})`);
    }

    if (!result.data?.appSubscriptionCreate?.appSubscription) {
      throw new Error('Subscription creation failed: No subscription returned from Shopify');
    }

    if (!result.data.appSubscriptionCreate.confirmationUrl) {
      throw new Error('Subscription creation failed: No confirmation URL returned from Shopify');
    }

    // Store subscription in database if successful
    if (result.data?.appSubscriptionCreate?.appSubscription && this.shop) {
      const subscription = result.data.appSubscriptionCreate.appSubscription;
      try {
        // Use database transaction
        await db.$transaction(async (tx: Prisma.TransactionClient) => {
            // Create billing subscription record
            const billingSubscription = await tx.billingSubscription.create({
              data: {
                shop: this.shop!,
                subscriptionId: subscription.id,
                planId: planId,
                status: subscription.status,
                trialDays: 0,
                trialEndsAt: null,
                currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
                priceAmount: plan.price,
                priceCurrency: 'USD'
              }
            });

            // Log billing event
            await tx.billingEvent.create({
              data: {
                shop: this.shop!,
                eventType: 'subscription_created',
                referenceId: subscription.id,
                eventData: JSON.stringify(subscription)
              }
            });

            // Update session with subscription info
            await tx.session.update({
              where: { shop: this.shop! },
              data: {
                subscriptionId: subscription.id,
                subscriptionStatus: subscription.status,
                billingPlanId: planId,
                trialEndsAt: null,
                lastBillingCheck: new Date()
              }
            });

            return billingSubscription;
        });

        console.log('✅ Subscription stored in database for shop:', this.shop);
      } catch (dbError) {
        console.error('Failed to store subscription in database:', dbError);
        // Don't fail the whole operation if database fails
      }
    }

    return result;
  }

  /**
   * Create one-time purchase for pay-per-use with proper validation
   */
  async createOneTimePurchase(productCount: number, returnUrl: string): Promise<any> {
    console.log(`🔄 Creating one-time purchase for shop: ${this.shop}, products: ${productCount}`);

    // Validate input parameters
    if (!productCount || typeof productCount !== 'number' || productCount <= 0) {
      throw new Error('Product count must be a positive number');
    }

    if (productCount > 1000) {
      throw new Error('Product count cannot exceed 1000 per purchase');
    }

    if (!returnUrl || typeof returnUrl !== 'string') {
      throw new Error('Valid return URL is required');
    }

    // Validate return URL format
    try {
      new URL(returnUrl);
    } catch {
      throw new Error('Return URL must be a valid URL');
    }

    if (!this.shop) {
      throw new Error('Shop is required for purchase creation');
    }

    const amount = (productCount * 0.10).toFixed(2);

    console.log(`💰 Creating purchase (TEST MODE ENABLED): ${productCount} products at $0.10 each = $${amount}`);

    const result = await this.executeGraphQLWithRetry(`
      mutation AppPurchaseOneTimeCreate($name: String!, $price: MoneyInput!, $returnUrl: URL!, $test: Boolean) {
        appPurchaseOneTimeCreate(name: $name, price: $price, returnUrl: $returnUrl, test: $test) {
          userErrors {
            field
            message
          }
          appPurchaseOneTime {
            id
            status
            name
            price {
              amount
              currencyCode
            }
            createdAt
          }
          confirmationUrl
        }
      }
    `, {
      name: `AI BULK SEO - ${productCount} Product${productCount > 1 ? 's' : ''} Optimization`,
      price: {
        amount: parseFloat(amount),
        currencyCode: "USD"
      },
      returnUrl,
      test: true // Enable Shopify test transactions
    }, 3, { shop: this.shop, action: 'create_one_time_purchase' });

    // Check for GraphQL errors
    if (result.data?.appPurchaseOneTimeCreate?.userErrors?.length > 0) {
      const error = result.data.appPurchaseOneTimeCreate.userErrors[0];
      throw new Error(`Purchase creation failed: ${error.message} (Field: ${error.field})`);
    }

    if (!result.data?.appPurchaseOneTimeCreate?.appPurchaseOneTime) {
      throw new Error('Purchase creation failed: No purchase returned from Shopify');
    }

    if (!result.data.appPurchaseOneTimeCreate.confirmationUrl) {
      throw new Error('Purchase creation failed: No confirmation URL returned from Shopify');
    }

    // Store purchase in database if successful
    if (result.data?.appPurchaseOneTimeCreate?.appPurchaseOneTime && this.shop) {
      const purchase = result.data.appPurchaseOneTimeCreate.appPurchaseOneTime;
      try {
        // Use database transaction
        await db.$transaction(async (tx: Prisma.TransactionClient) => {
            // Create billing purchase record
            const billingPurchase = await tx.billingPurchase.create({
              data: {
                shop: this.shop!,
                purchaseId: purchase.id,
                status: purchase.status,
                productCount: productCount,
                amount: parseFloat(amount),
                currency: 'USD',
                description: purchase.name
              }
            });

            // Log billing event
            await tx.billingEvent.create({
              data: {
                shop: this.shop!,
                eventType: 'purchase_created',
                referenceId: purchase.id,
                eventData: JSON.stringify(purchase)
              }
            });

            return billingPurchase;
        });

        console.log('✅ Purchase stored in database for shop:', this.shop);
      } catch (dbError) {
        console.error('Failed to store purchase in database:', dbError);
        // Don't fail the whole operation if database fails
      }

      // Invalidate cache after successful purchase creation
      invalidateBillingCache(this.shop, 'purchase');
    }

    console.log(`✅ Pay-per-use purchase created successfully for shop: ${this.shop}`);
    return result;
  }

  // Get current subscription status with caching
  async getCurrentSubscription(): Promise<any> {
    return getCachedSubscriptionData(this.shop, async () => {
      try {
        // Try the enhanced query first
        const response = await this.admin.graphql(`
          query {
            currentAppInstallation {
              activeSubscriptions {
                id
                name
                status
                trialDays
                currentPeriodEnd
                createdAt
                lineItems {
                  id
                  plan {
                    pricingDetails {
                      __typename
                      price {
                        amount
                        currencyCode
                      }
                      interval
                    }
                  }
                }
              }
            }
          }
        `);

        return response.json();
      } catch (error) {
        console.log(`⚠️ Enhanced query failed, falling back to basic query:`, error instanceof Error ? error.message : String(error));

        // Fallback to basic query without pricing details
        const response = await this.admin.graphql(`
          query {
            currentAppInstallation {
              activeSubscriptions {
                id
                name
                status
                trialDays
                currentPeriodEnd
                createdAt
                lineItems {
                  id
                  plan {
                    pricingDetails {
                      __typename
                    }
                  }
                }
              }
            }
          }
        `);

        return response.json();
      }
    });
  }

  // Get recent one-time purchases with caching
  async getOneTimePurchases(): Promise<any> {
    return getCachedPurchaseData(this.shop, async () => {
      const response = await this.admin.graphql(`
        query {
          currentAppInstallation {
            oneTimePurchases(first: 10) {
              edges {
                node {
                  id
                  name
                  status
                  price {
                    amount
                    currencyCode
                  }
                  createdAt
                }
              }
            }
          }
        }
      `);

      return response.json();
    });
  }

  /**
   * Cancel subscription with proper validation and error handling
   */
  async cancelSubscription(subscriptionId: string): Promise<any> {
    console.log(`🔄 Cancelling subscription for shop: ${this.shop}, subscription: ${subscriptionId}`);

    // Validate inputs
    if (!subscriptionId || typeof subscriptionId !== 'string') {
      throw new Error('Valid subscription ID is required');
    }

    if (!subscriptionId.startsWith('gid://shopify/AppSubscription/')) {
      throw new Error('Invalid subscription ID format');
    }

    const response = await this.executeGraphQLWithRetry(`
      mutation AppSubscriptionCancel($id: ID!) {
        appSubscriptionCancel(id: $id) {
          userErrors {
            field
            message
          }
          appSubscription {
            id
            status
            name
            currentPeriodEnd
          }
        }
      }
    `, {
      id: subscriptionId
    }, 3, { shop: this.shop, action: 'cancel_subscription' });

    console.log(`✅ Subscription cancellation completed for shop: ${this.shop}`);
    return response;
  }

  /**
   * Check if user has active billing with comprehensive logic and caching
   * Handles subscriptions and pay-per-use purchases
   */
  async hasActiveBilling(): Promise<{ hasAccess: boolean; plan?: BillingPlan; subscription?: SubscriptionData }> {
    console.log(`🔍 Checking billing status for shop: ${this.shop}`);

    // Use cached billing status to reduce API calls
    return getCachedBillingStatus(this.shop, async () => {
      try {
        // Check for active subscriptions first
        const subscriptionData = await this.getCurrentSubscription();
        const allSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];

        // Filter out cancelled and expired subscriptions
        const activeSubscriptions = allSubscriptions.filter((sub: any) =>
          sub.status === 'ACTIVE' || sub.status === 'PENDING'
        );

        console.log(`📊 Found ${allSubscriptions.length} total subscriptions, ${activeSubscriptions.length} truly active`);

        // Also check database for subscription info as fallback
        const dbSubscription = await db.billingSubscription.findFirst({
          where: {
            shop: this.shop,
            status: { in: ['ACTIVE', 'PENDING'] }
          },
          orderBy: { updatedAt: 'desc' }
        });

        const dbSession = await db.session.findUnique({
          where: { shop: this.shop },
          select: {
            subscriptionId: true,
            subscriptionStatus: true,
            billingPlanId: true,
            trialEndsAt: true
          }
        });

        console.log(`📋 Database subscription:`, dbSubscription ? {
          id: dbSubscription.subscriptionId,
          status: dbSubscription.status,
          planId: dbSubscription.planId
        } : 'none');

        console.log(`📋 Database session:`, dbSession ? {
          subscriptionId: dbSession.subscriptionId,
          status: dbSession.subscriptionStatus,
          planId: dbSession.billingPlanId
        } : 'none');

      if (activeSubscriptions.length > 0) {
        const subscription = activeSubscriptions[0];
        console.log(`📋 Subscription status: ${subscription.status}`);

        // Determine plan type from subscription with better logic
        let plan: BillingPlan | undefined;
        const lineItem = subscription.lineItems?.[0];

        console.log(`🔍 Subscription lineItems:`, JSON.stringify(subscription.lineItems, null, 2));

        // Enhanced plan detection with multiple fallbacks
        let planDetected = false;

        // Method 1: Try to get plan from Shopify API pricing details
        if (lineItem?.plan?.pricingDetails?.interval) {
          const interval = lineItem.plan.pricingDetails.interval;
          const amount = parseFloat(lineItem.plan.pricingDetails.price?.amount || '0');

          console.log(`📋 Plan details from API: interval=${interval}, amount=${amount}`);

          if (interval === 'ANNUAL') {
            plan = BILLING_PLANS.find(p => p.id === 'annual');
            planDetected = true;
          } else if (interval === 'EVERY_30_DAYS') {
            plan = BILLING_PLANS.find(p => p.id === 'monthly');
            planDetected = true;
          }

          console.log(`💰 Detected plan from API: ${plan?.name || 'unknown'} (${interval}, $${amount})`);
        }

        // Method 2: Try to determine from subscription name
        if (!planDetected && subscription.name) {
          const name = subscription.name.toLowerCase();
          console.log(`📋 Analyzing subscription name: "${subscription.name}"`);

          if (name.includes('annual') || name.includes('yearly') || name.includes('year')) {
            plan = BILLING_PLANS.find(p => p.id === 'annual');
            planDetected = true;
            console.log(`💰 Detected annual plan from name: ${subscription.name}`);
          } else if (name.includes('monthly') || name.includes('month')) {
            plan = BILLING_PLANS.find(p => p.id === 'monthly');
            planDetected = true;
            console.log(`💰 Detected monthly plan from name: ${subscription.name}`);
          }
        }

        // Method 3: Fallback to database information
        if (!planDetected) {
          console.log(`❌ No plan detected from API, trying database fallback`);

          if (dbSubscription?.planId) {
            plan = BILLING_PLANS.find(p => p.id === dbSubscription.planId);
            planDetected = true;
            console.log(`💰 Using plan from database: ${plan?.name || 'unknown'} (${dbSubscription.planId})`);
          } else if (dbSession?.billingPlanId && dbSession.billingPlanId !== 'pay_per_use') {
            plan = BILLING_PLANS.find(p => p.id === dbSession.billingPlanId);
            planDetected = true;
            console.log(`💰 Using plan from session: ${plan?.name || 'unknown'} (${dbSession.billingPlanId})`);
          }
        }

        // Method 4: Default to monthly if we have an active subscription but no plan detected
        if (!planDetected && subscription.status === 'ACTIVE') {
          console.log(`⚠️ Active subscription found but no plan detected, defaulting to monthly`);
          plan = BILLING_PLANS.find(p => p.id === 'monthly');
        }

        // Determine access based on subscription status
        const hasAccess = subscription.status === 'ACTIVE';

        // If we couldn't detect the plan but have an active subscription, create a fallback plan
        if (!plan && subscription.status === 'ACTIVE') {
          console.log(`⚠️ Creating fallback plan for active subscription`);
          const lineItem = subscription.lineItems?.[0];
          if (lineItem?.plan?.pricingDetails) {
            const interval = lineItem.plan.pricingDetails.interval;
            const amount = parseFloat(lineItem.plan.pricingDetails.price?.amount || '0');

            plan = {
              id: interval === 'ANNUAL' ? 'annual' : 'monthly',
              name: interval === 'ANNUAL' ? 'Annual Plan' : 'Monthly Plan',
              description: interval === 'ANNUAL' ? 'Annual subscription with unlimited access' : 'Monthly subscription with unlimited access',
              price: amount,
              type: interval === 'ANNUAL' ? 'annual' : 'monthly',
              features: [
                'Unlimited product optimizations',
                'Advanced SEO analysis',
                'Bulk optimization tools',
                'Priority support'
              ]
            };
            console.log(`✅ Created fallback plan: ${plan?.name} ($${plan?.price})`);
          }
        }

        console.log(`✅ Billing check result: hasAccess=${hasAccess}, plan=${plan?.name || 'none'}`);

        return {
          hasAccess,
          plan,
          subscription
        };
      }

      // If no active subscriptions, check for recent pay-per-use purchases
      console.log(`🔍 No active subscriptions, checking pay-per-use purchases...`);

      try {
        const purchaseData = await this.getOneTimePurchases();
        const purchases = purchaseData.data?.currentAppInstallation?.oneTimePurchases?.edges || [];

        console.log(`💳 Found ${purchases.length} one-time purchases`);

        // Check for recent purchases (within last 30 days) - include PENDING for immediate access
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const recentPurchases = purchases.filter((edge: any) => {
          const purchase = edge.node;
          const createdAt = new Date(purchase.createdAt);
          return (purchase.status === 'ACCEPTED' || purchase.status === 'PENDING') && createdAt > thirtyDaysAgo;
        });

        if (recentPurchases.length > 0) {
          const payPerUsePlan = BILLING_PLANS.find(p => p.id === 'pay_per_use');
          console.log(`✅ Found ${recentPurchases.length} recent pay-per-use purchases`);
          console.log('📋 Recent purchases:', recentPurchases.map((edge: any) => ({
            status: edge.node.status,
            createdAt: edge.node.createdAt,
            amount: edge.node.amount
          })));
          console.log('🎯 Returning pay-per-use plan:', payPerUsePlan);

          return {
            hasAccess: true,
            plan: payPerUsePlan
          };
        }
      } catch (purchaseError) {
        console.error('❌ Error checking pay-per-use purchases:', purchaseError);
        // Continue to return no access rather than throwing
      }

        // Check database for subscription info even if Shopify API doesn't return active subscriptions
        if (dbSubscription && dbSubscription.status === 'ACTIVE') {
          console.log(`🔄 Found active subscription in database: ${dbSubscription.subscriptionId}`);

          const plan = BILLING_PLANS.find(p => p.id === dbSubscription.planId);
          const hasAccess = dbSubscription.status === 'ACTIVE';

          console.log(`✅ Using database subscription data: plan=${plan?.name}, hasAccess=${hasAccess}`);

          return {
            hasAccess: Boolean(hasAccess),
            plan: plan || BILLING_PLANS.find(p => p.id === 'monthly'),
            subscription: {
              id: dbSubscription.subscriptionId,
              status: dbSubscription.status,
              name: `AI BULK SEO - ${plan?.name || 'Subscription'}`,
              currentPeriodEnd: dbSubscription.currentPeriodEnd instanceof Date
                ? dbSubscription.currentPeriodEnd.toISOString()
                : typeof dbSubscription.currentPeriodEnd === 'string'
                  ? dbSubscription.currentPeriodEnd
                  : undefined,
              lineItems: []
            }
          };
        }

        console.log(`❌ No active billing found for shop: ${this.shop}`);
        // Return pay-per-use plan as default so users can purchase credits
        const payPerUsePlan = BILLING_PLANS.find(p => p.id === 'pay_per_use');
        console.log('🎯 Returning default pay-per-use plan for credit purchases');
        return {
          hasAccess: false,
          plan: payPerUsePlan
        };

      } catch (error) {
        console.error(`❌ Error checking billing status for shop ${this.shop}:`, error);
        return { hasAccess: false };
      }
    });
  }

  /**
   * Calculate pay-per-use cost with validation
   */
  calculatePayPerUseCost(productCount: number): number {
    if (!productCount || productCount <= 0) {
      throw new Error('Product count must be greater than 0');
    }

    if (productCount > 1000) {
      throw new Error('Product count cannot exceed 1000 per purchase');
    }

    const costPerProduct = 0.10; // $0.10 per product
    const totalCost = productCount * costPerProduct;

    console.log(`💰 Pay-per-use cost calculation: ${productCount} products × $${costPerProduct} = $${totalCost.toFixed(2)}`);

    return parseFloat(totalCost.toFixed(2));
  }

  /**
   * Track usage for billing purposes
   */
  async trackUsage(billingType: 'subscription' | 'pay_per_use', productsOptimized: number, billingReferenceId?: string, batchId?: string): Promise<void> {
    try {
      console.log(`📊 Tracking usage: ${billingType}, ${productsOptimized} products optimized`);

      // Check if db models are available (Prisma client generated properly)
      if (db && typeof (db as any).billingUsage?.create === 'function') {
        await (db as any).billingUsage.create({
          data: {
            shop: this.shop,
            billingType,
            billingReferenceId,
            productsOptimized,
            batchId,
            optimizationDate: new Date()
          }
        });
      } else {
        console.warn('⚠️ Database models not available, skipping usage tracking');
      }

      console.log(`✅ Usage tracked for shop: ${this.shop}`);
    } catch (error) {
      console.error(`❌ Failed to track usage for shop ${this.shop}:`, error);
      // Don't throw error as this shouldn't break the main flow
    }
  }

  /**
   * Get usage statistics for the shop
   */
  async getUsageStats(days: number = 30): Promise<{ totalOptimized: number; byType: Record<string, number> }> {
    try {
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      // Check if db models are available (Prisma client generated properly)
      if (db && typeof (db as any).billingUsage?.findMany === 'function') {
        const usage = await (db as any).billingUsage.findMany({
          where: {
            shop: this.shop,
            optimizationDate: {
              gte: startDate
            }
          }
        });

        const totalOptimized = usage.reduce((sum: number, record: any) => sum + record.productsOptimized, 0);
        const byType = usage.reduce((acc: any, record: any) => {
          acc[record.billingType] = (acc[record.billingType] || 0) + record.productsOptimized;
          return acc;
        }, {} as Record<string, number>);

        console.log(`📈 Usage stats for ${days} days: ${totalOptimized} total products optimized`);
        return { totalOptimized, byType };
      } else {
        console.warn('⚠️ Database models not available, returning empty usage stats');
        return { totalOptimized: 0, byType: {} };
      }
    } catch (error) {
      console.error(`❌ Failed to get usage stats for shop ${this.shop}:`, error);
      return { totalOptimized: 0, byType: {} };
    }
  }

  /**
   * Get all billing plans
   */
  getAllBillingPlans(): BillingPlan[] {
    return BILLING_PLANS;
  }

  /**
   * Get billing plan by ID with validation
   */
  getBillingPlan(planId: string): BillingPlan | undefined {
    if (!planId || typeof planId !== 'string') {
      return undefined;
    }

    return BILLING_PLANS.find(p => p.id === planId);
  }


}
