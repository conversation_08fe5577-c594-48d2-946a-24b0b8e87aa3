/**
 * Database Management Service
 * Handles database operations, migrations, and health checks
 */

import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";

export interface DatabaseHealthCheck {
  isHealthy: boolean;
  connectionTime: number;
  error?: string;
  details: {
    canConnect: boolean;
    canQuery: boolean;
    canWrite: boolean;
    modelsAvailable: boolean;
  };
}

export interface DatabaseStats {
  sessions: number;
  billingSubscriptions: number;
  billingPurchases: number;
  billingUsage: number;
  billingEvents: number;
  csrfTokens: number;
  rateLimitEntries: number;
}

/**
 * Database Management Service
 */
export class DatabaseService {
  private static instance: DatabaseService;

  static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  /**
   * Perform comprehensive database health check
   */
  async healthCheck(context?: ErrorContext): Promise<DatabaseHealthCheck> {
    const startTime = Date.now();
    const result: DatabaseHealthCheck = {
      isHealthy: false,
      connectionTime: 0,
      details: {
        canConnect: false,
        canQuery: false,
        canWrite: false,
        modelsAvailable: false
      }
    };

    try {
      // Test connection
      await db.$connect();
      result.details.canConnect = true;
      console.log('✅ Database connection successful');

      // Test query capability
      const sessionCount = await db.session.count();
      result.details.canQuery = true;
      console.log(`✅ Database query successful (${sessionCount} sessions)`);

      // Test write capability with a safe operation
      const testId = `health_check_${Date.now()}`;
      await db.cSRFToken.create({
        data: {
          id: testId,
          token: 'health_check_token',
          shop: 'health_check_shop',
          expires: new Date(Date.now() + 1000) // Expires in 1 second
        }
      });

      // Clean up test record
      await db.cSRFToken.delete({
        where: { id: testId }
      });

      result.details.canWrite = true;
      console.log('✅ Database write test successful');

      // Check if all models are available
      const modelChecks = await Promise.allSettled([
        db.session.findFirst(),
        db.billingSubscription.findFirst(),
        db.billingPurchase.findFirst(),
        db.billingUsage.findFirst(),
        db.billingEvent.findFirst(),
        db.cSRFToken.findFirst(),
        db.rateLimitEntry.findFirst()
      ]);

      const allModelsWork = modelChecks.every(check => check.status === 'fulfilled');
      result.details.modelsAvailable = allModelsWork;

      if (allModelsWork) {
        console.log('✅ All database models are available');
      } else {
        console.warn('⚠️ Some database models may not be available');
      }

      result.isHealthy = result.details.canConnect && 
                        result.details.canQuery && 
                        result.details.canWrite && 
                        result.details.modelsAvailable;

    } catch (error) {
      console.error('❌ Database health check failed:', error);
      result.error = error instanceof Error ? error.message : 'Unknown error';
      
      await logError(
        error instanceof Error ? error : new Error('Database health check failed'),
        { ...context, action: 'database_health_check' }
      );
    } finally {
      result.connectionTime = Date.now() - startTime;
      await db.$disconnect();
    }

    return result;
  }

  /**
   * Get database statistics
   */
  async getStats(context?: ErrorContext): Promise<DatabaseStats> {
    try {
      const [
        sessions,
        billingSubscriptions,
        billingPurchases,
        billingUsage,
        billingEvents,
        csrfTokens,
        rateLimitEntries
      ] = await Promise.all([
        db.session.count(),
        db.billingSubscription.count(),
        db.billingPurchase.count(),
        db.billingUsage.count(),
        db.billingEvent.count(),
        db.cSRFToken.count(),
        db.rateLimitEntry.count()
      ]);

      return {
        sessions,
        billingSubscriptions,
        billingPurchases,
        billingUsage,
        billingEvents,
        csrfTokens,
        rateLimitEntries
      };
    } catch (error) {
      console.error('❌ Failed to get database stats:', error);
      await logError(
        error instanceof Error ? error : new Error('Database stats failed'),
        { ...context, action: 'database_stats' }
      );

      // Return zero stats on error
      return {
        sessions: 0,
        billingSubscriptions: 0,
        billingPurchases: 0,
        billingUsage: 0,
        billingEvents: 0,
        csrfTokens: 0,
        rateLimitEntries: 0
      };
    }
  }

  /**
   * Clean up expired records across all tables
   */
  async cleanupExpiredRecords(context?: ErrorContext): Promise<void> {
    try {
      console.log('🧹 Starting database cleanup...');

      // Execute cleanup operations sequentially
      console.log('🧹 Cleaning up expired sessions...');
      const expiredSessions = await db.session.deleteMany({
        where: {
          expires: {
            lt: new Date()
          }
        }
      });

      console.log('🧹 Cleaning up expired CSRF tokens...');
      const expiredCsrfTokens = await db.cSRFToken.deleteMany({
        where: {
          expires: {
            lt: new Date()
          }
        }
      });

      console.log('🧹 Cleaning up expired rate limit entries...');
      const expiredRateLimits = await db.rateLimitEntry.deleteMany({
        where: {
          resetTime: {
            lt: new Date()
          }
        }
      });

      console.log('🧹 Cleaning up old billing events...');
      const oldBillingEvents = await db.billingEvent.deleteMany({
        where: {
          createdAt: {
            lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          }
        }
      });

      const results = {
        expiredSessions: expiredSessions.count || 0,
        expiredCsrfTokens: expiredCsrfTokens.count || 0,
        expiredRateLimits: expiredRateLimits.count || 0,
        oldBillingEvents: oldBillingEvents.count || 0
      };

      console.log(`🧹 Database cleanup completed:`, results);
      
      const totalCleaned = Object.values(results).reduce((sum, count) => sum + count, 0);
      if (totalCleaned > 0) {
        console.log(`🧹 Total records cleaned: ${totalCleaned}`);
      }

    } catch (error) {
      console.error('❌ Database cleanup failed:', error);
      await logError(
        error instanceof Error ? error : new Error('Database cleanup failed'),
        { ...context, action: 'database_cleanup' }
      );
    }
  }

  /**
   * Optimize database performance
   */
  async optimizeDatabase(context?: ErrorContext): Promise<void> {
    try {
      console.log('⚡ Starting database optimization...');

      // For SQLite, run VACUUM and ANALYZE
      await db.$executeRaw`VACUUM`;
      await db.$executeRaw`ANALYZE`;

      console.log('⚡ Database optimization completed');
    } catch (error) {
      console.error('❌ Database optimization failed:', error);
      await logError(
        error instanceof Error ? error : new Error('Database optimization failed'),
        { ...context, action: 'database_optimization' }
      );
    }
  }

  /**
   * Backup database (PostgreSQL)
   */
  async backupDatabase(backupPath?: string, context?: ErrorContext): Promise<string | null> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const defaultBackupPath = `./backups/database-backup-${timestamp}.sql`;
      const finalBackupPath = backupPath || defaultBackupPath;

      // For PostgreSQL, we would use pg_dump (not available in serverless)
      console.log(`💾 PostgreSQL backup would be created: ${finalBackupPath}`);
      console.log('⚠️ Note: Use pg_dump for actual PostgreSQL backups');
      return finalBackupPath;
    } catch (error) {
      console.error('❌ Database backup failed:', error);
      await logError(
        error instanceof Error ? error : new Error('Database backup failed'),
        { ...context, action: 'database_backup' }
      );
      return null;
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();

// Schedule periodic cleanup (every 6 hours)
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    databaseService.cleanupExpiredRecords();
  }, 6 * 60 * 60 * 1000);
}

// Schedule periodic optimization (daily)
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    databaseService.optimizeDatabase();
  }, 24 * 60 * 60 * 1000);
}
