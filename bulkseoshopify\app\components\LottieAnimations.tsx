import { motion } from "framer-motion";
import { useEffect, useState } from "react";

// Client-side Lottie wrapper to avoid SSR issues
const ClientOnlyLottie = ({ animationData, loop = true }: { animationData: any; loop?: boolean }) => {
  const [<PERSON><PERSON>, setLottie] = useState<any>(null);

  useEffect(() => {
    import("lottie-react").then((LottieReact) => {
      setLottie(() => LottieReact.default);
    });
  }, []);

  if (!<PERSON>tie) {
    return <div className="animate-pulse bg-gray-200 rounded w-full h-full" />;
  }

  return <Lottie animationData={animationData} loop={loop} />;
};

// Loading Animation Component
export const LoadingAnimation = ({ size = 100, className = "" }: { size?: number; className?: string }) => {
  const loadingAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 90,
    "w": 200,
    "h": 200,
    "nm": "Loading",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Circle",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0] },
              { "t": 90, "s": [360] }
            ]
          },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": { "a": 0, "k": [100, 100, 100] }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [60, 60] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 8 },
                "lc": 2,
                "lj": 1,
                "ml": 4,
                "d": [
                  { "n": "d", "nm": "dash", "v": { "a": 0, "k": 20 } },
                  { "n": "g", "nm": "gap", "v": { "a": 0, "k": 10 } }
                ]
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 90,
        "st": 0,
        "bm": 0
      }
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={className}
      style={{ width: size, height: size }}
    >
      <ClientOnlyLottie animationData={loadingAnimation} loop={true} />
    </motion.div>
  );
};

// Success Animation Component
export const SuccessAnimation = ({ size = 100, className = "" }: { size?: number; className?: string }) => {
  const successAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 60,
    "w": 200,
    "h": 200,
    "nm": "Success",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Check",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0, 0, 100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 20, "s": [120, 120, 100] },
              { "t": 40, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "ind": 0,
                "ty": "sh",
                "ks": {
                  "a": 1,
                  "k": [
                    { "i": { "x": 0.833, "y": 0.833 }, "o": { "x": 0.167, "y": 0.167 }, "t": 10, "s": [{ "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-20, 0], [-20, 0], [-20, 0]], "c": false }] },
                    { "t": 50, "s": [{ "i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[-20, 0], [0, 20], [30, -20]], "c": false }] }
                  ]
                }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0.2, 0.8, 0.2, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 8 },
                "lc": 2,
                "lj": 2,
                "ml": 4
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 60,
        "st": 0,
        "bm": 0
      },
      {
        "ddd": 0,
        "ind": 2,
        "ty": 4,
        "nm": "Circle",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0, 0, 100] },
              { "t": 30, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [80, 80] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0.2, 0.8, 0.2, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 4 },
                "lc": 1,
                "lj": 1,
                "ml": 4
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 60,
        "st": 0,
        "bm": 0
      }
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.5 }}
      className={className}
      style={{ width: size, height: size }}
    >
      <ClientOnlyLottie animationData={successAnimation} loop={false} />
    </motion.div>
  );
};

// Processing Animation Component
export const ProcessingAnimation = ({ size = 100, className = "" }: { size?: number; className?: string }) => {
  const processingAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 120,
    "w": 200,
    "h": 200,
    "nm": "Processing",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Dot1",
        "sr": 1,
        "ks": {
          "o": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 20, "s": [30] },
              { "t": 40, "s": [100] }
            ]
          },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [70, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": { "a": 0, "k": [100, 100, 100] }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [12, 12] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "fl",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 }
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 120,
        "st": 0,
        "bm": 0
      },
      {
        "ddd": 0,
        "ind": 2,
        "ty": 4,
        "nm": "Dot2",
        "sr": 1,
        "ks": {
          "o": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 10, "s": [100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 30, "s": [30] },
              { "t": 50, "s": [100] }
            ]
          },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [100, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": { "a": 0, "k": [100, 100, 100] }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [12, 12] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "fl",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 }
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 120,
        "st": 0,
        "bm": 0
      },
      {
        "ddd": 0,
        "ind": 3,
        "ty": 4,
        "nm": "Dot3",
        "sr": 1,
        "ks": {
          "o": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 20, "s": [100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 40, "s": [30] },
              { "t": 60, "s": [100] }
            ]
          },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [130, 100, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": { "a": 0, "k": [100, 100, 100] }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [12, 12] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "fl",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 }
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 120,
        "st": 0,
        "bm": 0
      }
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={className}
      style={{ width: size, height: size }}
    >
      <ClientOnlyLottie animationData={processingAnimation} loop={true} />
    </motion.div>
  );
};

// Floating Elements Animation
export const FloatingElementsAnimation = ({ size = 200, className = "" }: { size?: number; className?: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={className}
      style={{ width: size, height: size, position: 'relative' }}
    >
      {[...Array(6)].map((_, i) => (
        <motion.div
          key={i}
          animate={{
            y: [0, -20, 0],
            x: [0, Math.sin(i) * 10, 0],
            rotate: [0, 360],
          }}
          transition={{
            duration: 3 + i * 0.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: i * 0.2
          }}
          style={{
            position: 'absolute',
            width: 8 + i * 2,
            height: 8 + i * 2,
            background: `hsl(${i * 60}, 70%, 60%)`,
            borderRadius: '50%',
            left: `${20 + i * 12}%`,
            top: `${30 + Math.sin(i) * 20}%`,
          }}
        />
      ))}
    </motion.div>
  );
};

// Morphing Shape Animation
export const MorphingShapeAnimation = ({ size = 100, className = "" }: { size?: number; className?: string }) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={className}
      style={{ width: size, height: size, display: 'flex', alignItems: 'center', justifyContent: 'center' }}
    >
      <motion.div
        animate={{
          borderRadius: ["20%", "50%", "20%"],
          rotate: [0, 180, 360],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 3,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          width: size * 0.6,
          height: size * 0.6,
          background: 'linear-gradient(45deg, #000000, #333333)',
          borderRadius: '20%',
        }}
      />
    </motion.div>
  );
};

// Bullet Point Animation
export const BulletPointAnimation = ({ size = 12, className = "" }: { size?: number; className?: string }) => {
  const bulletAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 60,
    "w": 24,
    "h": 24,
    "nm": "Bullet",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Dot",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": { "a": 0, "k": 0 },
          "p": { "a": 0, "k": [12, 12, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [100, 100, 100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 30, "s": [120, 120, 100] },
              { "t": 60, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [8, 8] },
                "p": { "a": 0, "k": [0, 0] }
              },
              {
                "ty": "fl",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 }
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 60,
        "st": 0,
        "bm": 0
      }
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.5 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: Math.random() * 0.5 }}
      className={className}
      style={{ width: size, height: size, display: 'inline-block' }}
    >
      <ClientOnlyLottie animationData={bulletAnimation} loop={true} />
    </motion.div>
  );
};

// SEO Icon Animation
export const SeoIconAnimation = ({ size = 64, className = "" }: { size?: number; className?: string }) => {
  const seoAnimation = {
    "v": "5.7.4",
    "fr": 30,
    "ip": 0,
    "op": 90,
    "w": 128,
    "h": 128,
    "nm": "SEO",
    "ddd": 0,
    "assets": [],
    "layers": [
      {
        "ddd": 0,
        "ind": 1,
        "ty": 4,
        "nm": "Search",
        "sr": 1,
        "ks": {
          "o": { "a": 0, "k": 100 },
          "r": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [0] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 45, "s": [10] },
              { "t": 90, "s": [0] }
            ]
          },
          "p": { "a": 0, "k": [64, 64, 0] },
          "a": { "a": 0, "k": [0, 0, 0] },
          "s": {
            "a": 1,
            "k": [
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 0, "s": [100, 100, 100] },
              { "i": { "x": [0.833], "y": [0.833] }, "o": { "x": [0.167], "y": [0.167] }, "t": 45, "s": [110, 110, 100] },
              { "t": 90, "s": [100, 100, 100] }
            ]
          }
        },
        "ao": 0,
        "shapes": [
          {
            "ty": "gr",
            "it": [
              {
                "d": 1,
                "ty": "el",
                "s": { "a": 0, "k": [40, 40] },
                "p": { "a": 0, "k": [-8, -8] }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 4 },
                "lc": 2,
                "lj": 1,
                "ml": 4
              },
              {
                "ind": 1,
                "ty": "sh",
                "ks": {
                  "a": 0,
                  "k": {
                    "i": [[0, 0], [0, 0]],
                    "o": [[0, 0], [0, 0]],
                    "v": [[12, 12], [20, 20]],
                    "c": false
                  }
                }
              },
              {
                "ty": "st",
                "c": { "a": 0, "k": [0, 0, 0, 1] },
                "o": { "a": 0, "k": 100 },
                "w": { "a": 0, "k": 4 },
                "lc": 2,
                "lj": 1,
                "ml": 4
              },
              {
                "ty": "tr",
                "p": { "a": 0, "k": [0, 0] },
                "a": { "a": 0, "k": [0, 0] },
                "s": { "a": 0, "k": [100, 100] },
                "r": { "a": 0, "k": 0 },
                "o": { "a": 0, "k": 100 }
              }
            ]
          }
        ],
        "ip": 0,
        "op": 90,
        "st": 0,
        "bm": 0
      }
    ]
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
      animate={{ opacity: 1, scale: 1, rotate: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className={className}
      style={{ width: size, height: size }}
    >
      <ClientOnlyLottie animationData={seoAnimation} loop={true} />
    </motion.div>
  );
};
