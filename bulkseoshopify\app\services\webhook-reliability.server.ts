/**
 * Webhook Reliability Service
 * Ensures webhook processing reliability, data consistency, and failure recovery
 */

import db from "../db.server";
import { logError, type ErrorContext } from "../utils/error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";

export interface WebhookEvent {
  id: string;
  topic: string;
  shop: string;
  payload: any;
  headers: Record<string, string>;
  receivedAt: Date;
  processedAt?: Date;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying';
  attempts: number;
  maxAttempts: number;
  lastError?: string;
  nextRetryAt?: Date;
}

export interface WebhookProcessor {
  topic: string;
  handler: (event: WebhookEvent) => Promise<void>;
  maxAttempts: number;
  retryDelayMs: number;
  timeout: number;
}

export interface DataSyncStatus {
  shop: string;
  lastSyncAt: Date;
  status: 'synced' | 'syncing' | 'error' | 'stale';
  errorCount: number;
  lastError?: string;
  nextSyncAt?: Date;
}

/**
 * Webhook reliability and data consistency service
 */
export class WebhookReliabilityService {
  private static instance: WebhookReliabilityService;
  private processors: Map<string, WebhookProcessor> = new Map();
  private processingQueue: WebhookEvent[] = [];
  private isProcessing = false;
  private syncStatuses: Map<string, DataSyncStatus> = new Map();

  static getInstance(): WebhookReliabilityService {
    if (!WebhookReliabilityService.instance) {
      WebhookReliabilityService.instance = new WebhookReliabilityService();
      WebhookReliabilityService.instance.initializeProcessors();
      WebhookReliabilityService.instance.startProcessing();
    }
    return WebhookReliabilityService.instance;
  }

  /**
   * Register webhook processor
   */
  registerProcessor(processor: WebhookProcessor): void {
    this.processors.set(processor.topic, processor);
    console.log(`📝 Registered webhook processor for topic: ${processor.topic}`);
  }

  /**
   * Process incoming webhook
   */
  async processWebhook(
    topic: string,
    shop: string,
    payload: any,
    headers: Record<string, string>,
    context?: ErrorContext
  ): Promise<{ success: boolean; eventId?: string; error?: string }> {
    try {
      // Apply rate limiting
      await applyRateLimit(RATE_LIMITERS.WEBHOOK_PROCESSING(shop), context);

      // Create webhook event
      const event: WebhookEvent = {
        id: `webhook_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        topic,
        shop,
        payload,
        headers,
        receivedAt: new Date(),
        status: 'pending',
        attempts: 0,
        maxAttempts: this.processors.get(topic)?.maxAttempts || 3
      };

      // Store event for reliability
      await this.storeWebhookEvent(event);

      // Add to processing queue
      this.processingQueue.push(event);

      console.log(`📨 Webhook queued: ${topic} for shop ${shop} (ID: ${event.id})`);

      return { success: true, eventId: event.id };

    } catch (error) {
      console.error('❌ Webhook processing failed:', error);
      await logError(
        error instanceof Error ? error : new Error('Webhook processing failed'),
        { ...context, shop, action: 'process_webhook', metadata: { topic } }
      );

      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get webhook processing status
   */
  async getWebhookStatus(eventId: string): Promise<WebhookEvent | null> {
    try {
      return await this.getWebhookEvent(eventId);
    } catch (error) {
      console.error('❌ Failed to get webhook status:', error);
      return null;
    }
  }

  /**
   * Retry failed webhook
   */
  async retryWebhook(eventId: string): Promise<boolean> {
    try {
      const event = await this.getWebhookEvent(eventId);
      if (!event) {
        return false;
      }

      if (event.status === 'completed') {
        console.log(`⚠️ Webhook ${eventId} already completed`);
        return true;
      }

      if (event.attempts >= event.maxAttempts) {
        console.log(`⚠️ Webhook ${eventId} exceeded max attempts`);
        return false;
      }

      // Reset status and add back to queue
      event.status = 'pending';
      event.nextRetryAt = undefined;
      this.processingQueue.push(event);

      console.log(`🔄 Webhook ${eventId} queued for retry`);
      return true;

    } catch (error) {
      console.error('❌ Failed to retry webhook:', error);
      return false;
    }
  }

  /**
   * Get data sync status for shop
   */
  getDataSyncStatus(shop: string): DataSyncStatus {
    return this.syncStatuses.get(shop) || {
      shop,
      lastSyncAt: new Date(0),
      status: 'stale',
      errorCount: 0
    };
  }

  /**
   * Update data sync status
   */
  updateDataSyncStatus(shop: string, status: Partial<DataSyncStatus>): void {
    const current = this.getDataSyncStatus(shop);
    const updated = { ...current, ...status, shop };
    this.syncStatuses.set(shop, updated);

    console.log(`📊 Data sync status updated for ${shop}: ${updated.status}`);
  }

  /**
   * Ensure data consistency for shop
   */
  async ensureDataConsistency(shop: string, context?: ErrorContext): Promise<boolean> {
    try {
      console.log(`🔄 Ensuring data consistency for shop: ${shop}`);

      this.updateDataSyncStatus(shop, { status: 'syncing' });

      // Check for missing or inconsistent data
      const inconsistencies = await this.detectDataInconsistencies(shop);
      
      if (inconsistencies.length > 0) {
        console.log(`⚠️ Found ${inconsistencies.length} data inconsistencies for ${shop}`);
        
        // Attempt to resolve inconsistencies
        for (const inconsistency of inconsistencies) {
          await this.resolveDataInconsistency(shop, inconsistency);
        }
      }

      this.updateDataSyncStatus(shop, {
        status: 'synced',
        lastSyncAt: new Date(),
        errorCount: 0,
        lastError: undefined
      });

      console.log(`✅ Data consistency ensured for shop: ${shop}`);
      return true;

    } catch (error) {
      console.error(`❌ Data consistency check failed for ${shop}:`, error);
      
      const syncStatus = this.getDataSyncStatus(shop);
      this.updateDataSyncStatus(shop, {
        status: 'error',
        errorCount: syncStatus.errorCount + 1,
        lastError: error instanceof Error ? error.message : 'Unknown error',
        nextSyncAt: new Date(Date.now() + 30 * 60 * 1000) // Retry in 30 minutes
      });

      await logError(
        error instanceof Error ? error : new Error('Data consistency check failed'),
        { ...context, shop, action: 'ensure_data_consistency' }
      );

      return false;
    }
  }

  /**
   * Private methods
   */
  private initializeProcessors(): void {
    // Register built-in processors
    this.registerProcessor({
      topic: 'app_subscriptions/update',
      handler: this.handleSubscriptionUpdate.bind(this),
      maxAttempts: 5,
      retryDelayMs: 30000, // 30 seconds
      timeout: 60000 // 1 minute
    });

    this.registerProcessor({
      topic: 'app_purchases_one_time/update',
      handler: this.handlePurchaseUpdate.bind(this),
      maxAttempts: 5,
      retryDelayMs: 30000,
      timeout: 60000
    });

    this.registerProcessor({
      topic: 'app/uninstalled',
      handler: this.handleAppUninstalled.bind(this),
      maxAttempts: 3,
      retryDelayMs: 60000, // 1 minute
      timeout: 120000 // 2 minutes
    });
  }

  private async startProcessing(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;
    console.log('🚀 Starting webhook processing...');

    // Process queue every 5 seconds
    setInterval(async () => {
      await this.processQueue();
    }, 5000);

    // Check for retries every minute
    setInterval(async () => {
      await this.processRetries();
    }, 60000);

    // Data consistency checks every 10 minutes
    setInterval(async () => {
      await this.performDataConsistencyChecks();
    }, 10 * 60 * 1000);
  }

  private async processQueue(): Promise<void> {
    if (this.processingQueue.length === 0) {
      return;
    }

    const event = this.processingQueue.shift();
    if (!event) {
      return;
    }

    await this.processEvent(event);
  }

  private async processEvent(event: WebhookEvent): Promise<void> {
    const processor = this.processors.get(event.topic);
    if (!processor) {
      console.warn(`⚠️ No processor found for topic: ${event.topic}`);
      event.status = 'failed';
      event.lastError = 'No processor found';
      await this.updateWebhookEvent(event);
      return;
    }

    event.status = 'processing';
    event.attempts++;
    await this.updateWebhookEvent(event);

    try {
      console.log(`⚡ Processing webhook: ${event.topic} for ${event.shop} (attempt ${event.attempts})`);

      // Set timeout for processing
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Webhook processing timeout')), processor.timeout);
      });

      await Promise.race([
        processor.handler(event),
        timeoutPromise
      ]);

      event.status = 'completed';
      event.processedAt = new Date();
      console.log(`✅ Webhook processed successfully: ${event.id}`);

    } catch (error) {
      console.error(`❌ Webhook processing failed: ${event.id}`, error);
      
      event.lastError = error instanceof Error ? error.message : 'Unknown error';

      if (event.attempts >= event.maxAttempts) {
        event.status = 'failed';
        console.error(`💀 Webhook failed permanently: ${event.id}`);
      } else {
        event.status = 'retrying';
        event.nextRetryAt = new Date(Date.now() + processor.retryDelayMs * event.attempts);
        console.log(`🔄 Webhook scheduled for retry: ${event.id} at ${event.nextRetryAt}`);
      }
    }

    await this.updateWebhookEvent(event);
  }

  private async processRetries(): Promise<void> {
    try {
      const retryEvents = await this.getRetryableEvents();
      
      for (const event of retryEvents) {
        if (event.nextRetryAt && new Date() >= event.nextRetryAt) {
          event.status = 'pending';
          event.nextRetryAt = undefined;
          this.processingQueue.push(event);
          console.log(`🔄 Webhook queued for retry: ${event.id}`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to process retries:', error);
    }
  }

  private async performDataConsistencyChecks(): Promise<void> {
    try {
      const shops = await this.getActiveShops();
      
      for (const shop of shops) {
        const syncStatus = this.getDataSyncStatus(shop);
        const staleThreshold = 60 * 60 * 1000; // 1 hour
        
        if (Date.now() - syncStatus.lastSyncAt.getTime() > staleThreshold) {
          console.log(`🔍 Performing data consistency check for stale shop: ${shop}`);
          await this.ensureDataConsistency(shop);
        }
      }
    } catch (error) {
      console.error('❌ Failed to perform data consistency checks:', error);
    }
  }

  // Webhook handlers
  private async handleSubscriptionUpdate(event: WebhookEvent): Promise<void> {
    // Implementation would update subscription data
    console.log(`📋 Processing subscription update for ${event.shop}`);
    this.updateDataSyncStatus(event.shop, { lastSyncAt: new Date() });
  }

  private async handlePurchaseUpdate(event: WebhookEvent): Promise<void> {
    // Implementation would update purchase data
    console.log(`💳 Processing purchase update for ${event.shop}`);
    this.updateDataSyncStatus(event.shop, { lastSyncAt: new Date() });
  }

  private async handleAppUninstalled(event: WebhookEvent): Promise<void> {
    // Implementation would clean up app data
    console.log(`🗑️ Processing app uninstall for ${event.shop}`);
    this.syncStatuses.delete(event.shop);
  }

  // Database operations (simplified - would need proper implementation)
  private async storeWebhookEvent(event: WebhookEvent): Promise<void> {
    console.log(`💾 Storing webhook event: ${event.id}`);
  }

  private async updateWebhookEvent(event: WebhookEvent): Promise<void> {
    console.log(`📝 Updating webhook event: ${event.id} (${event.status})`);
  }

  private async getWebhookEvent(eventId: string): Promise<WebhookEvent | null> {
    console.log(`📖 Getting webhook event: ${eventId}`);
    return null; // Placeholder
  }

  private async getRetryableEvents(): Promise<WebhookEvent[]> {
    console.log(`🔍 Getting retryable events`);
    return []; // Placeholder
  }

  private async getActiveShops(): Promise<string[]> {
    try {
      const sessions = await db.session.findMany({
        select: { shop: true },
        distinct: ['shop']
      });
      return sessions.map(s => String(s.shop)).filter(shop => shop);
    } catch (error) {
      console.error('❌ Failed to get active shops:', error);
      return [];
    }
  }

  private async detectDataInconsistencies(shop: string): Promise<string[]> {
    // Implementation would check for data inconsistencies
    console.log(`🔍 Detecting data inconsistencies for ${shop}`);
    return []; // Placeholder
  }

  private async resolveDataInconsistency(shop: string, inconsistency: string): Promise<void> {
    // Implementation would resolve specific inconsistency
    console.log(`🔧 Resolving data inconsistency for ${shop}: ${inconsistency}`);
  }
}

// Export singleton instance
export const webhookReliabilityService = WebhookReliabilityService.getInstance();
