import { json, redirect } from "@remix-run/node";
import { a as authenticate, b as applyRateLimit, R as RATE_LIMITERS, v as validateCSRFFromForm, g as getEnvironmentConfig, B as BillingService, d as db } from "./server-build-CCVLOhir.js";
import "react/jsx-runtime";
import "stream";
import "react-dom/server";
import "@remix-run/react";
import "isbot";
import "@shopify/shopify-app-remix/adapters/node";
import "@shopify/shopify-app-remix/server";
import "@shopify/shopify-api/rest/admin/2025-01";
import "@shopify/shopify-app-session-storage-prisma";
import "@prisma/client";
import "react";
import "@shopify/polaris";
import "@shopify/shopify-app-remix/react";
import "@shopify/app-bridge-react";
import "framer-motion";
import "crypto";
import "@radix-ui/react-slot";
import "class-variance-authority";
import "clsx";
import "tailwind-merge";
import "@radix-ui/react-select";
import "lucide-react";
import "@radix-ui/react-progress";
import "@radix-ui/react-separator";
import "@radix-ui/react-checkbox";
function validatePlanId(planId) {
  const errors = [];
  if (!planId) {
    errors.push("Plan ID is required");
    return { isValid: false, errors };
  }
  if (typeof planId !== "string") {
    errors.push("Plan ID must be a string");
    return { isValid: false, errors };
  }
  const trimmedPlanId = planId.trim();
  if (trimmedPlanId.length === 0) {
    errors.push("Plan ID cannot be empty");
    return { isValid: false, errors };
  }
  const validPlanIds = ["monthly", "annual", "pay_per_use"];
  if (!validPlanIds.includes(trimmedPlanId)) {
    errors.push(`Invalid plan ID. Must be one of: ${validPlanIds.join(", ")}`);
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedPlanId
  };
}
function validateProductCount(productCount) {
  const errors = [];
  if (productCount === null || productCount === void 0) {
    errors.push("Product count is required");
    return { isValid: false, errors };
  }
  let numericCount;
  if (typeof productCount === "string") {
    const parsed = parseInt(productCount.trim(), 10);
    if (isNaN(parsed)) {
      errors.push("Product count must be a valid number");
      return { isValid: false, errors };
    }
    numericCount = parsed;
  } else if (typeof productCount === "number") {
    if (!Number.isInteger(productCount)) {
      errors.push("Product count must be an integer");
      return { isValid: false, errors };
    }
    numericCount = productCount;
  } else {
    errors.push("Product count must be a number");
    return { isValid: false, errors };
  }
  if (numericCount <= 0) {
    errors.push("Product count must be greater than 0");
  }
  if (numericCount > 1e3) {
    errors.push("Product count cannot exceed 1000 per purchase");
  }
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: numericCount
  };
}
function validateSelectedProducts(selectedProducts, expectedCount) {
  const errors = [];
  if (!selectedProducts) {
    errors.push("Selected products are required");
    return { isValid: false, errors };
  }
  let parsedProducts;
  if (typeof selectedProducts === "string") {
    try {
      parsedProducts = JSON.parse(selectedProducts);
    } catch {
      errors.push("Selected products must be valid JSON");
      return { isValid: false, errors };
    }
  } else if (Array.isArray(selectedProducts)) {
    parsedProducts = selectedProducts;
  } else {
    errors.push("Selected products must be an array");
    return { isValid: false, errors };
  }
  if (!Array.isArray(parsedProducts)) {
    errors.push("Selected products must be an array");
    return { isValid: false, errors };
  }
  if (parsedProducts.length === 0) {
    errors.push("At least one product must be selected");
    return { isValid: false, errors };
  }
  for (let i = 0; i < parsedProducts.length; i++) {
    const productId = parsedProducts[i];
    if (typeof productId !== "string") {
      errors.push(`Product ID at index ${i} must be a string`);
      continue;
    }
    if (productId.trim().length === 0) {
      errors.push(`Product ID at index ${i} cannot be empty`);
      continue;
    }
    if (!productId.startsWith("gid://shopify/Product/")) {
      errors.push(`Product ID at index ${i} has invalid format`);
    }
  }
  if (expectedCount !== void 0 && parsedProducts.length !== expectedCount) {
    errors.push(`Expected ${expectedCount} products, but got ${parsedProducts.length}`);
  }
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: parsedProducts.map((id) => id.trim())
  };
}
function validateSubscriptionId(subscriptionId) {
  const errors = [];
  if (!subscriptionId) {
    errors.push("Subscription ID is required");
    return { isValid: false, errors };
  }
  if (typeof subscriptionId !== "string") {
    errors.push("Subscription ID must be a string");
    return { isValid: false, errors };
  }
  const trimmedId = subscriptionId.trim();
  if (trimmedId.length === 0) {
    errors.push("Subscription ID cannot be empty");
    return { isValid: false, errors };
  }
  if (!trimmedId.startsWith("gid://shopify/AppSubscription/")) {
    errors.push("Invalid subscription ID format");
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedId
  };
}
function validateBillingAction(action) {
  const errors = [];
  if (!action) {
    errors.push("Action is required");
    return { isValid: false, errors };
  }
  if (typeof action !== "string") {
    errors.push("Action must be a string");
    return { isValid: false, errors };
  }
  const trimmedAction = action.trim();
  if (trimmedAction.length === 0) {
    errors.push("Action cannot be empty");
    return { isValid: false, errors };
  }
  const validActions = [
    "create_subscription",
    "cancel_subscription",
    "create_pay_per_use_purchase",
    "check_purchase_status"
  ];
  if (!validActions.includes(trimmedAction)) {
    errors.push(`Invalid action. Must be one of: ${validActions.join(", ")}`);
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: trimmedAction
  };
}
function validateBillingFormData(formData) {
  const errors = [];
  const sanitizedData = {};
  const actionResult = validateBillingAction(formData.get("action"));
  if (!actionResult.isValid) {
    errors.push(...actionResult.errors);
    return { isValid: false, errors };
  }
  const action = actionResult.sanitizedValue;
  sanitizedData.action = action;
  switch (action) {
    case "create_subscription": {
      const planResult = validatePlanId(formData.get("planId"));
      if (!planResult.isValid) {
        errors.push(...planResult.errors);
      } else {
        sanitizedData.planId = planResult.sanitizedValue;
      }
      break;
    }
    case "cancel_subscription": {
      const subResult = validateSubscriptionId(formData.get("subscriptionId"));
      if (!subResult.isValid) {
        errors.push(...subResult.errors);
      } else {
        sanitizedData.subscriptionId = subResult.sanitizedValue;
      }
      break;
    }
    case "create_pay_per_use_purchase": {
      const countResult = validateProductCount(formData.get("productCount"));
      if (!countResult.isValid) {
        errors.push(...countResult.errors);
      } else {
        sanitizedData.productCount = countResult.sanitizedValue;
      }
      const productsResult = validateSelectedProducts(
        formData.get("selectedProducts"),
        sanitizedData.productCount
      );
      if (!productsResult.isValid) {
        errors.push(...productsResult.errors);
      } else {
        sanitizedData.selectedProducts = productsResult.sanitizedValue;
      }
      break;
    }
  }
  if (errors.length > 0) {
    return { isValid: false, errors };
  }
  return {
    isValid: true,
    errors: [],
    sanitizedValue: sanitizedData
  };
}
async function handleCreateSubscription(sanitizedData, admin, shop) {
  var _a, _b, _c, _d, _e;
  try {
    const planId = sanitizedData.planId;
    const config = getEnvironmentConfig();
    const returnUrl = `${config.SHOPIFY_APP_URL}/billing-success?type=subscription&shop=${shop}`;
    console.log(`🔄 Creating subscription for shop: ${shop}, plan: ${planId}`);
    const billingService = new BillingService(admin, shop);
    const result = await billingService.createSubscription(planId, returnUrl);
    if (((_c = (_b = (_a = result.data) == null ? void 0 : _a.appSubscriptionCreate) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
      const error = result.data.appSubscriptionCreate.userErrors[0];
      console.error(`❌ Subscription creation failed:`, error);
      return {
        error: `Subscription creation failed: ${error.message}${error.field ? ` (Field: ${error.field})` : ""}`
      };
    }
    if (!((_e = (_d = result.data) == null ? void 0 : _d.appSubscriptionCreate) == null ? void 0 : _e.appSubscription)) {
      console.error(`❌ No subscription returned from Shopify`);
      return { error: "Subscription creation failed: No subscription returned from Shopify" };
    }
    if (!result.data.appSubscriptionCreate.confirmationUrl) {
      console.error(`❌ No confirmation URL returned from Shopify`);
      return { error: "Subscription creation failed: No confirmation URL returned" };
    }
    const subscriptionId = result.data.appSubscriptionCreate.appSubscription.id;
    const confirmationUrl = result.data.appSubscriptionCreate.confirmationUrl;
    console.log(`✅ Subscription created successfully:`, {
      subscriptionId,
      confirmationUrl: confirmationUrl.substring(0, 100) + "..."
    });
    return {
      success: true,
      confirmationUrl,
      subscriptionId
    };
  } catch (error) {
    console.error(`❌ Subscription creation error for shop ${shop}:`, error);
    return {
      error: error instanceof Error ? error.message : "An unexpected error occurred during subscription creation"
    };
  }
}
async function handleCancelSubscription(sanitizedData, admin, shop) {
  var _a, _b, _c, _d, _e;
  try {
    const subscriptionId = sanitizedData.subscriptionId;
    console.log(`🔄 Cancelling subscription for shop: ${shop}, subscription: ${subscriptionId}`);
    const billingService = new BillingService(admin, shop);
    const result = await billingService.cancelSubscription(subscriptionId);
    if (((_c = (_b = (_a = result.data) == null ? void 0 : _a.appSubscriptionCancel) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
      const error = result.data.appSubscriptionCancel.userErrors[0];
      console.error(`❌ Subscription cancellation failed:`, error);
      return {
        error: `Subscription cancellation failed: ${error.message}`
      };
    }
    if ((_e = (_d = result.data) == null ? void 0 : _d.appSubscriptionCancel) == null ? void 0 : _e.appSubscription) {
      try {
        await db.$transaction(async (tx) => {
          await tx.billingSubscription.updateMany({
            where: {
              shop,
              subscriptionId
            },
            data: {
              status: "CANCELLED",
              updatedAt: /* @__PURE__ */ new Date()
            }
          });
          await tx.billingEvent.create({
            data: {
              shop,
              eventType: "subscription_cancelled",
              referenceId: subscriptionId,
              eventData: JSON.stringify({
                subscriptionId,
                status: "CANCELLED",
                cancelledAt: (/* @__PURE__ */ new Date()).toISOString()
              })
            }
          });
          await tx.session.updateMany({
            where: { shop },
            data: {
              subscriptionId: null,
              subscriptionStatus: "CANCELLED",
              billingPlanId: "pay_per_use",
              // Default back to pay-per-use
              lastBillingCheck: /* @__PURE__ */ new Date()
            }
          });
        });
        console.log(`✅ Database updated for cancelled subscription: ${subscriptionId}`);
      } catch (dbError) {
        console.error(`⚠️ Failed to update database for cancelled subscription:`, dbError);
      }
      const { invalidateBillingCache } = await import("./server-build-CCVLOhir.js").then((n) => n.j);
      invalidateBillingCache(shop);
      console.log(`🔄 Billing cache invalidated for shop: ${shop}`);
    }
    console.log(`✅ Subscription cancelled successfully for shop: ${shop}`);
    return {
      success: true,
      redirectTo: "/app/billing?cancelled=true"
    };
  } catch (error) {
    console.error(`❌ Subscription cancellation error for shop ${shop}:`, error);
    return {
      error: error instanceof Error ? error.message : "An unexpected error occurred during subscription cancellation"
    };
  }
}
async function handleCreatePayPerUsePurchase(sanitizedData, admin, shop) {
  var _a, _b, _c, _d, _e, _f;
  try {
    const productCount = sanitizedData.productCount;
    const config = getEnvironmentConfig();
    const returnUrl = `${config.SHOPIFY_APP_URL}/billing-success?type=purchase&shop=${shop}`;
    console.log(`🔄 Creating pay-per-use purchase for shop: ${shop}, products: ${productCount}`);
    const billingService = new BillingService(admin, shop);
    const result = await billingService.createOneTimePurchase(productCount, returnUrl);
    if (((_c = (_b = (_a = result.data) == null ? void 0 : _a.appPurchaseOneTimeCreate) == null ? void 0 : _b.userErrors) == null ? void 0 : _c.length) > 0) {
      const error = result.data.appPurchaseOneTimeCreate.userErrors[0];
      console.error(`❌ Pay-per-use purchase creation failed:`, error);
      return {
        error: `Purchase creation failed: ${error.message}`
      };
    }
    if (!((_e = (_d = result.data) == null ? void 0 : _d.appPurchaseOneTimeCreate) == null ? void 0 : _e.confirmationUrl)) {
      console.error(`❌ No confirmation URL returned for pay-per-use purchase`);
      return { error: "Purchase creation failed: No confirmation URL returned" };
    }
    const purchaseId = (_f = result.data.appPurchaseOneTimeCreate.appPurchaseOneTime) == null ? void 0 : _f.id;
    const confirmationUrl = result.data.appPurchaseOneTimeCreate.confirmationUrl;
    console.log(`✅ Pay-per-use purchase created successfully:`, {
      purchaseId,
      confirmationUrl: confirmationUrl.substring(0, 100) + "..."
    });
    return {
      success: true,
      confirmationUrl,
      purchaseId
    };
  } catch (error) {
    console.error(`❌ Pay-per-use purchase creation error for shop ${shop}:`, error);
    return {
      error: error instanceof Error ? error.message : "An unexpected error occurred during purchase creation"
    };
  }
}
async function handleBillingAction({ request }) {
  try {
    console.log(`🔄 Billing action - Request URL: ${request.url}`);
    const { admin, session } = await authenticate.admin(request);
    console.log(`✅ Billing action - Authenticated for shop: ${session.shop}`);
    if (!session.shop) {
      console.error(`❌ No shop in session`);
      return json({ error: "Authentication failed: No shop in session" }, { status: 401 });
    }
    try {
      await applyRateLimit(RATE_LIMITERS.BILLING_OPERATIONS(session.shop), {
        action: "billing_action",
        shop: session.shop
      });
    } catch (error) {
      console.error(`❌ Rate limit exceeded for shop: ${session.shop}`);
      return json({ error: "Too many requests. Please try again later." }, { status: 429 });
    }
    const formData = await request.formData();
    if (!validateCSRFFromForm(formData, session.shop)) {
      console.error(`❌ CSRF validation failed for shop: ${session.shop}`);
      return json({ error: "Invalid security token. Please refresh the page and try again." }, { status: 403 });
    }
    console.log(`📋 Raw form data:`, Object.fromEntries(formData.entries()));
    const validationResult = validateBillingFormData(formData);
    if (!validationResult.isValid) {
      console.error(`❌ Form validation failed:`, validationResult.errors);
      return json({
        error: `Validation failed: ${validationResult.errors.join(", ")}`
      }, { status: 400 });
    }
    const sanitizedData = validationResult.sanitizedValue;
    const action = sanitizedData.action;
    console.log(`🎯 Billing action type: ${action}`);
    console.log(`✅ Sanitized data:`, sanitizedData);
    let result;
    switch (action) {
      case "create_subscription":
        result = await handleCreateSubscription(sanitizedData, admin, session.shop);
        break;
      case "cancel_subscription":
        result = await handleCancelSubscription(sanitizedData, admin, session.shop);
        break;
      case "create_pay_per_use_purchase":
        result = await handleCreatePayPerUsePurchase(sanitizedData, admin, session.shop);
        break;
      default:
        return json({ error: `Unknown action: ${action}` }, { status: 400 });
    }
    if (result.error) {
      console.error(`❌ Billing action failed:`, result.error);
      return json({ error: result.error }, { status: 400 });
    }
    if (result.redirectTo) {
      console.log(`🔄 Redirecting to: ${result.redirectTo}`);
      return redirect(result.redirectTo);
    }
    if (result.confirmationUrl) {
      console.log(`✅ Billing action successful, returning confirmation URL`);
      return json({
        success: true,
        confirmationUrl: result.confirmationUrl,
        subscriptionId: result.subscriptionId,
        purchaseId: result.purchaseId
      });
    }
    return json({ success: true });
  } catch (error) {
    console.error(`❌ Billing action handler error:`, error);
    return json({
      error: error instanceof Error ? error.message : "An unexpected error occurred"
    }, { status: 500 });
  }
}
export {
  handleBillingAction,
  handleCancelSubscription,
  handleCreatePayPerUsePurchase,
  handleCreateSubscription
};
