import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const { admin, session } = await authenticate.admin(request);

    // Check if subscription status has been updated recently and force cache refresh if needed
    const { getSubscriptionUpdateFlag, clearSubscriptionUpdateFlag, invalidateBillingCache } = await import("../utils/cache.server");
    const subscriptionUpdateFlag = getSubscriptionUpdateFlag(session.shop);

    if (subscriptionUpdateFlag) {
      console.log(`🔄 Subscription update detected for shop: ${session.shop}, forcing billing cache refresh via API`);
      // Clear billing cache to ensure we get fresh data
      invalidateBillingCache(session.shop);
      // Clear the flag so we don't keep refreshing unnecessarily
      clearSubscriptionUpdateFlag(session.shop);
    }

    const billingService = new BillingService(admin, session.shop);
    const billingStatus = await billingService.hasActiveBilling();
    
    return json({
      hasAccess: billingStatus.hasAccess,
      plan: billingStatus.plan,
      subscription: billingStatus.subscription,
      isLoading: false
    });
  } catch (error) {
    console.error('Billing status API error:', error);
    return json({
      hasAccess: false,
      isLoading: false,
      error: error instanceof Error ? error.message : "Failed to check billing status"
    }, { status: 500 });
  }
};
