import{j as o}from"./jsx-runtime-0DLF9kdB.js";import{R as a,r as s}from"./index-BJYSoprK.js";import{p as T,A as I}from"./styles-duVO-NUe.js";import{u as C,d as F,F as L}from"./components-C2v4lmCU.js";import{w as d,B as h,I as b,a as k,i as P,P as S,T as v}from"./Page-Dli0YmGf.js";import{C as B}from"./Card-u-CSbGqf.js";import{c as A,T as x,B as D}from"./Button-3T1ZHLBP.js";import"./use-is-after-initial-mount-MUb1fdtO.js";import"./context-Dt_50QHC.js";import"./index-Ci0627_k.js";import"./index-DI88vBYx.js";var p={Item:"Polaris-FormLayout__Item",grouped:"Polaris-FormLayout--grouped",condensed:"Polaris-FormLayout--condensed"};function f({children:e,condensed:t=!1}){const r=A(p.Item,t?p.condensed:p.grouped);return e?a.createElement("div",{className:r},e):null}function y({children:e,condensed:t,title:r,helpText:n}){const l=s.useId();let c=null,i,u=null,m;n&&(i=`${l}HelpText`,c=a.createElement(k,{id:i,color:"text-secondary"},n)),r&&(m=`${l}Title`,u=a.createElement(x,{id:m,as:"p"},r));const E=s.Children.map(e,j=>d(j,f,{condensed:t}));return a.createElement(h,{role:"group",gap:"200","aria-labelledby":m,"aria-describedby":i},u,a.createElement(b,{gap:"300"},E),c)}const g=s.memo(function({children:t}){return a.createElement(h,{gap:"400"},s.Children.map(t,w))});g.Group=y;function w(e,t){return P(e,y)?e:d(e,f,{key:t})}const J=()=>[{rel:"stylesheet",href:T}];function K(){const e=C(),t=F(),[r,n]=s.useState(""),{errors:l}=t||e;return o.jsx(I,{i18n:e.polarisTranslations,children:o.jsx(S,{children:o.jsx(B,{children:o.jsx(L,{method:"post",children:o.jsxs(g,{children:[o.jsx(x,{variant:"headingMd",as:"h2",children:"Log in"}),o.jsx(v,{type:"text",name:"shop",label:"Shop domain",helpText:"example.myshopify.com",value:r,onChange:n,autoComplete:"on",error:l.shop}),o.jsx(D,{submit:!0,children:"Log in"})]})})})})})}export{K as default,J as links};
