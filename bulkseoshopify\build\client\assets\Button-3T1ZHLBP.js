import{R as a,r as v}from"./index-BJYSoprK.js";import{h as Y,u as J,c as ge,L as he,j as X,I as ye}from"./use-is-after-initial-mount-MUb1fdtO.js";function Q(e){const t=typeof e;return e!=null&&(t==="object"||t==="function")}function E(...e){return e.filter(Boolean).join(" ")}function g(e,t){return`${e}${t.charAt(0).toUpperCase()}${t.slice(1)}`}function _e(e){const t=Object.entries(e).filter(([n,o])=>o!=null);return t.length?Object.fromEntries(t):void 0}function $e(e,t,n,o){if(!o)return{};let r;return Q(o)?r=Object.fromEntries(Object.entries(o).map(([s,c])=>[s,`var(--p-${n}-${c})`])):r={[Y[0]]:`var(--p-${n}-${o})`},Object.fromEntries(Object.entries(r).map(([s,c])=>[`--pc-${e}-${t}-${s}`,c]))}function Fe(e,t,n){return n?Q(n)?Object.fromEntries(Object.entries(n).map(([o,r])=>[`--pc-${e}-${t}-${o}`,r])):{[`--pc-${e}-${t}-${Y[0]}`]:n}:{}}var ee=function(t){return a.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),a.createElement("path",{fillRule:"evenodd",d:"M5.72 8.47a.75.75 0 0 1 1.06 0l3.47 3.47 3.47-3.47a.75.75 0 1 1 1.06 1.06l-4 4a.75.75 0 0 1-1.06 0l-4-4a.75.75 0 0 1 0-1.06Z"}))};ee.displayName="ChevronDownIcon";var te=function(t){return a.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),a.createElement("path",{fillRule:"evenodd",d:"M14.53 12.28a.75.75 0 0 1-1.06 0l-3.47-3.47-3.47 3.47a.75.75 0 0 1-1.06-1.06l4-4a.75.75 0 0 1 1.06 0l4 4a.75.75 0 0 1 0 1.06Z"}))};te.displayName="ChevronUpIcon";var ne=function(t){return a.createElement("svg",Object.assign({viewBox:"0 0 20 20"},t),a.createElement("path",{d:"M10.884 4.323a1.25 1.25 0 0 0-1.768 0l-2.646 2.647a.75.75 0 0 0 1.06 1.06l2.47-2.47 2.47 2.47a.75.75 0 1 0 1.06-1.06l-2.646-2.647Z"}),a.createElement("path",{d:"m13.53 13.03-2.646 2.647a1.25 1.25 0 0 1-1.768 0l-2.646-2.647a.75.75 0 0 1 1.06-1.06l2.47 2.47 2.47-2.47a.75.75 0 0 1 1.06 1.06Z"}))};ne.displayName="SelectIcon";function Ie(e){const{top:t,left:n,bottom:o,right:r}=e.getBoundingClientRect();return t>=0&&r<=window.innerWidth&&o<=window.innerHeight&&n>=0}const U='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]',K='a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not([aria-disabled="true"]):not([tabindex="-1"]):not(:disabled),*[tabindex]:not([tabindex="-1"])',ve='a[role="menuitem"],frame[role="menuitem"],iframe[role="menuitem"],input[role="menuitem"]:not([type=hidden]):not(:disabled),select[role="menuitem"]:not(:disabled),textarea[role="menuitem"]:not(:disabled),button[role="menuitem"]:not(:disabled),*[tabindex]:not([tabindex="-1"])',oe=({currentTarget:e})=>e.blur();function Se(e,t){const n=[...document.querySelectorAll(U)],o=n.indexOf(e)+1,r=n.slice(o);for(const s of r)if(Ie(s)&&(!t||t&&t(s)))return s;return null}function Ue(e,t=!0){return!t&&z(e,U)?e:e.querySelector(U)}function ze(e){const t="a,button,frame,iframe,input:not([type=hidden]),select,textarea,*[tabindex]";return z(e,t)?e:e.querySelector(t)}function Re(e,t){const n=Se(e,t);return n&&n instanceof HTMLElement?(n.focus(),!0):!1}function We(e,t=!0){return!t&&z(e,K)?e:e.querySelector(K)}function De(e,t){const n=ae(e),o=re(n,t);o===-1?n[0].focus():n[(o-1+n.length)%n.length].focus()}function qe(e,t){const n=ae(e),o=re(n,t);o===-1?n[0].focus():n[(o+1)%n.length].focus()}function ae(e){return e.querySelectorAll(ve)}function re(e,t){let n=0;for(const o of e){if(o===t)break;n++}return n===e.length?-1:n}function z(e,t){if(e.matches)return e.matches(t);const n=(e.ownerDocument||document).querySelectorAll(t);let o=n.length;for(;--o>=0&&n.item(o)!==e;)return o>-1}var l={Button:"Polaris-Button",disabled:"Polaris-Button--disabled",pressed:"Polaris-Button--pressed",variantPrimary:"Polaris-Button--variantPrimary",variantSecondary:"Polaris-Button--variantSecondary",variantTertiary:"Polaris-Button--variantTertiary",variantPlain:"Polaris-Button--variantPlain",removeUnderline:"Polaris-Button--removeUnderline",variantMonochromePlain:"Polaris-Button--variantMonochromePlain",toneSuccess:"Polaris-Button--toneSuccess",toneCritical:"Polaris-Button--toneCritical",sizeMicro:"Polaris-Button--sizeMicro",sizeSlim:"Polaris-Button--sizeSlim",sizeMedium:"Polaris-Button--sizeMedium",sizeLarge:"Polaris-Button--sizeLarge",textAlignCenter:"Polaris-Button--textAlignCenter",textAlignStart:"Polaris-Button--textAlignStart",textAlignLeft:"Polaris-Button--textAlignLeft",textAlignEnd:"Polaris-Button--textAlignEnd",textAlignRight:"Polaris-Button--textAlignRight",fullWidth:"Polaris-Button--fullWidth",iconOnly:"Polaris-Button--iconOnly",iconWithText:"Polaris-Button--iconWithText",disclosure:"Polaris-Button--disclosure",loading:"Polaris-Button--loading",pressable:"Polaris-Button--pressable",hidden:"Polaris-Button--hidden",Icon:"Polaris-Button__Icon",Spinner:"Polaris-Button__Spinner"},I={Icon:"Polaris-Icon",toneInherit:"Polaris-Icon--toneInherit",toneBase:"Polaris-Icon--toneBase",toneSubdued:"Polaris-Icon--toneSubdued",toneCaution:"Polaris-Icon--toneCaution",toneWarning:"Polaris-Icon--toneWarning",toneCritical:"Polaris-Icon--toneCritical",toneInteractive:"Polaris-Icon--toneInteractive",toneInfo:"Polaris-Icon--toneInfo",toneSuccess:"Polaris-Icon--toneSuccess",tonePrimary:"Polaris-Icon--tonePrimary",toneEmphasis:"Polaris-Icon--toneEmphasis",toneMagic:"Polaris-Icon--toneMagic",toneTextCaution:"Polaris-Icon--toneTextCaution",toneTextWarning:"Polaris-Icon--toneTextWarning",toneTextCritical:"Polaris-Icon--toneTextCritical",toneTextInfo:"Polaris-Icon--toneTextInfo",toneTextPrimary:"Polaris-Icon--toneTextPrimary",toneTextSuccess:"Polaris-Icon--toneTextSuccess",toneTextMagic:"Polaris-Icon--toneTextMagic",Svg:"Polaris-Icon__Svg",Img:"Polaris-Icon__Img",Placeholder:"Polaris-Icon__Placeholder"},d={root:"Polaris-Text--root",block:"Polaris-Text--block",truncate:"Polaris-Text--truncate",visuallyHidden:"Polaris-Text--visuallyHidden",start:"Polaris-Text--start",center:"Polaris-Text--center",end:"Polaris-Text--end",justify:"Polaris-Text--justify",base:"Polaris-Text--base",inherit:"Polaris-Text--inherit",disabled:"Polaris-Text--disabled",success:"Polaris-Text--success",critical:"Polaris-Text--critical",caution:"Polaris-Text--caution",subdued:"Polaris-Text--subdued",magic:"Polaris-Text--magic","magic-subdued":"Polaris-Text__magic--subdued","text-inverse":"Polaris-Text__text--inverse","text-inverse-secondary":"Polaris-Text--textInverseSecondary",headingXs:"Polaris-Text--headingXs",headingSm:"Polaris-Text--headingSm",headingMd:"Polaris-Text--headingMd",headingLg:"Polaris-Text--headingLg",headingXl:"Polaris-Text--headingXl",heading2xl:"Polaris-Text--heading2xl",heading3xl:"Polaris-Text--heading3xl",bodyXs:"Polaris-Text--bodyXs",bodySm:"Polaris-Text--bodySm",bodyMd:"Polaris-Text--bodyMd",bodyLg:"Polaris-Text--bodyLg",regular:"Polaris-Text--regular",medium:"Polaris-Text--medium",semibold:"Polaris-Text--semibold",bold:"Polaris-Text--bold",break:"Polaris-Text--break",numeric:"Polaris-Text--numeric","line-through":"Polaris-Text__line--through"};const R=({alignment:e,as:t,breakWord:n,children:o,tone:r,fontWeight:s,id:c,numeric:u=!1,truncate:i=!1,variant:m,visuallyHidden:b=!1,textDecorationLine:x})=>{const h=t||(b?"span":"p"),y=E(d.root,m&&d[m],s&&d[s],(e||i)&&d.block,e&&d[e],n&&d.break,r&&d[r],u&&d.numeric,i&&d.truncate,b&&d.visuallyHidden,x&&d[x]);return a.createElement(h,Object.assign({className:y},c&&{id:c}),o)};function Z({source:e,tone:t,accessibilityLabel:n}){let o;typeof e=="function"?o="function":e==="placeholder"?o="placeholder":o="external";const r=E(I.Icon,t&&I[g("tone",t)]),{mdDown:s}=J(),c=e,u={function:a.createElement(c,Object.assign({className:I.Svg,focusable:"false","aria-hidden":"true"},s?{viewBox:"1 1 18 18"}:{})),placeholder:a.createElement("div",{className:I.Placeholder}),external:a.createElement("img",{className:I.Img,src:`data:image/svg+xml;utf8,${e}`,alt:"","aria-hidden":"true"})};return a.createElement("span",{className:r},n&&a.createElement(R,{as:"span",visuallyHidden:!0},n),u[o])}var G={Spinner:"Polaris-Spinner",sizeSmall:"Polaris-Spinner--sizeSmall",sizeLarge:"Polaris-Spinner--sizeLarge"};function Te({size:e="large",accessibilityLabel:t,hasFocusableParent:n}){const o=ge(),r=E(G.Spinner,e&&G[g("size",e)]),s=e==="large"?a.createElement("svg",{viewBox:"0 0 44 44",xmlns:"http://www.w3.org/2000/svg"},a.createElement("path",{d:"M15.542 1.487A21.507 21.507 0 00.5 22c0 11.874 9.626 21.5 21.5 21.5 9.847 0 18.364-6.675 20.809-16.072a1.5 1.5 0 00-2.904-.756C37.803 34.755 30.473 40.5 22 40.5 11.783 40.5 3.5 32.217 3.5 22c0-8.137 5.3-15.247 12.942-17.65a1.5 1.5 0 10-.9-2.863z"})):a.createElement("svg",{viewBox:"0 0 20 20",xmlns:"http://www.w3.org/2000/svg"},a.createElement("path",{d:"M7.229 1.173a9.25 9.25 0 1011.655 11.412 1.25 1.25 0 10-2.4-.698 6.75 6.75 0 11-8.506-8.329 1.25 1.25 0 10-.75-2.385z"})),c={...!n&&{role:"status"}},u=(o||!n)&&a.createElement(R,{as:"span",visuallyHidden:!0},t);return a.createElement(a.Fragment,null,a.createElement("span",{className:r},s),a.createElement("span",c,u))}function Ee(e,t){const n=v.useCallback(o=>{e&&(o.preventDefault(),o.stopPropagation())},[e]);return e?n:t}function Be(){return v.useContext(he)}const Me=v.memo(v.forwardRef(function(t,n){const o=Be();if(o)return a.createElement(o,Object.assign({},X.props,t,{ref:n}));const{external:r,url:s,target:c,...u}=t;let i;r?i="_blank":i=c??void 0;const m=i==="_blank"?"noopener noreferrer":void 0;return a.createElement("a",Object.assign({target:i},u,{href:s,rel:m},X.props,{ref:n}))}));function Ce({id:e,children:t,className:n,url:o,external:r,target:s,download:c,submit:u,disabled:i,loading:m,pressed:b,accessibilityLabel:x,role:h,ariaControls:y,ariaExpanded:B,ariaDescribedBy:M,ariaChecked:C,onClick:S,onFocus:w,onBlur:k,onKeyDown:O,onKeyPress:A,onKeyUp:j,onMouseEnter:N,onTouchStart:L,...f}){let p;const T={id:e,className:n,"aria-label":x},P={...T,role:h,onClick:S,onFocus:w,onBlur:k,onMouseUp:oe,onMouseEnter:N,onTouchStart:L},_=Ee(i,S);return o?p=i?a.createElement("a",T,t):a.createElement(Me,Object.assign({},P,{url:o,external:r,target:s,download:c},f),t):p=a.createElement("button",Object.assign({},P,{"aria-disabled":i,type:u?"submit":"button","aria-busy":m?!0:void 0,"aria-controls":y,"aria-expanded":B,"aria-describedby":M,"aria-checked":C,"aria-pressed":b,onKeyDown:O,onKeyUp:j,onKeyPress:A,onClick:_,tabIndex:i?-1:void 0},f),t),p}class we extends Error{constructor(t=""){super(`${t&&`${t} `}Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.`),this.name="MissingAppProviderError"}}function ke(){const e=v.useContext(ye);if(!e)throw new we("No i18n was provided.");return e}function He({id:e,children:t,url:n,disabled:o,external:r,download:s,target:c,submit:u,loading:i,pressed:m,accessibilityLabel:b,role:x,ariaControls:h,ariaExpanded:y,ariaDescribedBy:B,ariaChecked:M,onClick:C,onFocus:S,onBlur:w,onKeyDown:k,onKeyPress:O,onKeyUp:A,onMouseEnter:j,onTouchStart:N,onPointerDown:L,icon:f,disclosure:p,removeUnderline:T,size:P="medium",textAlign:_="center",fullWidth:ie,dataPrimaryLink:se,tone:W,variant:$="secondary"}){const le=ke(),D=o||i,{mdUp:ce}=J(),ue=E(l.Button,l.pressable,l[g("variant",$)],l[g("size",P)],l[g("textAlign",_)],ie&&l.fullWidth,p&&l.disclosure,f&&t&&l.iconWithText,f&&t==null&&l.iconOnly,D&&l.disabled,i&&l.loading,m&&!o&&!n&&l.pressed,T&&l.removeUnderline,W&&l[g("tone",W)]),de=p?a.createElement("span",{className:i?l.hidden:l.Icon},a.createElement(Z,{source:i?"placeholder":Ae(p,te,ee)})):null,q=Oe(f)?a.createElement(Z,{source:i?"placeholder":f}):f,me=q?a.createElement("span",{className:i?l.hidden:l.Icon},q):null,H=["plain","monochromePlain"].includes($);let F="medium";H?F="regular":$==="primary"&&(F=ce?"medium":"semibold");let V="bodySm";(P==="large"||H&&P!=="micro")&&(V="bodyMd");const fe=t?a.createElement(R,{as:"span",variant:V,fontWeight:F,key:o?"text-disabled":"text"},t):null,pe=i?a.createElement("span",{className:l.Spinner},a.createElement(Te,{size:"small",accessibilityLabel:le.translate("Polaris.Button.spinnerAccessibilityLabel")})):null,be={id:e,className:ue,accessibilityLabel:b,ariaDescribedBy:B,role:x,onClick:C,onFocus:S,onBlur:w,onMouseUp:oe,onMouseEnter:j,onTouchStart:N,"data-primary-link":se},xe={url:n,external:r,download:s,target:c},Pe={submit:u,disabled:D,loading:i,ariaControls:h,ariaExpanded:y,ariaChecked:M,pressed:m,onKeyDown:k,onKeyUp:A,onKeyPress:O,onPointerDown:L};return a.createElement(Ce,Object.assign({},be,xe,Pe),pe,me,fe,de)}function Oe(e){return typeof e=="string"||typeof e=="object"&&e.body||typeof e=="function"}function Ae(e,t,n){return e==="select"?ne:e==="up"?t:n}export{He as B,Z as I,Te as S,R as T,Me as U,$e as a,te as b,E as c,ee as d,De as e,Ue as f,Fe as g,oe as h,We as i,ze as j,Re as k,_e as s,ke as u,g as v,qe as w};
