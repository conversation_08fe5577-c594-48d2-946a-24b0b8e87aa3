import * as React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { InstantNavButton } from "@/components/navigation/InstantNavigation";

interface DashboardHeaderProps {
  totalProducts: number;
  optimizedCount: number;
  onNavigate: (path: string) => void;
}

export function DashboardHeader({ totalProducts, optimizedCount, onNavigate }: DashboardHeaderProps) {
  const optimizationPercentage = totalProducts > 0 ? Math.round((optimizedCount / totalProducts) * 100) : 0;

  return (
    <div className="relative bg-black text-white py-20 px-6 overflow-hidden">
      {/* Animated Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="grid grid-cols-12 gap-4 h-full">
          {Array.from({ length: 48 }).map((_, i) => (
            <motion.div
              key={i}
              className="bg-white rounded-sm"
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: [0, 0.3, 0], scale: [0, 1, 0] }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.1,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Main Content */}
          <div>
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="mb-6"
            >
              <div className="inline-flex items-center bg-white/10 rounded-full px-4 py-2 mb-4">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse" />
                <span className="text-sm font-medium">Live Dashboard</span>
              </div>
              
              <h1
                style={{
                  fontSize: 'clamp(4rem, 8vw, 8rem)',
                  fontWeight: 900,
                  lineHeight: 0.8,
                  letterSpacing: '-0.05em',
                  color: 'white',
                  marginBottom: '3rem'
                }}
              >
                SEO Command
                <br />
                <span style={{ color: '#9CA3AF' }}>Center</span>
              </h1>

              <p
                style={{
                  fontSize: '1.25rem',
                  fontWeight: 500,
                  color: '#D1D5DB',
                  lineHeight: 1.6,
                  maxWidth: '48rem',
                  marginBottom: '2rem'
                }}
              >
                Monitor, analyze, and optimize your entire product catalog from one powerful dashboard.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
              className="flex flex-wrap gap-4"
            >
              <InstantNavButton
                to="/app/seo-dashboard"
                size="lg"
                className="bg-white text-black hover:bg-gray-100"
              >
                Optimize Products
              </InstantNavButton>
            </motion.div>
          </div>

          {/* Right Side - Stats Display */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            className="lg:text-right"
          >
            <div className="bg-white/5 backdrop-blur-sm rounded-3xl p-8 border border-white/10">
              <div className="mb-6">
                <div className="text-4xl font-black mb-2">{totalProducts}</div>
                <div className="text-gray-300 font-medium">Total Products</div>
              </div>
              
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-300">Optimized</span>
                  <span className="font-bold">{optimizationPercentage}%</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <motion.div
                    className="bg-white rounded-full h-2"
                    initial={{ width: 0 }}
                    animate={{ width: `${optimizationPercentage}%` }}
                    transition={{ duration: 1.5, delay: 0.5, ease: "easeOut" }}
                  />
                </div>
              </div>
              
              <div className="text-sm text-gray-400">
                {optimizedCount} of {totalProducts} products optimized
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
