import "@shopify/shopify-app-remix/adapters/node";
import {
  ApiVersion,
  AppDistribution,
  DeliveryMethod,
  shopifyApp,
} from "@shopify/shopify-app-remix/server";
import { restResources } from "@shopify/shopify-api/rest/admin/2025-01";
import { PrismaSessionStorage } from "@shopify/shopify-app-session-storage-prisma";
import type { Session } from "@shopify/shopify-api";
import db from "./db.server";

// Simple session storage with logging for session tokens
class SessionTokenStorage extends PrismaSessionStorage<Session> {
  constructor(prisma: any) {
    super(prisma);
  }

  async storeSession(session: any): Promise<boolean> {
    try {
      console.log(`💾 [SESSION-TOKEN-STORAGE] Storing session for shop: ${session.shop}`);
      const result = await super.storeSession(session);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Session stored successfully for shop: ${session.shop}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to store session for shop: ${session.shop}`, error);
      return false;
    }
  }

  async loadSession(id: string): Promise<any> {
    try {
      console.log(`🔍 [SESSION-TOKEN-STORAGE] Loading session: ${id}`);
      const session = await super.loadSession(id);
      if (session) {
        console.log(`✅ [SESSION-TOKEN-STORAGE] Session loaded for shop: ${session.shop}`);
      } else {
        console.log(`⚠️ [SESSION-TOKEN-STORAGE] No session found for ID: ${id}`);
      }
      return session;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to load session: ${id}`, error);
      return undefined;
    }
  }

  async deleteSession(id: string): Promise<boolean> {
    try {
      console.log(`🗑️ [SESSION-TOKEN-STORAGE] Deleting session: ${id}`);
      const result = await super.deleteSession(id);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Session deleted: ${id}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to delete session: ${id}`, error);
      return false;
    }
  }

  async deleteSessions(ids: string[]): Promise<boolean> {
    try {
      console.log(`🗑️ [SESSION-TOKEN-STORAGE] Deleting sessions: ${ids.join(', ')}`);
      const result = await super.deleteSessions(ids);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Sessions deleted: ${ids.join(', ')}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to delete sessions: ${ids.join(', ')}`, error);
      return false;
    }
  }

  async findSessionsByShop(shop: string): Promise<Session[]> {
    try {
      console.log(`🔍 [SESSION-TOKEN-STORAGE] Finding sessions for shop: ${shop}`);
      const result = await super.findSessionsByShop(shop);
      console.log(`✅ [SESSION-TOKEN-STORAGE] Found ${result.length} sessions for shop: ${shop}`);
      return result;
    } catch (error) {
      console.error(`❌ [SESSION-TOKEN-STORAGE] Failed to find sessions for shop: ${shop}`, error);
      return [];
    }
  }
}

const shopify = shopifyApp({
  apiKey: process.env.SHOPIFY_API_KEY,
  apiSecretKey: process.env.SHOPIFY_API_SECRET || "",
  apiVersion: ApiVersion.January25,
  scopes: process.env.SCOPES?.split(","),
  appUrl: process.env.SHOPIFY_APP_URL || "",
  authPathPrefix: "/auth",
  sessionStorage: new SessionTokenStorage(db),
  distribution: AppDistribution.AppStore,
  restResources,
  future: {
    // Disable the problematic new auth strategy - it causes 302 redirect loops
    unstable_newEmbeddedAuthStrategy: false,
  },
  // Session configuration for maximum persistence
  useOnlineTokens: false, // Use offline tokens for better persistence
  exitIframePath: "/exitiframe",
  // Force session persistence
  isEmbeddedApp: true,
  // Enhanced webhook configuration
  webhooks: {
    APP_UNINSTALLED: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/uninstalled",
    },
    APP_SUBSCRIPTIONS_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_subscriptions/update",
    },
    APP_PURCHASES_ONE_TIME_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app_purchases_one_time/update",
    },
    APP_SCOPES_UPDATE: {
      deliveryMethod: DeliveryMethod.Http,
      callbackUrl: "/webhooks/app/scopes_update",
    },
  },
  // Enhanced authentication configuration
  auth: {
    path: "/auth",
    callbackPath: "/auth/callback",
  },
  ...(process.env.SHOP_CUSTOM_DOMAIN
    ? { customShopDomains: [process.env.SHOP_CUSTOM_DOMAIN] }
    : {}),
});

export default shopify;
export const apiVersion = ApiVersion.January25;
export const addDocumentResponseHeaders = shopify.addDocumentResponseHeaders;
export const authenticate = shopify.authenticate;
export const unauthenticated = shopify.unauthenticated;
export const login = shopify.login;
export const registerWebhooks = shopify.registerWebhooks;
export const sessionStorage = shopify.sessionStorage;
