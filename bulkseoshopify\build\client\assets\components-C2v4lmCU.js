var Rt=Object.defineProperty;var xt=(e,r,t)=>r in e?Rt(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t;var B=(e,r,t)=>xt(e,typeof r!="symbol"?r+"":r,t);import{r as o,a as Je}from"./index-BJYSoprK.js";import{R as bt}from"./index-Ci0627_k.js";import{l as Lt,D as ge,g as te,R as _t,h as Tt,N as z,s as I,i as Ee,u as Ct,a as $,j as re,k as pe,b as Ot,n as kt,o as Ye,p as F,I as Pt,q as Dt,r as Nt,t as De,v as At,A as Ft,w as It,x as Ve,y as jt,z as Xe,E as Q,f as $t,m as Ke,B as Ut,C as Ht,F as Mt,G as zt}from"./index-DI88vBYx.js";/**
 * React Router DOM v6.30.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function D(){return D=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},D.apply(this,arguments)}function Se(e,r){if(e==null)return{};var t={},n=Object.keys(e),a,i;for(i=0;i<n.length;i++)a=n[i],!(r.indexOf(a)>=0)&&(t[a]=e[a]);return t}const W="get",ce="application/x-www-form-urlencoded";function ne(e){return e!=null&&typeof e.tagName=="string"}function Bt(e){return ne(e)&&e.tagName.toLowerCase()==="button"}function Jt(e){return ne(e)&&e.tagName.toLowerCase()==="form"}function Yt(e){return ne(e)&&e.tagName.toLowerCase()==="input"}function Vt(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Xt(e,r){return e.button===0&&(!r||r==="_self")&&!Vt(e)}let J=null;function Kt(){if(J===null)try{new FormData(document.createElement("form"),0),J=!1}catch{J=!0}return J}const Wt=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function de(e){return e!=null&&!Wt.has(e)?null:e}function Gt(e,r){let t,n,a,i,u;if(Jt(e)){let s=e.getAttribute("action");n=s?I(s,r):null,t=e.getAttribute("method")||W,a=de(e.getAttribute("enctype"))||ce,i=new FormData(e)}else if(Bt(e)||Yt(e)&&(e.type==="submit"||e.type==="image")){let s=e.form;if(s==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||s.getAttribute("action");if(n=l?I(l,r):null,t=e.getAttribute("formmethod")||s.getAttribute("method")||W,a=de(e.getAttribute("formenctype"))||de(s.getAttribute("enctype"))||ce,i=new FormData(s,e),!Kt()){let{name:c,type:m,value:d}=e;if(m==="image"){let f=c?c+".":"";i.append(f+"x","0"),i.append(f+"y","0")}else c&&i.append(c,d)}}else{if(ne(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');t=W,n=null,a=ce,u=e}return i&&a==="text/plain"&&(u=i,i=void 0),{action:n,method:t.toLowerCase(),encType:a,formData:i,body:u}}const Zt=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Qt=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],qt=["fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"],er="6";try{window.__reactRouterVersion=er}catch{}const We=o.createContext({isTransitioning:!1}),Ge=o.createContext(new Map),tr="startTransition",Ne=Je[tr],rr="flushSync",Ae=bt[rr],nr="useId",Fe=Je[nr];function ar(e){Ne?Ne(e):e()}function H(e){Ae?Ae(e):e()}let ir=class{constructor(){this.status="pending",this.promise=new Promise((r,t)=>{this.resolve=n=>{this.status==="pending"&&(this.status="resolved",r(n))},this.reject=n=>{this.status==="pending"&&(this.status="rejected",t(n))}})}};function jn(e){let{fallbackElement:r,router:t,future:n}=e,[a,i]=o.useState(t.state),[u,s]=o.useState(),[l,c]=o.useState({isTransitioning:!1}),[m,d]=o.useState(),[f,p]=o.useState(),[h,v]=o.useState(),S=o.useRef(new Map),{v7_startTransition:R}=n||{},w=o.useCallback(x=>{R?ar(x):x()},[R]),E=o.useCallback((x,_)=>{let{deletedFetchers:T,flushSync:k,viewTransitionOpts:O}=_;x.fetchers.forEach((N,P)=>{N.data!==void 0&&S.current.set(P,N.data)}),T.forEach(N=>S.current.delete(N));let se=t.window==null||t.window.document==null||typeof t.window.document.startViewTransition!="function";if(!O||se){k?H(()=>i(x)):w(()=>i(x));return}if(k){H(()=>{f&&(m&&m.resolve(),f.skipTransition()),c({isTransitioning:!0,flushSync:!0,currentLocation:O.currentLocation,nextLocation:O.nextLocation})});let N=t.window.document.startViewTransition(()=>{H(()=>i(x))});N.finished.finally(()=>{H(()=>{d(void 0),p(void 0),s(void 0),c({isTransitioning:!1})})}),H(()=>p(N));return}f?(m&&m.resolve(),f.skipTransition(),v({state:x,currentLocation:O.currentLocation,nextLocation:O.nextLocation})):(s(x),c({isTransitioning:!0,flushSync:!1,currentLocation:O.currentLocation,nextLocation:O.nextLocation}))},[t.window,f,m,S,w]);o.useLayoutEffect(()=>t.subscribe(E),[t,E]),o.useEffect(()=>{l.isTransitioning&&!l.flushSync&&d(new ir)},[l]),o.useEffect(()=>{if(m&&u&&t.window){let x=u,_=m.promise,T=t.window.document.startViewTransition(async()=>{w(()=>i(x)),await _});T.finished.finally(()=>{d(void 0),p(void 0),s(void 0),c({isTransitioning:!1})}),p(T)}},[w,u,m,t.window]),o.useEffect(()=>{m&&u&&a.location.key===u.location.key&&m.resolve()},[m,f,a.location,u]),o.useEffect(()=>{!l.isTransitioning&&h&&(s(h.state),c({isTransitioning:!0,flushSync:!1,currentLocation:h.currentLocation,nextLocation:h.nextLocation}),v(void 0))},[l.isTransitioning,h]),o.useEffect(()=>{},[]);let y=o.useMemo(()=>({createHref:t.createHref,encodeLocation:t.encodeLocation,go:x=>t.navigate(x),push:(x,_,T)=>t.navigate(x,{state:_,preventScrollReset:T==null?void 0:T.preventScrollReset}),replace:(x,_,T)=>t.navigate(x,{replace:!0,state:_,preventScrollReset:T==null?void 0:T.preventScrollReset})}),[t]),b=t.basename||"/",g=o.useMemo(()=>({router:t,navigator:y,static:!1,basename:b}),[t,y,b]),L=o.useMemo(()=>({v7_relativeSplatPath:t.future.v7_relativeSplatPath}),[t.future.v7_relativeSplatPath]);return o.useEffect(()=>Lt(n,t.future),[n,t.future]),o.createElement(o.Fragment,null,o.createElement(ge.Provider,{value:g},o.createElement(te.Provider,{value:a},o.createElement(Ge.Provider,{value:S.current},o.createElement(We.Provider,{value:l},o.createElement(_t,{basename:b,location:a.location,navigationType:a.historyAction,navigator:y,future:L},a.initialized||t.future.v7_partialHydration?o.createElement(or,{routes:t.routes,future:t.future,state:a}):r))))),null)}const or=o.memo(lr);function lr(e){let{routes:r,future:t,state:n}=e;return Tt(r,void 0,n,t)}const sr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ur=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ze=o.forwardRef(function(r,t){let{onClick:n,relative:a,reloadDocument:i,replace:u,state:s,target:l,to:c,preventScrollReset:m,viewTransition:d}=r,f=Se(r,Zt),{basename:p}=o.useContext(z),h,v=!1;if(typeof c=="string"&&ur.test(c)&&(h=c,sr))try{let E=new URL(window.location.href),y=c.startsWith("//")?new URL(E.protocol+c):new URL(c),b=I(y.pathname,p);y.origin===E.origin&&b!=null?c=b+y.search+y.hash:v=!0}catch{}let S=Ee(c,{relative:a}),R=dr(c,{replace:u,state:s,target:l,preventScrollReset:m,relative:a,viewTransition:d});function w(E){n&&n(E),E.defaultPrevented||R(E)}return o.createElement("a",D({},f,{href:h||S,onClick:v||i?n:w,ref:t,target:l}))}),cr=o.forwardRef(function(r,t){let{"aria-current":n="page",caseSensitive:a=!1,className:i="",end:u=!1,style:s,to:l,viewTransition:c,children:m}=r,d=Se(r,Qt),f=re(l,{relative:d.relative}),p=$(),h=o.useContext(te),{navigator:v,basename:S}=o.useContext(z),R=h!=null&&vr(f)&&c===!0,w=v.encodeLocation?v.encodeLocation(f).pathname:f.pathname,E=p.pathname,y=h&&h.navigation&&h.navigation.location?h.navigation.location.pathname:null;a||(E=E.toLowerCase(),y=y?y.toLowerCase():null,w=w.toLowerCase()),y&&S&&(y=I(y,S)||y);const b=w!=="/"&&w.endsWith("/")?w.length-1:w.length;let g=E===w||!u&&E.startsWith(w)&&E.charAt(b)==="/",L=y!=null&&(y===w||!u&&y.startsWith(w)&&y.charAt(w.length)==="/"),x={isActive:g,isPending:L,isTransitioning:R},_=g?n:void 0,T;typeof i=="function"?T=i(x):T=[i,g?"active":null,L?"pending":null,R?"transitioning":null].filter(Boolean).join(" ");let k=typeof s=="function"?s(x):s;return o.createElement(Ze,D({},d,{"aria-current":_,className:T,ref:t,style:k,to:l,viewTransition:c}),typeof m=="function"?m(x):m)}),Qe=o.forwardRef((e,r)=>{let{fetcherKey:t,navigate:n,reloadDocument:a,replace:i,state:u,method:s=W,action:l,onSubmit:c,relative:m,preventScrollReset:d,viewTransition:f}=e,p=Se(e,qt),h=tt(),v=mr(l,{relative:m}),S=s.toLowerCase()==="get"?"get":"post",R=w=>{if(c&&c(w),w.defaultPrevented)return;w.preventDefault();let E=w.nativeEvent.submitter,y=(E==null?void 0:E.getAttribute("formmethod"))||s;h(E||w.currentTarget,{fetcherKey:t,method:y,navigate:n,replace:i,state:u,relative:m,preventScrollReset:d,viewTransition:f})};return o.createElement("form",D({ref:r,method:S,action:v,onSubmit:a?c:R},p))});var j;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(j||(j={}));var q;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(q||(q={}));function ae(e){let r=o.useContext(ge);return r||F(!1),r}function qe(e){let r=o.useContext(te);return r||F(!1),r}function dr(e,r){let{target:t,replace:n,state:a,preventScrollReset:i,relative:u,viewTransition:s}=r===void 0?{}:r,l=Ct(),c=$(),m=re(e,{relative:u});return o.useCallback(d=>{if(Xt(d,t)){d.preventDefault();let f=n!==void 0?n:pe(c)===pe(m);l(e,{replace:f,state:a,preventScrollReset:i,relative:u,viewTransition:s})}},[c,l,m,n,a,t,e,i,u,s])}function fr(){if(typeof document>"u")throw new Error("You are calling submit during the server render. Try calling submit within a `useEffect` or callback instead.")}let hr=0,et=()=>"__"+String(++hr)+"__";function tt(){let{router:e}=ae(j.UseSubmit),{basename:r}=o.useContext(z),t=Dt();return o.useCallback(function(n,a){a===void 0&&(a={}),fr();let{action:i,method:u,encType:s,formData:l,body:c}=Gt(n,r);if(a.navigate===!1){let m=a.fetcherKey||et();e.fetch(m,t,a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||u,formEncType:a.encType||s,flushSync:a.flushSync})}else e.navigate(a.action||i,{preventScrollReset:a.preventScrollReset,formData:l,body:c,formMethod:a.method||u,formEncType:a.encType||s,replace:a.replace,state:a.state,fromRouteId:t,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,r,t])}function mr(e,r){let{relative:t}=r===void 0?{}:r,{basename:n}=o.useContext(z),a=o.useContext(Ye);a||F(!1);let[i]=a.matches.slice(-1),u=D({},re(e||".",{relative:t})),s=$();if(e==null){u.search=s.search;let l=new URLSearchParams(u.search),c=l.getAll("index");if(c.some(d=>d==="")){l.delete("index"),c.filter(f=>f).forEach(f=>l.append("index",f));let d=l.toString();u.search=d?"?"+d:""}}return(!e||e===".")&&i.route.index&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(u.pathname=u.pathname==="/"?n:Nt([n,u.pathname])),pe(u)}function pr(e){var r;let{key:t}=e===void 0?{}:e,{router:n}=ae(j.UseFetcher),a=qe(q.UseFetcher),i=o.useContext(Ge),u=o.useContext(Ye),s=(r=u.matches[u.matches.length-1])==null?void 0:r.route.id;i||F(!1),u||F(!1),s==null&&F(!1);let l=Fe?Fe():"",[c,m]=o.useState(t||l);t&&t!==c?m(t):c||m(et()),o.useEffect(()=>(n.getFetcher(c),()=>{n.deleteFetcher(c)}),[n,c]);let d=o.useCallback((w,E)=>{s||F(!1),n.fetch(c,s,w,E)},[c,s,n]),f=tt(),p=o.useCallback((w,E)=>{f(w,D({},E,{navigate:!1,fetcherKey:c}))},[c,f]),h=o.useMemo(()=>o.forwardRef((E,y)=>o.createElement(Qe,D({},E,{navigate:!1,fetcherKey:c,ref:y}))),[c]),v=a.fetchers.get(c)||Pt,S=i.get(c);return o.useMemo(()=>D({Form:h,submit:p,load:d},v,{data:S}),[h,p,d,v,S])}const Ie="react-router-scroll-positions";let Y={};function $n(e){let{getKey:r,storageKey:t}=e===void 0?{}:e,{router:n}=ae(j.UseScrollRestoration),{restoreScrollPosition:a,preventScrollReset:i}=qe(q.UseScrollRestoration),{basename:u}=o.useContext(z),s=$(),l=Ot(),c=kt();o.useEffect(()=>(window.history.scrollRestoration="manual",()=>{window.history.scrollRestoration="auto"}),[]),yr(o.useCallback(()=>{if(c.state==="idle"){let m=(r?r(s,l):null)||s.key;Y[m]=window.scrollY}try{sessionStorage.setItem(t||Ie,JSON.stringify(Y))}catch{}window.history.scrollRestoration="auto"},[t,r,c.state,s,l])),typeof document<"u"&&(o.useLayoutEffect(()=>{try{let m=sessionStorage.getItem(t||Ie);m&&(Y=JSON.parse(m))}catch{}},[t]),o.useLayoutEffect(()=>{let m=r&&u!=="/"?(f,p)=>r(D({},f,{pathname:I(f.pathname,u)||f.pathname}),p):r,d=n==null?void 0:n.enableScrollRestoration(Y,()=>window.scrollY,m);return()=>d&&d()},[n,u,r]),o.useLayoutEffect(()=>{if(a!==!1){if(typeof a=="number"){window.scrollTo(0,a);return}if(s.hash){let m=document.getElementById(decodeURIComponent(s.hash.slice(1)));if(m){m.scrollIntoView();return}}i!==!0&&window.scrollTo(0,0)}},[s,a,i]))}function yr(e,r){let{capture:t}={};o.useEffect(()=>{let n=t!=null?{capture:t}:void 0;return window.addEventListener("pagehide",e,n),()=>{window.removeEventListener("pagehide",e,n)}},[e,t])}function vr(e,r){r===void 0&&(r={});let t=o.useContext(We);t==null&&F(!1);let{basename:n}=ae(j.useViewTransitionState),a=re(e,{relative:r.relative});if(!t.isTransitioning)return!1;let i=I(t.currentLocation.pathname,n)||t.currentLocation.pathname,u=I(t.nextLocation.pathname,n)||t.nextLocation.pathname;return De(a.pathname,u)!=null||De(a.pathname,i)!=null}var wr=-1,gr=-2,Er=-3,Sr=-4,Rr=-5,xr=-6,br=-7,Lr="B",_r="D",rt="E",Tr="M",Cr="N",nt="P",Or="R",kr="S",Pr="Y",Dr="U",Nr="Z",at=class{constructor(){B(this,"promise");B(this,"resolve");B(this,"reject");this.promise=new Promise((e,r)=>{this.resolve=e,this.reject=r})}};function Ar(){const e=new TextDecoder;let r="";return new TransformStream({transform(t,n){const a=e.decode(t,{stream:!0}),i=(r+a).split(`
`);r=i.pop()||"";for(const u of i)n.enqueue(u)},flush(t){r&&t.enqueue(r)}})}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var fe=typeof window<"u"?window:typeof globalThis<"u"?globalThis:void 0;function ye(e){const{hydrated:r,values:t}=this;if(typeof e=="number")return je.call(this,e);if(!Array.isArray(e)||!e.length)throw new SyntaxError;const n=t.length;for(const a of e)t.push(a);return r.length=t.length,je.call(this,n)}function je(e){const{hydrated:r,values:t,deferred:n,plugins:a}=this;let i;const u=[[e,l=>{i=l}]];let s=[];for(;u.length>0;){const[l,c]=u.pop();switch(l){case br:c(void 0);continue;case Rr:c(null);continue;case gr:c(NaN);continue;case xr:c(1/0);continue;case Er:c(-1/0);continue;case Sr:c(-0);continue}if(r[l]){c(r[l]);continue}const m=t[l];if(!m||typeof m!="object"){r[l]=m,c(m);continue}if(Array.isArray(m))if(typeof m[0]=="string"){const[d,f,p]=m;switch(d){case _r:c(r[l]=new Date(f));continue;case Dr:c(r[l]=new URL(f));continue;case Lr:c(r[l]=BigInt(f));continue;case Or:c(r[l]=new RegExp(f,p));continue;case Pr:c(r[l]=Symbol.for(f));continue;case kr:const h=new Set;r[l]=h;for(let y=1;y<m.length;y++)u.push([m[y],b=>{h.add(b)}]);c(h);continue;case Tr:const v=new Map;r[l]=v;for(let y=1;y<m.length;y+=2){const b=[];u.push([m[y+1],g=>{b[1]=g}]),u.push([m[y],g=>{b[0]=g}]),s.push(()=>{v.set(b[0],b[1])})}c(v);continue;case Cr:const S=Object.create(null);r[l]=S;for(const y of Object.keys(f).reverse()){const b=[];u.push([f[y],g=>{b[1]=g}]),u.push([Number(y.slice(1)),g=>{b[0]=g}]),s.push(()=>{S[b[0]]=b[1]})}c(S);continue;case nt:if(r[f])c(r[l]=r[f]);else{const y=new at;n[f]=y,c(r[l]=y.promise)}continue;case rt:const[,R,w]=m;let E=w&&fe&&fe[w]?new fe[w](R):new Error(R);r[l]=E,c(E);continue;case Nr:c(r[l]=r[f]);continue;default:if(Array.isArray(a)){const y=[],b=m.slice(1);for(let g=0;g<b.length;g++){const L=b[g];u.push([L,x=>{y[g]=x}])}s.push(()=>{for(const g of a){const L=g(m[0],...y);if(L){c(r[l]=L.value);return}}throw new SyntaxError});continue}throw new SyntaxError}}else{const d=[];r[l]=d;for(let f=0;f<m.length;f++){const p=m[f];p!==wr&&u.push([p,h=>{d[f]=h}])}c(d);continue}else{const d={};r[l]=d;for(const f of Object.keys(m).reverse()){const p=[];u.push([m[f],h=>{p[1]=h}]),u.push([Number(f.slice(1)),h=>{p[0]=h}]),s.push(()=>{d[p[0]]=p[1]})}c(d);continue}}for(;s.length>0;)s.pop()();return i}async function Fr(e,r){const{plugins:t}=r??{},n=new at,a=e.pipeThrough(Ar()).getReader(),i={values:[],hydrated:[],deferred:{},plugins:t},u=await Ir.call(i,a);let s=n.promise;return u.done?n.resolve():s=jr.call(i,a).then(n.resolve).catch(l=>{for(const c of Object.values(i.deferred))c.reject(l);n.reject(l)}),{done:s.then(()=>a.closed),value:u.value}}async function Ir(e){const r=await e.read();if(!r.value)throw new SyntaxError;let t;try{t=JSON.parse(r.value)}catch{throw new SyntaxError}return{done:r.done,value:ye.call(this,t)}}async function jr(e){let r=await e.read();for(;!r.done;){if(!r.value)continue;const t=r.value;switch(t[0]){case nt:{const n=t.indexOf(":"),a=Number(t.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const u=t.slice(n+1);let s;try{s=JSON.parse(u)}catch{throw new SyntaxError}const l=ye.call(this,s);i.resolve(l);break}case rt:{const n=t.indexOf(":"),a=Number(t.slice(1,n)),i=this.deferred[a];if(!i)throw new Error(`Deferred ID ${a} not found in stream`);const u=t.slice(n+1);let s;try{s=JSON.parse(u)}catch{throw new SyntaxError}const l=ye.call(this,s);i.reject(l);break}default:throw new SyntaxError}r=await e.read()}}/**
 * @remix-run/server-runtime v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const it=Symbol("SingleFetchRedirect");/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function C(){return C=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},C.apply(this,arguments)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function A(e,r){if(e===!1||e===null||typeof e>"u")throw new Error(r)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */async function ot(e,r){if(e.id in r)return r[e.id];try{let t=await import(e.module);return r[e.id]=t,t}catch(t){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(t),window.__remixContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function $r(e,r,t){let n=e.map(i=>{var u;let s=r[i.route.id],l=t.routes[i.route.id];return[l.css?l.css.map(c=>({rel:"stylesheet",href:c})):[],(s==null||(u=s.links)===null||u===void 0?void 0:u.call(s))||[]]}).flat(2),a=Jr(e,t);return st(n,a)}async function lt(e,r){var t,n;if(!e.css&&!r.links||!Vr())return;let a=[((t=e.css)===null||t===void 0?void 0:t.map(s=>({rel:"stylesheet",href:s})))??[],((n=r.links)===null||n===void 0?void 0:n.call(r))??[]].flat(1);if(a.length===0)return;let i=[];for(let s of a)!Re(s)&&s.rel==="stylesheet"&&i.push({...s,rel:"preload",as:"style"});let u=i.filter(s=>(!s.media||window.matchMedia(s.media).matches)&&!document.querySelector(`link[rel="stylesheet"][href="${s.href}"]`));await Promise.all(u.map(Ur))}async function Ur(e){return new Promise(r=>{let t=document.createElement("link");Object.assign(t,e);function n(){document.head.contains(t)&&document.head.removeChild(t)}t.onload=()=>{n(),r()},t.onerror=()=>{n(),r()},document.head.appendChild(t)})}function Re(e){return e!=null&&typeof e.page=="string"}function Hr(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function Mr(e,r,t){let n=await Promise.all(e.map(async a=>{let i=await ot(r.routes[a.route.id],t);return i.links?i.links():[]}));return st(n.flat(1).filter(Hr).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function $e(e,r,t,n,a,i,u){let s=ut(e),l=(d,f)=>t[f]?d.route.id!==t[f].route.id:!0,c=(d,f)=>{var p;return t[f].pathname!==d.pathname||((p=t[f].route.path)===null||p===void 0?void 0:p.endsWith("*"))&&t[f].params["*"]!==d.params["*"]};return u==="data"&&(i.v3_singleFetch||a.search!==s.search)?r.filter((d,f)=>{if(!n.routes[d.route.id].hasLoader)return!1;if(l(d,f)||c(d,f))return!0;let h=i.v3_singleFetch||a.search!==s.search;if(d.route.shouldRevalidate){var v;let S=d.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((v=t[0])===null||v===void 0?void 0:v.params)||{},nextUrl:new URL(e,window.origin),nextParams:d.params,defaultShouldRevalidate:h});if(typeof S=="boolean")return S}return h}):r.filter((d,f)=>{let p=n.routes[d.route.id];return(u==="assets"||p.hasLoader)&&(l(d,f)||c(d,f))})}function zr(e,r,t){let n=ut(e);return xe(r.filter(a=>t.routes[a.route.id].hasLoader&&!t.routes[a.route.id].hasClientLoader).map(a=>{let{pathname:i,search:u}=n,s=new URLSearchParams(u);return s.set("_data",a.route.id),`${i}?${s}`}))}function Br(e,r){return xe(e.map(t=>{let n=r.routes[t.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function Jr(e,r){return xe(e.map(t=>{let n=r.routes[t.route.id],a=[n.module];return n.imports&&(a=a.concat(n.imports)),a}).flat(1))}function xe(e){return[...new Set(e)]}function Yr(e){let r={},t=Object.keys(e).sort();for(let n of t)r[n]=e[n];return r}function st(e,r){let t=new Set,n=new Set(r);return e.reduce((a,i)=>{if(r&&!Re(i)&&i.as==="script"&&i.href&&n.has(i.href))return a;let s=JSON.stringify(Yr(i));return t.has(s)||(t.add(s),a.push({key:s,link:i})),a},[])}function ut(e){let r=At(e);return r.search===void 0&&(r.search=""),r}let V;function Vr(){if(V!==void 0)return V;let e=document.createElement("link");return V=e.relList.supports("preload"),e=null,V}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const Xr={"&":"\\u0026",">":"\\u003e","<":"\\u003c","\u2028":"\\u2028","\u2029":"\\u2029"},Kr=/[&><\u2028\u2029]/g;function X(e){return e.replace(Kr,r=>Xr[r])}function Ue(e){return{__html:e}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Wr(e){return e.headers.get("X-Remix-Catch")!=null}function Gr(e){return e.headers.get("X-Remix-Error")!=null}function Zr(e){return be(e)&&e.status>=400&&e.headers.get("X-Remix-Error")==null&&e.headers.get("X-Remix-Catch")==null&&e.headers.get("X-Remix-Response")==null}function Qr(e){return e.headers.get("X-Remix-Redirect")!=null}function qr(e){var r;return!!((r=e.headers.get("Content-Type"))!==null&&r!==void 0&&r.match(/text\/remix-deferred/))}function be(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function en(e){let r=e;return r&&typeof r=="object"&&typeof r.data=="object"&&typeof r.subscribe=="function"&&typeof r.cancel=="function"&&typeof r.resolveData=="function"}async function ct(e,r,t=0){let n=new URL(e.url);n.searchParams.set("_data",r),t>0&&await new Promise(s=>setTimeout(s,5**t*10));let a=await ie(e),i=window.__remixRevalidation,u=await fetch(n.href,a).catch(s=>{if(typeof i=="number"&&i===window.__remixRevalidation&&(s==null?void 0:s.name)==="TypeError"&&t<3)return ct(e,r,t+1);throw s});if(Gr(u)){let s=await u.json(),l=new Error(s.message);return l.stack=s.stack,l}if(Zr(u)){let s=await u.text(),l=new Error(s);return l.stack=void 0,l}return u}async function ie(e){let r={signal:e.signal};if(e.method!=="GET"){r.method=e.method;let t=e.headers.get("Content-Type");t&&/\bapplication\/json\b/.test(t)?(r.headers={"Content-Type":t},r.body=JSON.stringify(await e.json())):t&&/\btext\/plain\b/.test(t)?(r.headers={"Content-Type":t},r.body=await e.text()):t&&/\bapplication\/x-www-form-urlencoded\b/.test(t)?r.body=new URLSearchParams(await e.text()):r.body=await e.formData()}return r}const tn="__deferred_promise:";async function rn(e){if(!e)throw new Error("parseDeferredReadableStream requires stream argument");let r,t={};try{let n=nn(e),i=(await n.next()).value;if(!i)throw new Error("no critical data");let u=JSON.parse(i);if(typeof u=="object"&&u!==null)for(let[s,l]of Object.entries(u))typeof l!="string"||!l.startsWith(tn)||(r=r||{},r[s]=new Promise((c,m)=>{t[s]={resolve:d=>{c(d),delete t[s]},reject:d=>{m(d),delete t[s]}}}));return(async()=>{try{for await(let s of n){let[l,...c]=s.split(":"),m=c.join(":"),d=JSON.parse(m);if(l==="data")for(let[f,p]of Object.entries(d))t[f]&&t[f].resolve(p);else if(l==="error")for(let[f,p]of Object.entries(d)){let h=new Error(p.message);h.stack=p.stack,t[f]&&t[f].reject(h)}}for(let[s,l]of Object.entries(t))l.reject(new Ft(`Deferred ${s} will never be resolved`))}catch(s){for(let l of Object.values(t))l.reject(s)}})(),new It({...u,...r})}catch(n){for(let a of Object.values(t))a.reject(n);throw n}}async function*nn(e){let r=e.getReader(),t=[],n=[],a=!1,i=new TextEncoder,u=new TextDecoder,s=async()=>{if(n.length>0)return n.shift();for(;!a&&n.length===0;){let c=await r.read();if(c.done){a=!0;break}t.push(c.value);try{let d=u.decode(He(...t)).split(`

`);if(d.length>=2&&(n.push(...d.slice(0,-1)),t=[i.encode(d.slice(-1).join(`

`))]),n.length>0)break}catch{continue}}return n.length>0||t.length>0&&(n=u.decode(He(...t)).split(`

`).filter(m=>m),t=[]),n.shift()},l=await s();for(;l;)yield l,l=await s()}function He(...e){let r=new Uint8Array(e.reduce((n,a)=>n+a.length,0)),t=0;for(let n of e)r.set(n,t),t+=n.length;return r}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Un(e,r,t){return async({request:n,matches:a,fetcherKey:i})=>n.method!=="GET"?an(n,a):i?ln(n,a):on(e,r,t(),n,a)}async function an(e,r){let t=r.find(i=>i.shouldLoad);A(t,"No action match found");let n,a=await t.resolve(async i=>await i(async()=>{let s=oe(e.url),l=await ie(e),{data:c,status:m}=await Le(s,l);return n=m,ve(c,t.route.id)}));return be(a.result)||Ve(a.result)?{[t.route.id]:a}:{[t.route.id]:{type:a.type,result:jt(a.result,n)}}}async function on(e,r,t,n,a){let i=new Set,u=!1,s=a.map(()=>Me()),l=Promise.all(s.map(h=>h.promise)),c=Me(),m=ft(oe(n.url)),d=await ie(n),f={},p=Promise.all(a.map(async(h,v)=>h.resolve(async S=>{if(s[v].resolve(),!h.shouldLoad){var R;if(!t.state.initialized)return;if(h.route.id in t.state.loaderData&&e.routes[h.route.id]&&(R=r[h.route.id])!==null&&R!==void 0&&R.shouldRevalidate){e.routes[h.route.id].hasLoader&&(u=!0);return}}if(e.routes[h.route.id].hasClientLoader){e.routes[h.route.id].hasLoader&&(u=!0);try{let w=await dt(S,m,d,h.route.id);f[h.route.id]={type:"data",result:w}}catch(w){f[h.route.id]={type:"error",result:w}}return}e.routes[h.route.id].hasLoader&&i.add(h.route.id);try{let w=await S(async()=>{let E=await c.promise;return ht(E,h.route.id)});f[h.route.id]={type:"data",result:w}}catch(w){f[h.route.id]={type:"error",result:w}}})));if(await l,(!t.state.initialized||i.size===0)&&!window.__remixHdrActive)c.resolve({});else try{u&&i.size>0&&m.searchParams.set("_routes",a.filter(v=>i.has(v.route.id)).map(v=>v.route.id).join(","));let h=await Le(m,d);c.resolve(h.data)}catch(h){c.reject(h)}return await p,f}async function ln(e,r){let t=r.find(a=>a.shouldLoad);A(t,"No fetcher match found");let n=await t.resolve(async a=>{let i=ft(oe(e.url)),u=await ie(e);return dt(a,i,u,t.route.id)});return{[t.route.id]:n}}function dt(e,r,t,n){return e(async()=>{let a=new URL(r);a.searchParams.set("_routes",n);let{data:i}=await Le(a,t);return ht(i,n)})}function ft(e){let r=e.searchParams.getAll("index");e.searchParams.delete("index");let t=[];for(let n of r)n&&t.push(n);for(let n of t)e.searchParams.append("index",n);return e}function oe(e){let r=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return r.pathname==="/"?r.pathname="_root.data":r.pathname=`${r.pathname.replace(/\/$/,"")}.data`,r}async function Le(e,r){let t=await fetch(e,r);if(new Set([100,101,204,205]).has(t.status))return!r.method||r.method==="GET"?{status:t.status,data:{}}:{status:t.status,data:{data:null}};A(t.body,"No response body to decode");try{let a=await sn(t.body,window);return{status:t.status,data:a.value}}catch(a){throw console.error(a),new Error(`Unable to decode turbo-stream response from URL: ${e.toString()}`)}}function sn(e,r){return Fr(e,{plugins:[(t,...n)=>{if(t==="SanitizedError"){let[a,i,u]=n,s=Error;a&&a in r&&typeof r[a]=="function"&&(s=r[a]);let l=new s(i);return l.stack=u,{value:l}}if(t==="ErrorResponse"){let[a,i,u]=n;return{value:new Q(i,u,a)}}if(t==="SingleFetchRedirect")return{value:{[it]:n[0]}}},(t,n)=>{if(t==="SingleFetchFallback")return{value:void 0};if(t==="SingleFetchClassInstance")return{value:n}}]})}function ht(e,r){let t=e[it];return t?ve(t,r):e[r]!==void 0?ve(e[r],r):null}function ve(e,r){if("error"in e)throw e.error;if("redirect"in e){let t={};throw e.revalidate&&(t["X-Remix-Revalidate"]="yes"),e.reload&&(t["X-Remix-Reload-Document"]="yes"),e.replace&&(t["X-Remix-Replace"]="yes"),Xe(e.redirect,{status:e.status,headers:t})}else{if("data"in e)return e.data;throw new Error(`No response found for routeId "${r}"`)}}function Me(){let e,r,t=new Promise((n,a)=>{e=async i=>{n(i);try{await t}catch{}},r=async i=>{a(i);try{await t}catch{}}});return{promise:t,resolve:e,reject:r}}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */class Hn extends o.Component{constructor(r){super(r),this.state={error:r.error||null,location:r.location}}static getDerivedStateFromError(r){return{error:r}}static getDerivedStateFromProps(r,t){return t.location!==r.location?{error:r.error||null,location:r.location}:{error:r.error||t.error,location:t.location}}render(){return this.state.error?o.createElement(mt,{error:this.state.error,isOutsideRemixApp:!0}):this.props.children}}function mt({error:e,isOutsideRemixApp:r}){console.error(e);let t=o.createElement("script",{dangerouslySetInnerHTML:{__html:`
        console.log(
          "💿 Hey developer 👋. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."
        );
      `}});if(Ve(e))return o.createElement(we,{title:"Unhandled Thrown Response!"},o.createElement("h1",{style:{fontSize:"24px"}},e.status," ",e.statusText),t);let n;if(e instanceof Error)n=e;else{let a=e==null?"Unknown Error":typeof e=="object"&&"toString"in e?e.toString():JSON.stringify(e);n=new Error(a)}return o.createElement(we,{title:"Application Error!",isOutsideRemixApp:r},o.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),o.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},n.stack),t)}function we({title:e,renderScripts:r,isOutsideRemixApp:t,children:n}){var a;let{routeModules:i}=U();return(a=i.root)!==null&&a!==void 0&&a.Layout&&!t?n:o.createElement("html",{lang:"en"},o.createElement("head",null,o.createElement("meta",{charSet:"utf-8"}),o.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),o.createElement("title",null,e)),o.createElement("body",null,o.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},n,r?o.createElement(On,null):null)))}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function un(){return o.createElement(we,{title:"Loading...",renderScripts:!0},o.createElement("script",{dangerouslySetInnerHTML:{__html:`
              console.log(
                "💿 Hey developer 👋. You can provide a way better UX than this " +
                "when your app is loading JS modules and/or running \`clientLoader\` " +
                "functions. Check out https://remix.run/route/hydrate-fallback " +
                "for more information."
              );
            `}}))}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pt(e){let r={};return Object.values(e).forEach(t=>{let n=t.parentId||"";r[n]||(r[n]=[]),r[n].push(t)}),r}function cn(e,r,t){let n=yt(r),a=r.HydrateFallback&&(!t||e.id==="root")?r.HydrateFallback:e.id==="root"?un:void 0,i=r.ErrorBoundary?r.ErrorBoundary:e.id==="root"?()=>o.createElement(mt,{error:$t()}):void 0;return e.id==="root"&&r.Layout?{...n?{element:o.createElement(r.Layout,null,o.createElement(n,null))}:{Component:n},...i?{errorElement:o.createElement(r.Layout,null,o.createElement(i,null))}:{ErrorBoundary:i},...a?{hydrateFallbackElement:o.createElement(r.Layout,null,o.createElement(a,null))}:{HydrateFallback:a}}:{Component:n,ErrorBoundary:i,HydrateFallback:a}}function Mn(e,r,t,n,a,i){return _e(r,t,n,a,i,"",pt(r),e)}function K(e,r,t){if(t){let u=`You cannot call ${e==="action"?"serverAction()":"serverLoader()"} in SPA Mode (routeId: "${r.id}")`;throw console.error(u),new Q(400,"Bad Request",new Error(u),!0)}let a=`You are trying to call ${e==="action"?"serverAction()":"serverLoader()"} on a route that does not have a server ${e} (routeId: "${r.id}")`;if(e==="loader"&&!r.hasLoader||e==="action"&&!r.hasAction)throw console.error(a),new Q(400,"Bad Request",new Error(a),!0)}function he(e,r){let t=e==="clientAction"?"a":"an",n=`Route "${r}" does not have ${t} ${e}, but you are trying to submit to it. To fix this, please add ${t} \`${e}\` function to the route`;throw console.error(n),new Q(405,"Method Not Allowed",new Error(n),!0)}function _e(e,r,t,n,a,i="",u=pt(e),s){return(u[i]||[]).map(l=>{let c=r[l.id];async function m(E,y,b){if(typeof b=="function")return await b();let g=await hn(E,l);return y?mn(g):g}function d(E,y,b){return l.hasLoader?m(E,y,b):Promise.resolve(null)}function f(E,y,b){if(!l.hasAction)throw he("action",l.id);return m(E,y,b)}async function p(E){let y=r[l.id],b=y?lt(l,y):Promise.resolve();try{return E()}finally{await b}}let h={id:l.id,index:l.index,path:l.path};if(c){var v,S,R;Object.assign(h,{...h,...cn(l,c,a),handle:c.handle,shouldRevalidate:ze(n,c,l.id,s)});let E=t==null||(v=t.loaderData)===null||v===void 0?void 0:v[l.id],y=t==null||(S=t.errors)===null||S===void 0?void 0:S[l.id],b=s==null&&(((R=c.clientLoader)===null||R===void 0?void 0:R.hydrate)===!0||!l.hasLoader);h.loader=async({request:g,params:L},x)=>{try{return await p(async()=>(A(c,"No `routeModule` available for critical-route loader"),c.clientLoader?c.clientLoader({request:g,params:L,async serverLoader(){if(K("loader",l,a),b){if(E!==void 0)return E;if(y!==void 0)throw y;return null}return d(g,!0,x)}}):a?null:d(g,!1,x)))}finally{b=!1}},h.loader.hydrate=yn(l,c,a),h.action=({request:g,params:L},x)=>p(async()=>{if(A(c,"No `routeModule` available for critical-route action"),!c.clientAction){if(a)throw he("clientAction",l.id);return f(g,!1,x)}return c.clientAction({request:g,params:L,async serverAction(){return K("action",l,a),f(g,!0,x)}})})}else l.hasClientLoader||(h.loader=({request:E},y)=>p(()=>a?Promise.resolve(null):d(E,!1,y))),l.hasClientAction||(h.action=({request:E},y)=>p(()=>{if(a)throw he("clientAction",l.id);return f(E,!1,y)})),h.lazy=async()=>{let E=await fn(l,r),y={...E};if(E.clientLoader){let b=E.clientLoader;y.loader=(g,L)=>b({...g,async serverLoader(){return K("loader",l,a),d(g.request,!0,L)}})}if(E.clientAction){let b=E.clientAction;y.action=(g,L)=>b({...g,async serverAction(){return K("action",l,a),f(g.request,!0,L)}})}return{...y.loader?{loader:y.loader}:{},...y.action?{action:y.action}:{},hasErrorBoundary:y.hasErrorBoundary,shouldRevalidate:ze(n,y,l.id,s),handle:y.handle,Component:y.Component,ErrorBoundary:y.ErrorBoundary}};let w=_e(e,r,t,n,a,l.id,u,s);return w.length>0&&(h.children=w),h})}function ze(e,r,t,n){if(n)return dn(t,r.shouldRevalidate,n);if(e.v3_singleFetch&&r.shouldRevalidate){let a=r.shouldRevalidate;return i=>a({...i,defaultShouldRevalidate:!0})}return r.shouldRevalidate}function dn(e,r,t){let n=!1;return a=>n?r?r(a):a.defaultShouldRevalidate:(n=!0,t.has(e))}async function fn(e,r){let t=await ot(e,r);return await lt(e,t),{Component:yt(t),ErrorBoundary:t.ErrorBoundary,clientAction:t.clientAction,clientLoader:t.clientLoader,handle:t.handle,links:t.links,meta:t.meta,shouldRevalidate:t.shouldRevalidate}}async function hn(e,r){let t=await ct(e,r.id);if(t instanceof Error)throw t;if(Qr(t))throw pn(t);if(Wr(t))throw t;return qr(t)&&t.body?await rn(t.body):t}function mn(e){if(en(e))return e.data;if(be(e)){let r=e.headers.get("Content-Type");return r&&/\bapplication\/json\b/.test(r)?e.json():e.text()}return e}function pn(e){let r=parseInt(e.headers.get("X-Remix-Status"),10)||302,t=e.headers.get("X-Remix-Redirect"),n={},a=e.headers.get("X-Remix-Revalidate");a&&(n["X-Remix-Revalidate"]=a);let i=e.headers.get("X-Remix-Reload-Document");i&&(n["X-Remix-Reload-Document"]=i);let u=e.headers.get("X-Remix-Replace");return u&&(n["X-Remix-Replace"]=u),Xe(t,{status:r,headers:n})}function yt(e){if(e.default==null)return;if(!(typeof e.default=="object"&&Object.keys(e.default).length===0))return e.default}function yn(e,r,t){return t&&e.id!=="root"||r.clientLoader!=null&&(r.clientLoader.hydrate===!0||e.hasLoader!==!0)}/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const G=new Set,vn=1e3,ee=new Set,wn=7680;function Te(e,r){return e.v3_lazyRouteDiscovery===!0&&!r}function gn(e,r){let t=new Set(r.state.matches.map(u=>u.route.id)),n=r.state.location.pathname.split("/").filter(Boolean),a=["/"];for(n.pop();n.length>0;)a.push(`/${n.join("/")}`),n.pop();a.forEach(u=>{let s=Ke(r.routes,u,r.basename);s&&s.forEach(l=>t.add(l.route.id))});let i=[...t].reduce((u,s)=>Object.assign(u,{[s]:e.routes[s]}),{});return{...e,routes:i}}function zn(e,r,t,n,a){if(Te(t,n))return async({path:i,patch:u,signal:s,fetcherKey:l})=>{ee.has(i)||await vt([i],l?window.location.href:i,e,r,t,n,a,u,s)}}function Bn(e,r,t,n,a){o.useEffect(()=>{var i;if(!Te(n,a)||((i=navigator.connection)===null||i===void 0?void 0:i.saveData)===!0)return;function u(d){let f=d.tagName==="FORM"?d.getAttribute("action"):d.getAttribute("href");if(!f)return;let p=new URL(f,window.location.origin);ee.has(p.pathname)||G.add(p.pathname)}async function s(){let d=Array.from(G.keys()).filter(f=>ee.has(f)?(G.delete(f),!1):!0);if(d.length!==0)try{await vt(d,null,r,t,n,a,e.basename,e.patchRoutes)}catch(f){console.error("Failed to fetch manifest patches",f)}}document.body.querySelectorAll("a[data-discover], form[data-discover]").forEach(d=>u(d)),s();let l=Sn(s,100);function c(d){return d.nodeType===Node.ELEMENT_NODE}let m=new MutationObserver(d=>{let f=new Set;d.forEach(p=>{[p.target,...p.addedNodes].forEach(h=>{c(h)&&((h.tagName==="A"&&h.getAttribute("data-discover")||h.tagName==="FORM"&&h.getAttribute("data-discover"))&&f.add(h),h.tagName!=="A"&&h.querySelectorAll("a[data-discover], form[data-discover]").forEach(v=>f.add(v)))})}),f.forEach(p=>u(p)),l()});return m.observe(document.documentElement,{subtree:!0,childList:!0,attributes:!0,attributeFilter:["data-discover","href","action"]}),()=>m.disconnect()},[n,a,r,t,e])}const me="remix-manifest-version";async function vt(e,r,t,n,a,i,u,s,l){let c=`${u??"/"}/__manifest`.replace(/\/+/g,"/"),m=new URL(c,window.location.origin);if(e.sort().forEach(v=>m.searchParams.append("p",v)),m.searchParams.set("version",t.version),m.toString().length>wn){G.clear();return}let d;try{let v=await fetch(m,{signal:l});if(v.ok){if(v.status===204&&v.headers.has("X-Remix-Reload-Document")){if(!r){console.warn("Detected a manifest version mismatch during eager route discovery. The next navigation/fetch to an undiscovered route will result in a new document navigation to sync up with the latest manifest.");return}if(sessionStorage.getItem(me)===t.version){console.error("Unable to discover routes due to manifest version mismatch.");return}throw sessionStorage.setItem(me,t.version),window.location.href=r,new Error("Detected manifest version mismatch, reloading...")}else if(v.status>=400)throw new Error(await v.text())}else throw new Error(`${v.status} ${v.statusText}`);sessionStorage.removeItem(me),d=await v.json()}catch(v){if(l!=null&&l.aborted)return;throw v}let f=new Set(Object.keys(t.routes)),p=Object.values(d).reduce((v,S)=>f.has(S.id)?v:Object.assign(v,{[S.id]:S}),{});Object.assign(t.routes,p),e.forEach(v=>En(v,ee));let h=new Set;Object.values(p).forEach(v=>{(!v.parentId||!p[v.parentId])&&h.add(v.parentId)}),h.forEach(v=>s(v||null,_e(p,n,null,a,i,v)))}function En(e,r){if(r.size>=vn){let t=r.values().next().value;typeof t=="string"&&r.delete(t)}r.add(e)}function Sn(e,r){let t;return(...n)=>{window.clearTimeout(t),t=window.setTimeout(()=>e(...n),r)}}function wt(){let e=o.useContext(ge);return A(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function le(){let e=o.useContext(te);return A(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}const gt=o.createContext(void 0);gt.displayName="Remix";function U(){let e=o.useContext(gt);return A(e,"You must render this element inside a <Remix> element"),e}function Et(e,r){let[t,n]=o.useState(!1),[a,i]=o.useState(!1),{onFocus:u,onBlur:s,onMouseEnter:l,onMouseLeave:c,onTouchStart:m}=r,d=o.useRef(null);o.useEffect(()=>{if(e==="render"&&i(!0),e==="viewport"){let h=S=>{S.forEach(R=>{i(R.isIntersecting)})},v=new IntersectionObserver(h,{threshold:.5});return d.current&&v.observe(d.current),()=>{v.disconnect()}}},[e]);let f=()=>{e==="intent"&&n(!0)},p=()=>{e==="intent"&&(n(!1),i(!1))};return o.useEffect(()=>{if(t){let h=setTimeout(()=>{i(!0)},100);return()=>{clearTimeout(h)}}},[t]),[a,d,{onFocus:M(u,f),onBlur:M(s,p),onMouseEnter:M(l,f),onMouseLeave:M(c,p),onTouchStart:M(m,f)}]}const Ce=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i;function Oe(e,r,t){return e==="render"&&!r&&!t?"true":void 0}let Rn=o.forwardRef(({to:e,prefetch:r="none",discover:t="render",...n},a)=>{let i=typeof e=="string"&&Ce.test(e),u=Ee(e),[s,l,c]=Et(r,n);return o.createElement(o.Fragment,null,o.createElement(cr,C({},n,c,{ref:St(a,l),to:e,"data-discover":Oe(t,i,n.reloadDocument)})),s&&!i?o.createElement(Pe,{page:u}):null)});Rn.displayName="NavLink";let xn=o.forwardRef(({to:e,prefetch:r="none",discover:t="render",...n},a)=>{let i=typeof e=="string"&&Ce.test(e),u=Ee(e),[s,l,c]=Et(r,n);return o.createElement(o.Fragment,null,o.createElement(Ze,C({},n,c,{ref:St(a,l),to:e,"data-discover":Oe(t,i,n.reloadDocument)})),s&&!i?o.createElement(Pe,{page:u}):null)});xn.displayName="Link";let bn=o.forwardRef(({discover:e="render",...r},t)=>{let n=typeof r.action=="string"&&Ce.test(r.action);return o.createElement(Qe,C({},r,{ref:t,"data-discover":Oe(e,n,r.reloadDocument)}))});bn.displayName="Form";function M(e,r){return t=>{e&&e(t),t.defaultPrevented||r(t)}}function ke(e,r,t){if(t&&!Z)return[e[0]];if(r){let n=e.findIndex(a=>r[a.route.id]!==void 0);return e.slice(0,n+1)}return e}function Jn(){let{isSpaMode:e,manifest:r,routeModules:t,criticalCss:n}=U(),{errors:a,matches:i}=le(),u=ke(i,a,e),s=o.useMemo(()=>$r(u,t,r),[u,t,r]);return o.createElement(o.Fragment,null,n?o.createElement("style",{dangerouslySetInnerHTML:{__html:n}}):null,s.map(({key:l,link:c})=>Re(c)?o.createElement(Pe,C({key:l},c)):o.createElement("link",C({key:l},c))))}function Pe({page:e,...r}){let{router:t}=wt(),n=o.useMemo(()=>Ke(t.routes,e,t.basename),[t.routes,e,t.basename]);return n?o.createElement(_n,C({page:e,matches:n},r)):(console.warn(`Tried to prefetch ${e} but no routes matched.`),null)}function Ln(e){let{manifest:r,routeModules:t}=U(),[n,a]=o.useState([]);return o.useEffect(()=>{let i=!1;return Mr(e,r,t).then(u=>{i||a(u)}),()=>{i=!0}},[e,r,t]),n}function _n({page:e,matches:r,...t}){let n=$(),{future:a,manifest:i,routeModules:u}=U(),{loaderData:s,matches:l}=le(),c=o.useMemo(()=>$e(e,r,l,i,n,a,"data"),[e,r,l,i,n,a]),m=o.useMemo(()=>{if(!a.v3_singleFetch)return zr(e,c,i);if(e===n.pathname+n.search+n.hash)return[];let h=new Set,v=!1;if(r.forEach(R=>{var w;i.routes[R.route.id].hasLoader&&(!c.some(E=>E.route.id===R.route.id)&&R.route.id in s&&(w=u[R.route.id])!==null&&w!==void 0&&w.shouldRevalidate||i.routes[R.route.id].hasClientLoader?v=!0:h.add(R.route.id))}),h.size===0)return[];let S=oe(e);return v&&h.size>0&&S.searchParams.set("_routes",r.filter(R=>h.has(R.route.id)).map(R=>R.route.id).join(",")),[S.pathname+S.search]},[a.v3_singleFetch,s,n,i,c,r,e,u]),d=o.useMemo(()=>$e(e,r,l,i,n,a,"assets"),[e,r,l,i,n,a]),f=o.useMemo(()=>Br(d,i),[d,i]),p=Ln(d);return o.createElement(o.Fragment,null,m.map(h=>o.createElement("link",C({key:h,rel:"prefetch",as:"fetch",href:h},t))),f.map(h=>o.createElement("link",C({key:h,rel:"modulepreload",href:h},t))),p.map(({key:h,link:v})=>o.createElement("link",C({key:h},v))))}function Yn(){let{isSpaMode:e,routeModules:r}=U(),{errors:t,matches:n,loaderData:a}=le(),i=$(),u=ke(n,t,e),s=null;t&&(s=t[u[u.length-1].route.id]);let l=[],c=null,m=[];for(let d=0;d<u.length;d++){let f=u[d],p=f.route.id,h=a[p],v=f.params,S=r[p],R=[],w={id:p,data:h,meta:[],params:f.params,pathname:f.pathname,handle:f.route.handle,error:s};if(m[d]=w,S!=null&&S.meta?R=typeof S.meta=="function"?S.meta({data:h,params:v,location:i,matches:m,error:s}):Array.isArray(S.meta)?[...S.meta]:S.meta:c&&(R=[...c]),R=R||[],!Array.isArray(R))throw new Error("The route at "+f.route.path+` returns an invalid value. All route meta functions must return an array of meta objects.

To reference the meta function API, see https://remix.run/route/meta`);w.meta=R,m[d]=w,l=[...R],c=l}return o.createElement(o.Fragment,null,l.flat().map(d=>{if(!d)return null;if("tagName"in d){let{tagName:f,...p}=d;if(!Tn(f))return console.warn(`A meta object uses an invalid tagName: ${f}. Expected either 'link' or 'meta'`),null;let h=f;return o.createElement(h,C({key:JSON.stringify(p)},p))}if("title"in d)return o.createElement("title",{key:"title"},String(d.title));if("charset"in d&&(d.charSet??(d.charSet=d.charset),delete d.charset),"charSet"in d&&d.charSet!=null)return typeof d.charSet=="string"?o.createElement("meta",{key:"charSet",charSet:d.charSet}):null;if("script:ld+json"in d)try{let f=JSON.stringify(d["script:ld+json"]);return o.createElement("script",{key:`script:ld+json:${f}`,type:"application/ld+json",dangerouslySetInnerHTML:{__html:f}})}catch{return null}return o.createElement("meta",C({key:JSON.stringify(d)},d))}))}function Tn(e){return typeof e=="string"&&/^(meta|link)$/.test(e)}function Cn(e){return o.createElement(Ut,e)}let Z=!1;function On(e){let{manifest:r,serverHandoffString:t,abortDelay:n,serializeError:a,isSpaMode:i,future:u,renderMeta:s}=U(),{router:l,static:c,staticContext:m}=wt(),{matches:d}=le(),f=Te(u,i);s&&(s.didRenderScripts=!0);let p=ke(d,null,i);o.useEffect(()=>{Z=!0},[]);let h=(g,L)=>{let x;return a&&L instanceof Error?x=a(L):x=L,`${JSON.stringify(g)}:__remixContext.p(!1, ${X(JSON.stringify(x))})`},v=(g,L,x)=>{let _;try{_=JSON.stringify(x)}catch(T){return h(L,T)}return`${JSON.stringify(L)}:__remixContext.p(${X(_)})`},S=(g,L,x)=>{let _;return a&&x instanceof Error?_=a(x):_=x,`__remixContext.r(${JSON.stringify(g)}, ${JSON.stringify(L)}, !1, ${X(JSON.stringify(_))})`},R=(g,L,x)=>{let _;try{_=JSON.stringify(x)}catch(T){return S(g,L,T)}return`__remixContext.r(${JSON.stringify(g)}, ${JSON.stringify(L)}, ${X(_)})`},w=[],E=o.useMemo(()=>{var g;let L=u.v3_singleFetch?"window.__remixContext.stream = new ReadableStream({start(controller){window.__remixContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());":"",x=m?`window.__remixContext = ${t};${L}`:" ",_=u.v3_singleFetch||m==null?void 0:m.activeDeferreds;x+=_?["__remixContext.p = function(v,e,p,x) {","  if (typeof e !== 'undefined') {",`    x=new Error("Unexpected Server Error");
    x.stack=undefined;`,"    p=Promise.reject(x);","  } else {","    p=Promise.resolve(v);","  }","  return p;","};","__remixContext.n = function(i,k) {","  __remixContext.t = __remixContext.t || {};","  __remixContext.t[i] = __remixContext.t[i] || {};","  let p = new Promise((r, e) => {__remixContext.t[i][k] = {r:(v)=>{r(v);},e:(v)=>{e(v);}};});",typeof n=="number"?`setTimeout(() => {if(typeof p._error !== "undefined" || typeof p._data !== "undefined"){return;} __remixContext.t[i][k].e(new Error("Server timeout."))}, ${n});`:"","  return p;","};","__remixContext.r = function(i,k,v,e,p,x) {","  p = __remixContext.t[i][k];","  if (typeof e !== 'undefined') {",`    x=new Error("Unexpected Server Error");
    x.stack=undefined;`,"    p.e(x);","  } else {","    p.r(v);","  }","};"].join(`
`)+Object.entries(_).map(([k,O])=>{let se=new Set(O.pendingKeys),N=O.deferredKeys.map(P=>{if(se.has(P))return w.push(o.createElement(Be,{key:`${k} | ${P}`,deferredData:O,routeId:k,dataKey:P,scriptProps:e,serializeData:R,serializeError:S})),`${JSON.stringify(P)}:__remixContext.n(${JSON.stringify(k)}, ${JSON.stringify(P)})`;{let ue=O.data[P];return typeof ue._error<"u"?h(P,ue._error):v(k,P,ue._data)}}).join(`,
`);return`Object.assign(__remixContext.state.loaderData[${JSON.stringify(k)}], {${N}});`}).join(`
`)+(w.length>0?`__remixContext.a=${w.length};`:""):"";let T=c?`${(g=r.hmr)!==null&&g!==void 0&&g.runtime?`import ${JSON.stringify(r.hmr.runtime)};`:""}${f?"":`import ${JSON.stringify(r.url)}`};
${p.map((k,O)=>`import * as route${O} from ${JSON.stringify(r.routes[k.route.id].module)};`).join(`
`)}
${f?`window.__remixManifest = ${JSON.stringify(gn(r,l),null,2)};`:""}
window.__remixRouteModules = {${p.map((k,O)=>`${JSON.stringify(k.route.id)}:route${O}`).join(",")}};

import(${JSON.stringify(r.entry.module)});`:" ";return o.createElement(o.Fragment,null,o.createElement("script",C({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ue(x),type:void 0})),o.createElement("script",C({},e,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ue(T),type:"module",async:!0})))},[]);if(!c&&typeof __remixContext=="object"&&__remixContext.a)for(let g=0;g<__remixContext.a;g++)w.push(o.createElement(Be,{key:g,scriptProps:e,serializeData:R,serializeError:S}));let y=p.map(g=>{let L=r.routes[g.route.id];return(L.imports||[]).concat([L.module])}).flat(1),b=Z?[]:r.entry.imports.concat(y);return Z?null:o.createElement(o.Fragment,null,f?null:o.createElement("link",{rel:"modulepreload",href:r.url,crossOrigin:e.crossOrigin}),o.createElement("link",{rel:"modulepreload",href:r.entry.module,crossOrigin:e.crossOrigin}),Pn(b).map(g=>o.createElement("link",{key:g,rel:"modulepreload",href:g,crossOrigin:e.crossOrigin})),E,w)}function Be({dataKey:e,deferredData:r,routeId:t,scriptProps:n,serializeData:a,serializeError:i}){return typeof document>"u"&&r&&e&&t&&A(r.pendingKeys.includes(e),`Deferred data for route ${t} with key ${e} was not pending but tried to render a script for it.`),o.createElement(o.Suspense,{fallback:typeof document>"u"&&r&&e&&t?null:o.createElement("script",C({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}}))},typeof document>"u"&&r&&e&&t?o.createElement(Cn,{resolve:r.data[e],errorElement:o.createElement(kn,{dataKey:e,routeId:t,scriptProps:n,serializeError:i}),children:u=>o.createElement("script",C({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:a(t,e,u)}}))}):o.createElement("script",C({},n,{async:!0,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:" "}})))}function kn({dataKey:e,routeId:r,scriptProps:t,serializeError:n}){let a=Ht();return o.createElement("script",C({},t,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:n(r,e,a)}}))}function Pn(e){return[...new Set(e)]}function Vn(){return Mt()}function Xn(){return zt()}function Kn(e={}){return pr(e)}function St(...e){return r=>{e.forEach(t=>{typeof t=="function"?t(r):t!=null&&(t.current=r)})}}export{bn as F,Jn as L,Yn as M,gt as R,On as S,C as _,Kn as a,U as b,$n as c,Xn as d,sn as e,_e as f,zn as g,Un as h,A as i,Mn as j,Bn as k,Hn as l,jn as m,xn as n,yn as s,Vn as u};
