/**
 * Enhanced Caching Service
 * Provides multi-layer caching with memory, database, and Redis support
 */

import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { CacheEntry, CacheOptions } from "../types";

export interface CacheStats {
  memoryHits: number;
  memoryMisses: number;
  databaseHits: number;
  databaseMisses: number;
  totalEntries: number;
  memoryUsage: number;
}

/**
 * Enhanced caching service with multiple storage layers
 */
export class CacheService {
  private static instance: CacheService;
  private memoryCache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    memoryHits: 0,
    memoryMisses: 0,
    databaseHits: 0,
    databaseMisses: 0,
    totalEntries: 0,
    memoryUsage: 0
  };
  private maxMemoryEntries = 1000;
  private defaultTTL = 300000; // 5 minutes

  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService();
    }
    return CacheService.instance;
  }

  /**
   * Get cached value with fallback to database
   */
  async get<T>(key: string, options?: CacheOptions, context?: ErrorContext): Promise<T | null> {
    try {
      const fullKey = this.buildKey(key, options?.namespace);

      // Try memory cache first
      const memoryEntry = this.memoryCache.get(fullKey);
      if (memoryEntry && !this.isExpired(memoryEntry)) {
        this.stats.memoryHits++;
        console.log(`🎯 Memory cache hit: ${fullKey}`);
        return memoryEntry.data as T;
      }

      if (memoryEntry) {
        // Remove expired entry
        this.memoryCache.delete(fullKey);
      }
      this.stats.memoryMisses++;

      // Try database cache
      try {
        const dbEntry = await db.cacheEntry.findUnique({
          where: { key: fullKey }
        });

        if (dbEntry && new Date() < dbEntry.expiresAt) {
          this.stats.databaseHits++;
          console.log(`💾 Database cache hit: ${fullKey}`);

          // Store in memory for faster access
          const cacheEntry: CacheEntry<T> = {
            data: JSON.parse(dbEntry.data),
            timestamp: dbEntry.createdAt.getTime(),
            ttl: dbEntry.expiresAt.getTime() - Date.now(),
            key: fullKey
          };
          this.setMemoryCache(fullKey, cacheEntry);

          return cacheEntry.data;
        }

        if (dbEntry) {
          // Remove expired entry
          await db.cacheEntry.delete({ where: { key: fullKey } });
        }
      } catch (error) {
        console.warn('⚠️ Database cache lookup failed:', error);
      }

      this.stats.databaseMisses++;
      console.log(`❌ Cache miss: ${fullKey}`);
      return null;

    } catch (error) {
      console.error('❌ Cache get error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache get failed'),
        { ...context, action: 'cache_get', metadata: { key } }
      );
      return null;
    }
  }

  /**
   * Set cached value in both memory and database
   */
  async set<T>(
    key: string, 
    value: T, 
    options?: CacheOptions, 
    context?: ErrorContext
  ): Promise<void> {
    try {
      const fullKey = this.buildKey(key, options?.namespace);
      const ttl = options?.ttl || this.defaultTTL;
      const expiresAt = new Date(Date.now() + ttl);

      const cacheEntry: CacheEntry<T> = {
        data: value,
        timestamp: Date.now(),
        ttl,
        key: fullKey
      };

      // Set in memory cache
      this.setMemoryCache(fullKey, cacheEntry);

      // Set in database cache for persistence
      try {
        await db.cacheEntry.upsert({
          where: { key: fullKey },
          update: {
            data: JSON.stringify(value),
            expiresAt,
            tags: options?.tags?.join(',') || null,
            updatedAt: new Date()
          },
          create: {
            key: fullKey,
            data: JSON.stringify(value),
            expiresAt,
            tags: options?.tags?.join(',') || null
          }
        });

        console.log(`💾 Cache set: ${fullKey} (TTL: ${ttl}ms)`);
      } catch (error) {
        console.warn('⚠️ Database cache set failed:', error);
        // Continue with memory cache only
      }

    } catch (error) {
      console.error('❌ Cache set error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache set failed'),
        { ...context, action: 'cache_set', metadata: { key } }
      );
    }
  }

  /**
   * Delete cached value
   */
  async delete(key: string, options?: CacheOptions, context?: ErrorContext): Promise<void> {
    try {
      const fullKey = this.buildKey(key, options?.namespace);

      // Remove from memory
      this.memoryCache.delete(fullKey);

      // Remove from database
      try {
        await db.cacheEntry.delete({
          where: { key: fullKey }
        });
      } catch (error) {
        console.warn('⚠️ Database cache delete failed:', error);
      }

      console.log(`🗑️ Cache deleted: ${fullKey}`);

    } catch (error) {
      console.error('❌ Cache delete error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache delete failed'),
        { ...context, action: 'cache_delete', metadata: { key } }
      );
    }
  }

  /**
   * Clear cache by tags
   */
  async clearByTags(tags: string[], context?: ErrorContext): Promise<void> {
    try {
      // Clear from database
      for (const tag of tags) {
        await db.cacheEntry.deleteMany({
          where: {
            tags: {
              contains: tag
            }
          }
        });
      }

      // Clear from memory (simple approach - clear all)
      this.memoryCache.clear();
      this.stats.totalEntries = 0;
      this.stats.memoryUsage = 0;

      console.log(`🧹 Cache cleared by tags: ${tags.join(', ')}`);

    } catch (error) {
      console.error('❌ Cache clear by tags error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache clear by tags failed'),
        { ...context, action: 'cache_clear_tags', metadata: { tags } }
      );
    }
  }

  /**
   * Get or set cached value with fallback function
   */
  async getOrSet<T>(
    key: string,
    fallback: () => Promise<T>,
    options?: CacheOptions,
    context?: ErrorContext
  ): Promise<T> {
    const cached = await this.get<T>(key, options, context);
    
    if (cached !== null) {
      return cached;
    }

    console.log(`🔄 Cache miss, executing fallback for: ${key}`);
    const value = await fallback();
    await this.set(key, value, options, context);
    
    return value;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.stats.totalEntries = this.memoryCache.size;
    this.stats.memoryUsage = this.calculateMemoryUsage();
    return { ...this.stats };
  }

  /**
   * Clear all cache
   */
  async clearAll(context?: ErrorContext): Promise<void> {
    try {
      // Clear memory
      this.memoryCache.clear();

      // Clear database
      await db.cacheEntry.deleteMany({});

      // Reset stats
      this.stats = {
        memoryHits: 0,
        memoryMisses: 0,
        databaseHits: 0,
        databaseMisses: 0,
        totalEntries: 0,
        memoryUsage: 0
      };

      console.log('🧹 All cache cleared');

    } catch (error) {
      console.error('❌ Cache clear all error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache clear all failed'),
        { ...context, action: 'cache_clear_all' }
      );
    }
  }

  /**
   * Clean up expired entries
   */
  async cleanup(context?: ErrorContext): Promise<void> {
    try {
      // Clean memory cache
      const now = Date.now();
      let cleaned = 0;
      
      for (const [key, entry] of this.memoryCache.entries()) {
        if (this.isExpired(entry)) {
          this.memoryCache.delete(key);
          cleaned++;
        }
      }

      // Clean database cache
      const dbCleaned = await db.cacheEntry.deleteMany({
        where: {
          expiresAt: {
            lt: new Date()
          }
        }
      });

      if (cleaned > 0 || dbCleaned.count > 0) {
        console.log(`🧹 Cache cleanup: ${cleaned} memory entries, ${dbCleaned.count} database entries`);
      }

    } catch (error) {
      console.error('❌ Cache cleanup error:', error);
      await logError(
        error instanceof Error ? error : new Error('Cache cleanup failed'),
        { ...context, action: 'cache_cleanup' }
      );
    }
  }

  /**
   * Private helper methods
   */
  private buildKey(key: string, namespace?: string): string {
    return namespace ? `${namespace}:${key}` : key;
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > (entry.timestamp + entry.ttl);
  }

  private setMemoryCache<T>(key: string, entry: CacheEntry<T>): void {
    // Implement LRU eviction if memory cache is full
    if (this.memoryCache.size >= this.maxMemoryEntries) {
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }

    this.memoryCache.set(key, entry);
  }

  private calculateMemoryUsage(): number {
    let usage = 0;
    for (const entry of this.memoryCache.values()) {
      usage += JSON.stringify(entry.data).length;
    }
    return usage;
  }
}

// Export singleton instance
export const cacheService = CacheService.getInstance();

// Schedule cleanup every 10 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    cacheService.cleanup();
  }, 10 * 60 * 1000);
}
