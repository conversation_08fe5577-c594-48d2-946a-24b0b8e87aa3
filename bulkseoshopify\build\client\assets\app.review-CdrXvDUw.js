import{j as r}from"./jsx-runtime-0DLF9kdB.js";import{r as t}from"./index-BJYSoprK.js";import{u as a}from"./index-DI88vBYx.js";import{P as i,B as s}from"./Page-Dli0YmGf.js";import{T as o}from"./TitleBar-DFMSJ8Yc.js";import{S as p,T as m}from"./Button-3T1ZHLBP.js";import"./use-is-after-initial-mount-MUb1fdtO.js";import"./context-Dt_50QHC.js";import"./index-Ci0627_k.js";function v(){const e=a();return t.useEffect(()=>{e("/app/seo-dashboard")},[e]),r.jsxs(i,{title:"SEO Review",children:[r.jsx(o,{title:"SEO Review"}),r.jsxs(s,{gap:"400",align:"center",children:[r.jsx(p,{size:"large"}),r.jsx(m,{as:"p",variant:"bodyMd",children:"Redirecting to SEO Dashboard..."})]})]})}export{v as default};
