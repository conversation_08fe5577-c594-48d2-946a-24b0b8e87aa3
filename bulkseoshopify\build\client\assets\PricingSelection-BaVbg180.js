import{j as e}from"./jsx-runtime-0DLF9kdB.js";import{r as f}from"./index-BJYSoprK.js";import{c,B as y}from"./button-RlsfHhb_.js";import{c as A,B as M,S as b,C as T,Z as w,D as _}from"./badge-CSt7rrAu.js";import{a as F}from"./components-C2v4lmCU.js";import{u as Y}from"./index-DI88vBYx.js";import{u as $}from"./useAppBridge-Bj34gXAL.js";import{m as d}from"./proxy-CdGEizrq.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],v=A("clock",R);function B({className:r,...t}){return e.jsx("div",{"data-slot":"card",className:c("bg-black text-white flex flex-col rounded-3xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.01] hover:border-white/30 backdrop-blur-sm group",r),...t})}function U({className:r,...t}){return e.jsx("div",{"data-slot":"card-header",className:c("flex flex-col space-y-3 p-8 pb-6",r),...t})}function q({className:r,...t}){return e.jsx("div",{"data-slot":"card-title",className:c("text-2xl font-black leading-tight text-white tracking-tight",r),...t})}function D({className:r,...t}){return e.jsx("div",{"data-slot":"card-description",className:c("text-white/70 text-base leading-relaxed font-medium",r),...t})}function I({className:r,...t}){return e.jsx("div",{"data-slot":"card-content",className:c("px-8 pb-8",r),...t})}function L(r){const t=r.toLowerCase();if(t.includes("authentication")||t.includes("unauthorized"))return{type:"error",title:"Authentication Required",message:"Please refresh the page and try again. If the problem persists, contact support.",action:{label:"Refresh Page",onClick:()=>window.location.reload()}};if(t.includes("rate limit")||t.includes("too many requests"))return{type:"warning",title:"Too Many Requests",message:"Please wait a moment before trying again. We limit requests to ensure system stability.",action:{label:"Try Again",onClick:()=>window.location.reload()}};if(t.includes("csrf")||t.includes("security token"))return{type:"error",title:"Security Check Failed",message:"For your security, please refresh the page and try again.",action:{label:"Refresh Page",onClick:()=>window.location.reload()}};if(t.includes("validation")||t.includes("invalid"))return{type:"error",title:"Invalid Information",message:"Please check your input and try again. Make sure all required fields are filled correctly."};if(t.includes("plan")&&t.includes("not found"))return{type:"error",title:"Plan Not Available",message:"The selected plan is no longer available. Please choose a different plan.",action:{label:"View Plans",onClick:()=>window.location.href="/app/billing/pricing"}};if(t.includes("subscription")){if(t.includes("already exists")||t.includes("duplicate"))return{type:"warning",title:"Already Subscribed",message:"You already have an active subscription. Check your billing dashboard for details.",action:{label:"View Dashboard",onClick:()=>window.location.href="/app/billing"}};if(t.includes("cancelled")||t.includes("expired"))return{type:"error",title:"Subscription Issue",message:"There was an issue with your subscription. Please try subscribing again.",action:{label:"Try Again",onClick:()=>window.location.href="/app/billing/pricing"}}}if(t.includes("payment")||t.includes("billing")||t.includes("charge"))return{type:"error",title:"Payment Issue",message:"There was a problem processing your payment. Please check your payment method and try again.",action:{label:"Try Again",onClick:()=>window.location.reload()}};if(t.includes("product count")){if(t.includes("exceed")||t.includes("maximum"))return{type:"warning",title:"Too Many Products",message:"You can optimize up to 1,000 products per purchase. Please reduce the number of selected products."};if(t.includes("minimum")||t.includes("at least"))return{type:"warning",title:"No Products Selected",message:"Please select at least one product to optimize."}}return t.includes("network")||t.includes("connection")||t.includes("timeout")?{type:"error",title:"Connection Problem",message:"Please check your internet connection and try again.",action:{label:"Retry",onClick:()=>window.location.reload()}}:t.includes("server")||t.includes("internal")?{type:"error",title:"Server Error",message:"Something went wrong on our end. Please try again in a few moments.",action:{label:"Try Again",onClick:()=>window.location.reload()}}:{type:"error",title:"Something Went Wrong",message:"An unexpected error occurred. Please try again or contact support if the problem persists.",action:{label:"Try Again",onClick:()=>window.location.reload()}}}function H(r,t){switch(r){case"subscription_created":return{type:"success",title:"Subscription Activated!",message:`Your ${t==null?void 0:t.planName} is now active. You can start optimizing your products right away.`,action:{label:"Start Optimizing",onClick:()=>window.location.href="/app"}};case"subscription_cancelled":return{type:"info",title:"Subscription Cancelled",message:"Your subscription has been cancelled. You can still use the app until your current billing period ends.",action:{label:"View Dashboard",onClick:()=>window.location.href="/app/billing"}};case"purchase_created":return{type:"success",title:"Purchase Successful!",message:`You can now optimize ${(t==null?void 0:t.productCount)||"your selected"} products. The optimization will begin shortly.`,action:{label:"View Products",onClick:()=>window.location.href="/app"}};case"trial_started":return{type:"success",title:"Free Trial Started!",message:"Your free trial is now active. Explore all features and optimize your products for free.",action:{label:"Get Started",onClick:()=>window.location.href="/app"}};default:return{type:"success",title:"Success!",message:"Your action was completed successfully."}}}function X({plans:r,onPlanSelect:t,selectedPlan:j,showPayPerUse:p=!0,csrfToken:o,hasActiveSubscription:u=!1}){const a=F(),N=Y(),k=$(),[P,S]=f.useState(j||"annual");f.useEffect(()=>{if(console.log("🔄 Fetcher state:",a.state),console.log("📋 Fetcher data:",a.data),a.data&&typeof a.data=="object"){const i=a.data;if(i.error){console.error("❌ Billing error:",i.error);const n=L(i.error),s=document.createElement("div");s.innerHTML=`
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">⚠️</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${n.title}</h4>
                <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">${n.message}</p>
                ${n.action?`
                  <button onclick="${n.action.onClick.toString()}()" style="
                    background: #dc2626; color: white; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">${n.action.label}</button>
                `:""}
              </div>
              <button onclick="this.parentElement.parentElement.remove()" style="
                background: none; border: none; color: #991b1b; cursor: pointer; font-size: 18px;
              ">×</button>
            </div>
          </div>
        `,document.body.appendChild(s),setTimeout(()=>{s.parentElement&&s.remove()},1e4)}else if(i.success&&i.confirmationUrl){console.log("✅ Subscription created successfully, redirecting to confirmation..."),console.log("🔗 Confirmation URL:",i.confirmationUrl.substring(0,100)+"...");const n=H("subscription_created",{planName:"subscription"}),s=document.createElement("div");s.innerHTML=`
          <div style="
            position: fixed; top: 20px; right: 20px; z-index: 1000;
            background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
            padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: start; gap: 12px;">
              <div style="color: #ffffff; font-size: 20px;">✅</div>
              <div style="flex: 1;">
                <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">${n.title}</h4>
                <p style="margin: 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Redirecting to payment...</p>
              </div>
            </div>
          </div>
        `,document.body.appendChild(s);try{setTimeout(()=>{window.top?window.top.location.href=i.confirmationUrl:window.location.href=i.confirmationUrl},1500)}catch(l){console.error("❌ Failed to redirect to confirmation URL:",l),s.remove();const g=document.createElement("div");g.innerHTML=`
            <div style="
              position: fixed; top: 20px; right: 20px; z-index: 1000;
              background: #000000; border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 24px;
              padding: 16px; max-width: 400px; box-shadow: 0 10px 25px rgba(0,0,0,0.3);
            ">
              <div style="display: flex; align-items: start; gap: 12px;">
                <div style="color: #ffffff; font-size: 20px;">⚠️</div>
                <div style="flex: 1;">
                  <h4 style="margin: 0 0 8px 0; font-weight: 600; color: #ffffff;">Redirect Issue</h4>
                  <p style="margin: 0 0 12px 0; color: rgba(255, 255, 255, 0.7); font-size: 14px; line-height: 1.4;">Subscription created successfully, but failed to redirect. Please refresh the page.</p>
                  <button onclick="window.location.reload()" style="
                    background: #ffffff; color: #000000; border: none; padding: 8px 16px;
                    border-radius: 4px; font-size: 12px; cursor: pointer;
                  ">Refresh Page</button>
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                  background: none; border: none; color: rgba(255, 255, 255, 0.7); cursor: pointer; font-size: 18px;
                ">×</button>
              </div>
            </div>
          `,document.body.appendChild(g)}}}a.state==="loading"&&console.log("⏳ Billing request in progress..."),a.state==="idle"&&a.data&&console.log("✅ Billing request completed")},[a.data,a.state]);const m=i=>{S(i),t==null||t(i)},C=i=>{if(console.log("🔄 Starting trial for plan:",i),console.log("🔐 CSRF token available:",!!o),!i||typeof i!="string"){console.error("❌ Invalid plan ID:",i),alert("Error: Invalid plan selected");return}if(!o){console.error("❌ No CSRF token available"),alert("Security token missing. Please refresh the page and try again.");return}if(a.state==="submitting"||a.state==="loading"){console.log("⏳ Already processing subscription request...");return}const n=new FormData;n.append("action","create_subscription"),n.append("planId",i),o&&n.append("csrfToken",o),console.log("📤 Submitting billing form with data:",{action:"create_subscription",planId:i,hasCSRF:!!o}),a.submit(n,{method:"POST"})},z=i=>{switch(i){case"annual":return e.jsx(b,{className:"w-6 h-6 text-yellow-500"});case"monthly":return e.jsx(v,{className:"w-6 h-6 text-blue-500"});case"pay_per_use":return e.jsx(w,{className:"w-6 h-6 text-green-500"});default:return e.jsx(_,{className:"w-6 h-6 text-gray-500"})}},E=()=>{const i=r.find(s=>s.id==="annual"),n=r.find(s=>s.id==="monthly");if(i&&n){const s=i.price/12,l=(n.price-s)*12;return Math.round(l)}return 0},x=p&&!u,h=x?r:r.filter(i=>i.type!=="pay_per_use");return console.log("🔍 PricingSelection - showPayPerUse:",p),console.log("🔍 PricingSelection - hasActiveSubscription:",u),console.log("🔍 PricingSelection - shouldShowPayPerUse:",x),console.log("🔍 PricingSelection - filteredPlans:",h.map(i=>i.id)),e.jsxs("div",{className:"max-w-6xl mx-auto p-6 bg-black text-white",children:[e.jsx("div",{className:"text-center mb-12",children:e.jsxs(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[e.jsx("h1",{className:"text-4xl font-bold mb-4 text-white",children:"Choose Your Plan"}),e.jsx("p",{className:"text-xl text-white/70 mb-6",children:"Start optimizing your products with our powerful SEO tools"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:h.map((i,n)=>e.jsx(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:n*.1},children:e.jsxs(B,{className:`relative h-full transition-all duration-300 hover:shadow-xl cursor-pointer bg-white/10 border-white/20 ${i.recommended?"border-2 border-white shadow-lg scale-105":P===i.id?"border-2 border-blue-400":"border-white/20 hover:border-white/40"}`,onClick:()=>m(i.id),children:[i.recommended&&e.jsx("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:e.jsxs(M,{className:"bg-white text-black px-4 py-1 text-sm font-semibold",children:[e.jsx(b,{className:"w-4 h-4 mr-1"}),"Best Value"]})}),e.jsxs(U,{className:"text-center pb-4",children:[e.jsx("div",{className:"flex justify-center mb-4",children:z(i.type)}),e.jsx(q,{className:"text-2xl font-bold text-white",children:i.name}),e.jsx(D,{className:"text-base text-white/70",children:i.description}),e.jsx("div",{className:"mt-4",children:i.type==="pay_per_use"?e.jsxs("div",{children:[e.jsxs("span",{className:"text-4xl font-bold text-white",children:["$",i.price]}),e.jsx("span",{className:"text-white/70 ml-2",children:"per product"})]}):e.jsxs("div",{children:[e.jsxs("span",{className:"text-4xl font-bold text-white",children:["$",i.price]}),e.jsxs("span",{className:"text-white/70 ml-2",children:["/",i.type==="annual"?"year":"month"]}),i.type==="annual"&&e.jsxs("div",{className:"text-sm text-green-400 font-medium mt-1",children:["Save $",E(),"/year vs monthly"]}),i.type==="monthly"&&e.jsxs("div",{className:"text-sm text-white/70 mt-1",children:["Equivalent to $",i.price.toFixed(2),"/month"]})]})})]}),e.jsxs(I,{className:"pt-0",children:[e.jsx("ul",{className:"space-y-3 mb-6",children:i.features.map((s,l)=>e.jsxs("li",{className:"flex items-start gap-3",children:[e.jsx(T,{className:"w-5 h-5 text-green-400 mt-0.5 flex-shrink-0"}),e.jsx("span",{className:"text-sm text-white/80",children:s})]},l))}),i.type!=="pay_per_use"?e.jsx(y,{onClick:s=>{s.stopPropagation(),C(i.id)},className:`w-full ${i.recommended?"bg-white text-black hover:bg-white/90 disabled:bg-white/60":"bg-white/20 text-white hover:bg-white/30 disabled:bg-white/10"} disabled:cursor-not-allowed transition-all duration-200`,disabled:a.state==="submitting"||a.state==="loading",children:a.state==="submitting"||a.state==="loading"?e.jsxs(e.Fragment,{children:[e.jsx(v,{className:"w-4 h-4 mr-2 animate-spin"}),"Processing..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"w-4 h-4 mr-2"}),"Subscribe Now"]})}):e.jsx(y,{onClick:s=>{s.stopPropagation(),m(i.id),console.log("🎯 Pay-per-use selected, navigating to SEO dashboard"),k.toast.show("Pay-per-use selected! Select products to optimize and pay only for what you use.",{isError:!1}),N("/app/seo-dashboard")},className:"w-full border-2 border-white/30 bg-transparent text-white hover:bg-white hover:text-black",children:"Select Pay-Per-Use"})]})]})},i.id))}),e.jsxs(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.4},className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsx("h3",{className:"text-2xl font-bold text-center mb-8 text-white",children:"Plan Comparison"}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b",children:[e.jsx("th",{className:"text-left py-4 px-4",children:"Feature"}),e.jsx("th",{className:"text-center py-4 px-4",children:"Annual"}),e.jsx("th",{className:"text-center py-4 px-4",children:"Monthly"}),e.jsx("th",{className:"text-center py-4 px-4",children:"Pay-Per-Use"})]})}),e.jsxs("tbody",{className:"text-sm",children:[e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"py-4 px-4 font-medium",children:"Product Optimizations"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Unlimited"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Unlimited"}),e.jsx("td",{className:"text-center py-4 px-4",children:"$0.10 each"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"py-4 px-4 font-medium",children:"Support Level"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Priority"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Standard"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Standard"})]}),e.jsxs("tr",{className:"border-b",children:[e.jsx("td",{className:"py-4 px-4 font-medium",children:"Analytics"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Advanced"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Basic"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Basic"})]}),e.jsxs("tr",{children:[e.jsx("td",{className:"py-4 px-4 font-medium",children:"Monthly Commitment"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Annual only"}),e.jsx("td",{className:"text-center py-4 px-4",children:"Yes"}),e.jsx("td",{className:"text-center py-4 px-4",children:"None"})]})]})]})})]}),e.jsxs(d.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,delay:.6},className:"mt-12 text-center",children:[e.jsx("h3",{className:"text-xl font-semibold mb-4",children:"Frequently Asked Questions"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Can I change plans later?"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Yes, you can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"How does billing work?"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"You'll be charged immediately upon subscription. Monthly plans bill every 30 days, annual plans bill yearly."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Is there a setup fee?"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"No setup fees. You only pay the subscription price or per-use charges."})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium mb-2",children:"Can I cancel anytime?"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Yes, you can cancel your subscription at any time. No long-term contracts required."})]})]})]})]})}export{X as P};
