import{j as e}from"./jsx-runtime-0DLF9kdB.js";import{r as i}from"./index-BJYSoprK.js";import{B as x}from"./button-RlsfHhb_.js";import{I as o}from"./input-Bg89CcEX.js";import{a as u}from"./components-C2v4lmCU.js";import{u as b}from"./useAppBridge-Bj34gXAL.js";import{T as w}from"./TitleBar-DFMSJ8Yc.js";import"./index-Ci0627_k.js";import"./index-DI88vBYx.js";function E(){var m;const t=u(),l=b(),[n,r]=i.useState("Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"),[d,a]=i.useState("Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}"),c=t.state==="submitting";i.useEffect(()=>{t.data&&"success"in t.data&&t.data.success?l.toast.show(t.data.message||"Settings saved!"):t.data&&"error"in t.data&&t.data.error&&l.toast.show(t.data.error,{isError:!0})},[t.data,l]),i.useEffect(()=>{if(typeof window<"u"){const s=sessionStorage.getItem("title_prompt_template"),h=sessionStorage.getItem("description_prompt_template");s&&r(s),h&&a(h)}},[]),i.useCallback(s=>{r(s)},[]),i.useCallback(s=>{a(s)},[]);const p=i.useCallback(()=>{typeof window<"u"&&(sessionStorage.setItem("title_prompt_template",n),sessionStorage.setItem("description_prompt_template",d));const s=new FormData;s.append("action","save"),t.submit(s,{method:"POST"})},[n,d,t]);return i.useCallback(()=>{r("Generate an SEO-optimized product title for the following product. The title should be compelling, include relevant keywords, and be under 60 characters. Product details: {title}, {description}, {type}, {vendor}"),a("Generate an SEO-optimized meta description for the following product. The description should be compelling, include relevant keywords, and be under 160 characters. Product details: {title}, {description}, {type}, {vendor}")},[]),e.jsxs(e.Fragment,{children:[e.jsx(w,{title:"AI BULK SEO Settings"}),e.jsxs("div",{className:"min-h-screen bg-black text-white",children:[e.jsx("div",{className:"bg-black py-20 px-6",children:e.jsx("div",{className:"max-w-6xl mx-auto text-center",children:e.jsxs("div",{className:"flex flex-col items-center mb-12",children:[e.jsx("img",{src:"/logo.png",alt:"AI BULK SEO Logo",className:"w-16 h-16 mb-6 rounded-2xl shadow-2xl",style:{filter:"brightness(1.1) contrast(1.1)"}}),e.jsx("h1",{style:{fontSize:"clamp(3rem, 8vw, 6rem)",fontWeight:900,lineHeight:.9,letterSpacing:"-0.05em",marginBottom:"1rem",color:"white"},children:"SETTINGS"}),e.jsx("p",{style:{fontSize:"clamp(1.25rem, 3vw, 1.75rem)",fontWeight:300,color:"#cbd5e1",maxWidth:"40rem",margin:"0 auto"},children:"Configure your AI BULK SEO optimization preferences"})]})})}),e.jsx("div",{className:"px-6 pb-20",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-8",children:[e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"API Configuration"}),e.jsx("p",{className:"text-white/70",children:"Configure your API keys for enhanced functionality"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"Google PageSpeed API Key"}),e.jsx(o,{type:"password",placeholder:"Enter your Google PageSpeed API key",className:"w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"}),e.jsx("p",{className:"text-xs text-white/60",children:"Required for detailed Core Web Vitals analysis"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"OpenAI API Key"}),e.jsx(o,{type:"password",placeholder:"Enter your OpenAI API key",className:"w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"}),e.jsx("p",{className:"text-xs text-white/60",children:"Required for advanced content optimization"})]})]})]}),e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"SEO Prompt Templates"}),e.jsx("p",{className:"text-white/70",children:"Customize AI prompts for product optimization"})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"Title Generation Prompt"}),e.jsx("textarea",{value:n,onChange:s=>r(s.target.value),className:"w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none",placeholder:"Enter your title generation prompt...",style:{lineHeight:1.6}})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"Description Generation Prompt"}),e.jsx("textarea",{value:d,onChange:s=>a(s.target.value),className:"w-full h-32 px-4 py-3 text-sm bg-white/5 border border-white/20 rounded-2xl text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10 focus:outline-none resize-none",placeholder:"Enter your description generation prompt...",style:{lineHeight:1.6}})]})]})]}),e.jsxs("div",{className:"bg-white/10 border border-white/20 rounded-3xl p-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Automation Settings"}),e.jsx("p",{className:"text-white/70",children:"Configure bulk processing and automation preferences"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"Batch Size"}),e.jsx(o,{type:"number",defaultValue:"10",min:"1",max:"50",className:"w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"}),e.jsx("p",{className:"text-xs text-white/60",children:"Number of products to process simultaneously"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("label",{className:"text-sm font-semibold text-white",children:"Processing Delay"}),e.jsx(o,{type:"number",defaultValue:"1000",min:"500",max:"5000",className:"w-full bg-white/5 border-white/20 text-white placeholder:text-white/50 focus:border-white/40 focus:bg-white/10"}),e.jsx("p",{className:"text-xs text-white/60",children:"Delay between requests (milliseconds)"})]})]})]}),e.jsx("div",{className:"flex justify-center pt-8",children:e.jsx(x,{onClick:p,disabled:c,size:"lg",className:"bg-white text-black hover:bg-gray-100 font-bold py-4 px-12 text-lg rounded-2xl transition-colors duration-200",children:c?"Saving Settings...":"Save Settings"})}),((m=t.data)==null?void 0:m.success)&&e.jsx("div",{className:"mt-8",children:e.jsx("div",{className:"bg-white/10 border border-white/20 rounded-2xl p-6 text-center",children:e.jsx("div",{className:"text-white font-semibold text-lg",children:"✅ Settings saved successfully!"})})})]})})]})]})}export{E as default};
