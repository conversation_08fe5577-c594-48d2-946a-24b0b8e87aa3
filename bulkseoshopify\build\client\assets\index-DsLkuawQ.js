import{j as P}from"./jsx-runtime-0DLF9kdB.js";import{r as t}from"./index-BJYSoprK.js";import{M as W,i as b,u as z,P as A,a as D,b as H,L as K}from"./proxy-CdGEizrq.js";class U extends t.Component{getSnapshotBeforeUpdate(c){const e=this.props.childRef.current;if(e&&c.isPresent&&!this.props.isPresent){const f=e.offsetParent,m=b(f)&&f.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=m-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function B({children:o,isPresent:c,anchorX:e,root:f}){const m=t.useId(),n=t.useRef(null),p=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:g}=t.useContext(W);return t.useInsertionEffect(()=>{const{width:y,height:s,top:C,left:r,right:u}=p.current;if(c||!n.current||!y||!s)return;const a=e==="left"?`left: ${r}`:`right: ${u}`;n.current.dataset.motionPopId=m;const l=document.createElement("style");g&&(l.nonce=g);const d=f??document.head;return d.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${m}"] {
            position: absolute !important;
            width: ${y}px !important;
            height: ${s}px !important;
            ${a}px !important;
            top: ${C}px !important;
          }
        `),()=>{d.contains(l)&&d.removeChild(l)}},[c]),P.jsx(U,{isPresent:c,childRef:n,sizeRef:p,children:t.cloneElement(o,{ref:n})})}const F=({children:o,initial:c,isPresent:e,onExitComplete:f,custom:m,presenceAffectsLayout:n,mode:p,anchorX:g,root:y})=>{const s=z(G),C=t.useId();let r=!0,u=t.useMemo(()=>(r=!1,{id:C,initial:c,isPresent:e,custom:m,onExitComplete:a=>{s.set(a,!0);for(const l of s.values())if(!l)return;f&&f()},register:a=>(s.set(a,!1),()=>s.delete(a))}),[e,s,f]);return n&&r&&(u={...u}),t.useMemo(()=>{s.forEach((a,l)=>s.set(l,!1))},[e]),t.useEffect(()=>{!e&&!s.size&&f&&f()},[e]),p==="popLayout"&&(o=P.jsx(B,{isPresent:e,anchorX:g,root:y,children:o})),P.jsx(A.Provider,{value:u,children:o})};function G(){return new Map}const R=o=>o.key||"";function L(o){const c=[];return t.Children.forEach(o,e=>{t.isValidElement(e)&&c.push(e)}),c}const _=({children:o,custom:c,initial:e=!0,onExitComplete:f,presenceAffectsLayout:m=!0,mode:n="sync",propagate:p=!1,anchorX:g="left",root:y})=>{const[s,C]=D(p),r=t.useMemo(()=>L(o),[o]),u=p&&!s?[]:r.map(R),a=t.useRef(!0),l=t.useRef(r),d=z(()=>new Map),[I,k]=t.useState(r),[x,$]=t.useState(r);H(()=>{a.current=!1,l.current=r;for(let h=0;h<x.length;h++){const i=R(x[h]);u.includes(i)?d.delete(i):d.get(i)!==!0&&d.set(i,!1)}},[x,u.length,u.join("-")]);const M=[];if(r!==I){let h=[...r];for(let i=0;i<x.length;i++){const E=x[i],j=R(E);u.includes(j)||(h.splice(i,0,E),M.push(E))}return n==="wait"&&M.length&&(h=M),$(L(h)),k(r),null}const{forceRender:w}=t.useContext(K);return P.jsx(P.Fragment,{children:x.map(h=>{const i=R(h),E=p&&!s?!1:r===x||u.includes(i),j=()=>{if(d.has(i))d.set(i,!0);else return;let v=!0;d.forEach(S=>{S||(v=!1)}),v&&(w==null||w(),$(l.current),p&&(C==null||C()),f&&f())};return P.jsx(F,{isPresent:E,initial:!a.current||e?void 0:!1,custom:c,presenceAffectsLayout:m,mode:n,root:y,onExitComplete:E?void 0:j,anchorX:g,children:h},i)})})};export{_ as A};
