import{j as t}from"./jsx-runtime-0DLF9kdB.js";import{b as d,c as y,_ as f,M as x,L as S,S as j}from"./components-C2v4lmCU.js";import{a as w,b as k,O as g}from"./index-DI88vBYx.js";import{r as n}from"./index-BJYSoprK.js";import"./index-Ci0627_k.js";/**
 * @remix-run/react v2.17.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */let l="positions";function M({getKey:r,...a}){let{isSpaMode:c}=d(),o=w(),p=k();y({getKey:r,storageKey:l});let h=n.useMemo(()=>{if(!r)return null;let e=r(o,p);return e!==o.key?e:null},[]);if(c)return null;let m=((e,u)=>{if(!window.history.state||!window.history.state.key){let s=Math.random().toString(32).slice(2);window.history.replaceState({key:s},"")}try{let i=JSON.parse(sessionStorage.getItem(e)||"{}")[u||window.history.state.key];typeof i=="number"&&window.scrollTo(0,i)}catch(s){console.error(s),sessionStorage.removeItem(e)}}).toString();return n.createElement("script",f({},a,{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:`(${m})(${JSON.stringify(l)}, ${JSON.stringify(h)})`}}))}const O="/assets/tailwind-DD_5iXyO.css",E=()=>[{rel:"stylesheet",href:O}];function I(){return t.jsxs("html",{children:[t.jsxs("head",{children:[t.jsx("meta",{charSet:"utf-8"}),t.jsx("meta",{name:"viewport",content:"width=device-width,initial-scale=1"}),t.jsx("link",{rel:"preconnect",href:"https://cdn.shopify.com/"}),t.jsx("link",{rel:"stylesheet",href:"https://cdn.shopify.com/static/fonts/inter/v4/styles.css"}),t.jsx(x,{}),t.jsx(S,{})]}),t.jsxs("body",{children:[t.jsx(g,{}),t.jsx(M,{}),t.jsx(j,{})]})]})}export{I as default,E as links};
