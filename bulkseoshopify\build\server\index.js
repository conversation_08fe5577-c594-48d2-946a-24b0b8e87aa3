import { s, c, e, h, f, i, m, p, r } from "./assets/server-build-CCVLOhir.js";
import "react/jsx-runtime";
import "stream";
import "react-dom/server";
import "@remix-run/react";
import "@remix-run/node";
import "isbot";
import "@shopify/shopify-app-remix/adapters/node";
import "@shopify/shopify-app-remix/server";
import "@shopify/shopify-api/rest/admin/2025-01";
import "@shopify/shopify-app-session-storage-prisma";
import "@prisma/client";
import "react";
import "@shopify/polaris";
import "@shopify/shopify-app-remix/react";
import "@shopify/app-bridge-react";
import "framer-motion";
import "crypto";
import "@radix-ui/react-slot";
import "class-variance-authority";
import "clsx";
import "tailwind-merge";
import "@radix-ui/react-select";
import "lucide-react";
import "@radix-ui/react-progress";
import "@radix-ui/react-separator";
import "@radix-ui/react-checkbox";
export {
  s as assets,
  c as assetsBuildDirectory,
  e as basename,
  h as entry,
  f as future,
  i as isSpaMode,
  m as mode,
  p as publicPath,
  r as routes
};
