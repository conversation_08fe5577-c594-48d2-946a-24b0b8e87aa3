import{R as t,r as c}from"./index-BJYSoprK.js";import{c as _,B as Ne,a as ue,s as xt,g as mr,u as pe,v as de,T as W,I as ge,f as fr,U as hr,h as ln,b as gr,d as vr,S as br,w as Er,e as Pr,i as Sr,j as Le,k as Gt}from"./Button-3T1ZHLBP.js";import{g as sn,f as Cr,k as De,c as cn,b as un,d as xr,l as dn,o as Ar,a as Tr,p as Ir}from"./use-is-after-initial-mount-MUb1fdtO.js";import{T as Nr,b as kr,g as wr,a as _r,P as pn,u as Br,R as At,d as Mr,c as yr,S as Fr,e as Oe,E as at,f as Lr,M as Or}from"./context-Dt_50QHC.js";import{a as Rr}from"./index-Ci0627_k.js";let oe;(function(e){e[e.Backspace=8]="Backspace",e[e.Tab=9]="Tab",e[e.Enter=13]="Enter",e[e.Shift=16]="Shift",e[e.Ctrl=17]="Ctrl",e[e.Alt=18]="Alt",e[e.Pause=19]="Pause",e[e.CapsLock=20]="CapsLock",e[e.Escape=27]="Escape",e[e.Space=32]="Space",e[e.PageUp=33]="PageUp",e[e.PageDown=34]="PageDown",e[e.End=35]="End",e[e.Home=36]="Home",e[e.LeftArrow=37]="LeftArrow",e[e.UpArrow=38]="UpArrow",e[e.RightArrow=39]="RightArrow",e[e.DownArrow=40]="DownArrow",e[e.Insert=45]="Insert",e[e.Delete=46]="Delete",e[e.Key0=48]="Key0",e[e.Key1=49]="Key1",e[e.Key2=50]="Key2",e[e.Key3=51]="Key3",e[e.Key4=52]="Key4",e[e.Key5=53]="Key5",e[e.Key6=54]="Key6",e[e.Key7=55]="Key7",e[e.Key8=56]="Key8",e[e.Key9=57]="Key9",e[e.KeyA=65]="KeyA",e[e.KeyB=66]="KeyB",e[e.KeyC=67]="KeyC",e[e.KeyD=68]="KeyD",e[e.KeyE=69]="KeyE",e[e.KeyF=70]="KeyF",e[e.KeyG=71]="KeyG",e[e.KeyH=72]="KeyH",e[e.KeyI=73]="KeyI",e[e.KeyJ=74]="KeyJ",e[e.KeyK=75]="KeyK",e[e.KeyL=76]="KeyL",e[e.KeyM=77]="KeyM",e[e.KeyN=78]="KeyN",e[e.KeyO=79]="KeyO",e[e.KeyP=80]="KeyP",e[e.KeyQ=81]="KeyQ",e[e.KeyR=82]="KeyR",e[e.KeyS=83]="KeyS",e[e.KeyT=84]="KeyT",e[e.KeyU=85]="KeyU",e[e.KeyV=86]="KeyV",e[e.KeyW=87]="KeyW",e[e.KeyX=88]="KeyX",e[e.KeyY=89]="KeyY",e[e.KeyZ=90]="KeyZ",e[e.LeftMeta=91]="LeftMeta",e[e.RightMeta=92]="RightMeta",e[e.Select=93]="Select",e[e.Numpad0=96]="Numpad0",e[e.Numpad1=97]="Numpad1",e[e.Numpad2=98]="Numpad2",e[e.Numpad3=99]="Numpad3",e[e.Numpad4=100]="Numpad4",e[e.Numpad5=101]="Numpad5",e[e.Numpad6=102]="Numpad6",e[e.Numpad7=103]="Numpad7",e[e.Numpad8=104]="Numpad8",e[e.Numpad9=105]="Numpad9",e[e.Multiply=106]="Multiply",e[e.Add=107]="Add",e[e.Subtract=109]="Subtract",e[e.Decimal=110]="Decimal",e[e.Divide=111]="Divide",e[e.F1=112]="F1",e[e.F2=113]="F2",e[e.F3=114]="F3",e[e.F4=115]="F4",e[e.F5=116]="F5",e[e.F6=117]="F6",e[e.F7=118]="F7",e[e.F8=119]="F8",e[e.F9=120]="F9",e[e.F10=121]="F10",e[e.F11=122]="F11",e[e.F12=123]="F12",e[e.NumLock=144]="NumLock",e[e.ScrollLock=145]="ScrollLock",e[e.Semicolon=186]="Semicolon",e[e.Equals=187]="Equals",e[e.Comma=188]="Comma",e[e.Dash=189]="Dash",e[e.Period=190]="Period",e[e.ForwardSlash=191]="ForwardSlash",e[e.GraveAccent=192]="GraveAccent",e[e.OpenBracket=219]="OpenBracket",e[e.BackSlash=220]="BackSlash",e[e.CloseBracket=221]="CloseBracket",e[e.SingleQuote=222]="SingleQuote"})(oe||(oe={}));var Hr={themeContainer:"Polaris-ThemeProvider--themeContainer"};const Wr=["light","dark-experimental"],$r=e=>Wr.includes(e);function Dr(e){const{as:n="div",children:r,className:o,theme:a=sn}=e;return t.createElement(Nr.Provider,{value:a},t.createElement(kr.Provider,{value:wr(a)},t.createElement(n,{"data-portal-id":e["data-portal-id"],className:_(Cr(a),Hr.themeContainer,o)},r)))}function mn(e,n,r,o){const a=c.useRef(n),i=c.useRef(o);De(()=>{a.current=n},[n]),De(()=>{i.current=o},[o]),c.useEffect(()=>{if(!(typeof e=="string"&&r!==null))return;let l;if(typeof r>"u")l=window;else if("current"in r){if(r.current===null)return;l=r.current}else l=r;const s=i.current,u=d=>a.current(d);return l.addEventListener(e,u,s),()=>{l.removeEventListener(e,u,s)}},[e,r])}var fn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{d:"M10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"}),t.createElement("path",{d:"M11 13a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"}),t.createElement("path",{fillRule:"evenodd",d:"M17 10a7 7 0 1 1-14 0 7 7 0 0 1 14 0Zm-1.5 0a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0Z"}))};fn.displayName="AlertCircleIcon";var hn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{fillRule:"evenodd",d:"M16.5 10a.75.75 0 0 1-.75.75h-9.69l2.72 2.72a.75.75 0 0 1-1.06 1.06l-4-4a.75.75 0 0 1 0-1.06l4-4a.75.75 0 1 1 1.06 1.06l-2.72 2.72h9.69a.75.75 0 0 1 .75.75Z"}))};hn.displayName="ArrowLeftIcon";var gn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{fillRule:"evenodd",d:"M11.764 5.204a.75.75 0 0 1 .032 1.06l-3.516 3.736 3.516 3.736a.75.75 0 1 1-1.092 1.028l-4-4.25a.75.75 0 0 1 0-1.028l4-4.25a.75.75 0 0 1 1.06-.032Z"}))};gn.displayName="ChevronLeftIcon";var vn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{fillRule:"evenodd",d:"M7.72 14.53a.75.75 0 0 1 0-1.06l3.47-3.47-3.47-3.47a.75.75 0 0 1 1.06-1.06l4 4a.75.75 0 0 1 0 1.06l-4 4a.75.75 0 0 1-1.06 0Z"}))};vn.displayName="ChevronRightIcon";var bn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{d:"M6 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),t.createElement("path",{d:"M11.5 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}),t.createElement("path",{d:"M17 10a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))};bn.displayName="MenuHorizontalIcon";var En=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{fillRule:"evenodd",d:"M12.323 13.383a5.5 5.5 0 1 1 1.06-1.06l2.897 2.897a.75.75 0 1 1-1.06 1.06l-2.897-2.897Zm.677-4.383a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"}))};En.displayName="SearchIcon";var Pn=function(n){return t.createElement("svg",Object.assign({viewBox:"0 0 20 20"},n),t.createElement("path",{d:"M13.03 6.97a.75.75 0 0 1 0 1.06l-1.97 1.97 1.97 1.97a.75.75 0 1 1-1.06 1.06l-1.97-1.97-1.97 1.97a.75.75 0 0 1-1.06-1.06l1.97-1.97-1.97-1.97a.75.75 0 0 1 1.06-1.06l1.97 1.97 1.97-1.97a.75.75 0 0 1 1.06 0Z"}),t.createElement("path",{fillRule:"evenodd",d:"M10 17a7 7 0 1 0 0-14 7 7 0 0 0 0 14Zm0-1.5a5.5 5.5 0 1 0 0-11 5.5 5.5 0 0 0 0 11Z"}))};Pn.displayName="XCircleIcon";function Sn({content:e,onAction:n,plain:r,destructive:o,...a},i,l){const s=r?"plain":void 0,u=o?"primary":void 0,d=!(i!=null&&i.tone)&&o?"critical":i==null?void 0:i.tone;return t.createElement(Ne,Object.assign({key:l,onClick:n,tone:d,variant:s||u},a,i),e)}var Ye={listReset:"Polaris-Box--listReset",Box:"Polaris-Box",visuallyHidden:"Polaris-Box--visuallyHidden",printHidden:"Polaris-Box--printHidden"};const z=c.forwardRef(({as:e="div",background:n,borderColor:r,borderStyle:o,borderWidth:a,borderBlockStartWidth:i,borderBlockEndWidth:l,borderInlineStartWidth:s,borderInlineEndWidth:u,borderRadius:d,borderEndStartRadius:p,borderEndEndRadius:g,borderStartStartRadius:f,borderStartEndRadius:m,children:v,color:P,id:S,minHeight:A,minWidth:C,maxWidth:B,overflowX:T,overflowY:M,outlineColor:x,outlineStyle:h,outlineWidth:b,padding:I,paddingBlock:F,paddingBlockStart:G,paddingBlockEnd:L,paddingInline:R,paddingInlineStart:k,paddingInlineEnd:y,role:$,shadow:O,tabIndex:Q,width:te,printHidden:ae,visuallyHidden:U,position:X,insetBlockStart:le,insetBlockEnd:Y,insetInlineStart:me,insetInlineEnd:Se,zIndex:J,opacity:Ce,...ie},ne)=>{const re=o||(r||a||i||l||s||u?"solid":void 0),se=h||(x||b?"solid":void 0),Ze={"--pc-box-color":P?`var(--p-color-${P})`:void 0,"--pc-box-background":n?`var(--p-color-${n})`:void 0,"--pc-box-border-color":r?r==="transparent"?"transparent":`var(--p-color-${r})`:void 0,"--pc-box-border-style":re,"--pc-box-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-box-border-end-start-radius":p?`var(--p-border-radius-${p})`:void 0,"--pc-box-border-end-end-radius":g?`var(--p-border-radius-${g})`:void 0,"--pc-box-border-start-start-radius":f?`var(--p-border-radius-${f})`:void 0,"--pc-box-border-start-end-radius":m?`var(--p-border-radius-${m})`:void 0,"--pc-box-border-width":a?`var(--p-border-width-${a})`:void 0,"--pc-box-border-block-start-width":i?`var(--p-border-width-${i})`:void 0,"--pc-box-border-block-end-width":l?`var(--p-border-width-${l})`:void 0,"--pc-box-border-inline-start-width":s?`var(--p-border-width-${s})`:void 0,"--pc-box-border-inline-end-width":u?`var(--p-border-width-${u})`:void 0,"--pc-box-min-height":A,"--pc-box-min-width":C,"--pc-box-max-width":B,"--pc-box-outline-color":x?`var(--p-color-${x})`:void 0,"--pc-box-outline-style":se,"--pc-box-outline-width":b?`var(--p-border-width-${b})`:void 0,"--pc-box-overflow-x":T,"--pc-box-overflow-y":M,...ue("box","padding-block-start","space",G||F||I),...ue("box","padding-block-end","space",L||F||I),...ue("box","padding-inline-start","space",k||R||I),...ue("box","padding-inline-end","space",y||R||I),"--pc-box-shadow":O?`var(--p-shadow-${O})`:void 0,"--pc-box-width":te,position:X,"--pc-box-inset-block-start":le?`var(--p-space-${le})`:void 0,"--pc-box-inset-block-end":Y?`var(--p-space-${Y})`:void 0,"--pc-box-inset-inline-start":me?`var(--p-space-${me})`:void 0,"--pc-box-inset-inline-end":Se?`var(--p-space-${Se})`:void 0,zIndex:J,opacity:Ce},Ve=_(Ye.Box,U&&Ye.visuallyHidden,ae&&Ye.printHidden,e==="ul"&&Ye.listReset);return t.createElement(e,{className:Ve,id:S,ref:ne,style:xt(Ze),role:$,tabIndex:Q,...ie},v)});z.displayName="Box";var zr={InlineStack:"Polaris-InlineStack"};const ze=function({as:n="div",align:r,direction:o="row",blockAlign:a,gap:i,wrap:l=!0,children:s}){const u={"--pc-inline-stack-align":r,"--pc-inline-stack-block-align":a,"--pc-inline-stack-wrap":l?"wrap":"nowrap",...ue("inline-stack","gap","space",i),...mr("inline-stack","flex-direction",o)};return t.createElement(n,{className:zr.InlineStack,style:u},s)};var vt={BlockStack:"Polaris-BlockStack",listReset:"Polaris-BlockStack--listReset",fieldsetReset:"Polaris-BlockStack--fieldsetReset"};const Gr=({as:e="div",children:n,align:r,inlineAlign:o,gap:a,id:i,reverseOrder:l=!1,...s})=>{const u=_(vt.BlockStack,(e==="ul"||e==="ol")&&vt.listReset,e==="fieldset"&&vt.fieldsetReset),d={"--pc-block-stack-align":r?`${r}`:null,"--pc-block-stack-inline-align":o?`${o}`:null,"--pc-block-stack-order":l?"column-reverse":"column",...ue("block-stack","gap","space",a)};return t.createElement(e,{className:u,id:i,style:xt(d),...s},n)},Cn=c.createContext(!1);function Ur({children:e,filterActions:n}){return t.createElement(Cn.Provider,{value:n},e)}var K={Item:"Polaris-ActionList__Item",default:"Polaris-ActionList--default",active:"Polaris-ActionList--active",destructive:"Polaris-ActionList--destructive",disabled:"Polaris-ActionList--disabled",Prefix:"Polaris-ActionList__Prefix",Suffix:"Polaris-ActionList__Suffix",indented:"Polaris-ActionList--indented",menu:"Polaris-ActionList--menu",Text:"Polaris-ActionList__Text"};const jr=c.createContext(!1);var Ae={Badge:"Polaris-Badge",toneSuccess:"Polaris-Badge--toneSuccess","toneSuccess-strong":"Polaris-Badge__toneSuccess--strong",toneInfo:"Polaris-Badge--toneInfo","toneInfo-strong":"Polaris-Badge__toneInfo--strong",toneAttention:"Polaris-Badge--toneAttention","toneAttention-strong":"Polaris-Badge__toneAttention--strong",toneWarning:"Polaris-Badge--toneWarning","toneWarning-strong":"Polaris-Badge__toneWarning--strong",toneCritical:"Polaris-Badge--toneCritical","toneCritical-strong":"Polaris-Badge__toneCritical--strong",toneNew:"Polaris-Badge--toneNew",toneMagic:"Polaris-Badge--toneMagic","toneRead-only":"Polaris-Badge__toneRead--only",toneEnabled:"Polaris-Badge--toneEnabled",sizeLarge:"Polaris-Badge--sizeLarge",withinFilter:"Polaris-Badge--withinFilter",Icon:"Polaris-Badge__Icon",PipContainer:"Polaris-Badge__PipContainer"};let q;(function(e){e.Info="info",e.Success="success",e.Warning="warning",e.Critical="critical",e.Attention="attention",e.New="new",e.Magic="magic",e.InfoStrong="info-strong",e.SuccessStrong="success-strong",e.WarningStrong="warning-strong",e.CriticalStrong="critical-strong",e.AttentionStrong="attention-strong",e.ReadOnly="read-only",e.Enabled="enabled"})(q||(q={}));let He;(function(e){e.Incomplete="incomplete",e.PartiallyComplete="partiallyComplete",e.Complete="complete"})(He||(He={}));function xn(e,n,r){let o="",a="";if(!n&&!r)return"";switch(n){case He.Incomplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.incomplete");break;case He.PartiallyComplete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.partiallyComplete");break;case He.Complete:o=e.translate("Polaris.Badge.PROGRESS_LABELS.complete");break}switch(r){case q.Info:case q.InfoStrong:a=e.translate("Polaris.Badge.TONE_LABELS.info");break;case q.Success:case q.SuccessStrong:a=e.translate("Polaris.Badge.TONE_LABELS.success");break;case q.Warning:case q.WarningStrong:a=e.translate("Polaris.Badge.TONE_LABELS.warning");break;case q.Critical:case q.CriticalStrong:a=e.translate("Polaris.Badge.TONE_LABELS.critical");break;case q.Attention:case q.AttentionStrong:a=e.translate("Polaris.Badge.TONE_LABELS.attention");break;case q.New:a=e.translate("Polaris.Badge.TONE_LABELS.new");break;case q.ReadOnly:a=e.translate("Polaris.Badge.TONE_LABELS.readOnly");break;case q.Enabled:a=e.translate("Polaris.Badge.TONE_LABELS.enabled");break}return!r&&n?o:r&&!n?a:e.translate("Polaris.Badge.progressAndTone",{progressLabel:o,toneLabel:a})}var bt={Pip:"Polaris-Badge-Pip",toneInfo:"Polaris-Badge-Pip--toneInfo",toneSuccess:"Polaris-Badge-Pip--toneSuccess",toneNew:"Polaris-Badge-Pip--toneNew",toneAttention:"Polaris-Badge-Pip--toneAttention",toneWarning:"Polaris-Badge-Pip--toneWarning",toneCritical:"Polaris-Badge-Pip--toneCritical",progressIncomplete:"Polaris-Badge-Pip--progressIncomplete",progressPartiallyComplete:"Polaris-Badge-Pip--progressPartiallyComplete",progressComplete:"Polaris-Badge-Pip--progressComplete"};function Zr({tone:e,progress:n="complete",accessibilityLabelOverride:r}){const o=pe(),a=_(bt.Pip,e&&bt[de("tone",e)],n&&bt[de("progress",n)]),i=r||xn(o,n,e);return t.createElement("span",{className:a},t.createElement(W,{as:"span",visuallyHidden:!0},i))}const Ut="medium",Vr={complete:()=>t.createElement("svg",{viewBox:"0 0 20 20"},t.createElement("path",{d:"M6 10c0-.93 0-1.395.102-1.776a3 3 0 0 1 2.121-2.122C8.605 6 9.07 6 10 6c.93 0 1.395 0 1.776.102a3 3 0 0 1 2.122 2.122C14 8.605 14 9.07 14 10s0 1.395-.102 1.777a3 3 0 0 1-2.122 2.12C11.395 14 10.93 14 10 14s-1.395 0-1.777-.102a3 3 0 0 1-2.12-2.121C6 11.395 6 10.93 6 10Z"})),partiallyComplete:()=>t.createElement("svg",{viewBox:"0 0 20 20"},t.createElement("path",{fillRule:"evenodd",d:"m8.888 6.014-.017-.018-.02.02c-.253.013-.45.038-.628.086a3 3 0 0 0-2.12 2.122C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.121 2.12C8.605 14 9.07 14 10 14c.93 0 1.395 0 1.776-.102a3 3 0 0 0 2.122-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.122-2.122C11.395 6 10.93 6 10 6c-.475 0-.829 0-1.112.014ZM8.446 7.34a1.75 1.75 0 0 0-1.041.94l4.314 4.315c.443-.2.786-.576.941-1.042L8.446 7.34Zm4.304 2.536L10.124 7.25c.908.001 1.154.013 1.329.06a1.75 1.75 0 0 1 1.237 1.237c.047.175.059.42.06 1.329ZM8.547 12.69c.182.05.442.06 1.453.06h.106L7.25 9.894V10c0 1.01.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237Z"})),incomplete:()=>t.createElement("svg",{viewBox:"0 0 20 20"},t.createElement("path",{fillRule:"evenodd",d:"M8.547 12.69c.183.05.443.06 1.453.06s1.27-.01 1.453-.06a1.75 1.75 0 0 0 1.237-1.237c.05-.182.06-.443.06-1.453s-.01-1.27-.06-1.453a1.75 1.75 0 0 0-1.237-1.237c-.182-.05-.443-.06-1.453-.06s-1.27.01-1.453.06A1.75 1.75 0 0 0 7.31 8.547c-.05.183-.06.443-.06 1.453s.01 1.27.06 1.453a1.75 1.75 0 0 0 1.237 1.237ZM6.102 8.224C6 8.605 6 9.07 6 10s0 1.395.102 1.777a3 3 0 0 0 2.122 2.12C8.605 14 9.07 14 10 14s1.395 0 1.777-.102a3 3 0 0 0 2.12-2.121C14 11.395 14 10.93 14 10c0-.93 0-1.395-.102-1.776a3 3 0 0 0-2.121-2.122C11.395 6 10.93 6 10 6c-.93 0-1.395 0-1.776.102a3 3 0 0 0-2.122 2.122Z"}))};function An({children:e,tone:n,progress:r,icon:o,size:a=Ut,toneAndProgressLabelOverride:i}){const l=pe(),s=c.useContext(jr),u=_(Ae.Badge,n&&Ae[de("tone",n)],a&&a!==Ut&&Ae[de("size",a)],s&&Ae.withinFilter),d=i||xn(l,r,n);let p=!!d&&t.createElement(W,{as:"span",visuallyHidden:!0},d);return r&&!o&&(p=t.createElement("span",{className:Ae.Icon},t.createElement(ge,{accessibilityLabel:d,source:Vr[r]}))),t.createElement("span",{className:u},p,o&&t.createElement("span",{className:Ae.Icon},t.createElement(ge,{source:o})),e&&t.createElement(W,{as:"span",variant:"bodySm",fontWeight:n==="new"?"medium":void 0},e))}An.Pip=Zr;function Ge(e){const[n,r]=c.useState(e);return{value:n,toggle:c.useCallback(()=>r(o=>!o),[]),setTrue:c.useCallback(()=>r(!0),[]),setFalse:c.useCallback(()=>r(!1),[])}}var jt={TooltipContainer:"Polaris-Tooltip__TooltipContainer",HasUnderline:"Polaris-Tooltip__HasUnderline"};function qr(){const e=c.useContext(_r);if(!e)throw new Error("No ephemeral presence manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function Qr(){const e=c.useContext(pn);if(!e)throw new Error("No portals manager was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function Tn({children:e,idPrefix:n="",onPortalCreated:r=Xr}){const o=Br(),{container:a}=Qr(),i=c.useId(),l=n!==""?`${n}-${i}`:i;return c.useEffect(()=>{r()},[r]),a?Rr.createPortal(t.createElement(Dr,{theme:$r(o)?o:sn,"data-portal-id":l},e),a):null}function Xr(){}var ve={TooltipOverlay:"Polaris-Tooltip-TooltipOverlay",Tail:"Polaris-Tooltip-TooltipOverlay__Tail",positionedAbove:"Polaris-Tooltip-TooltipOverlay--positionedAbove",measuring:"Polaris-Tooltip-TooltipOverlay--measuring",measured:"Polaris-Tooltip-TooltipOverlay--measured",instant:"Polaris-Tooltip-TooltipOverlay--instant",Content:"Polaris-Tooltip-TooltipOverlay__Content",default:"Polaris-Tooltip-TooltipOverlay--default",wide:"Polaris-Tooltip-TooltipOverlay--wide"};function Yr(e,n,r,o,a,i,l,s=0){const u=e.top,d=u+e.height,p=e.top-s,g=a.height-e.top-e.height,f=n.height,m=r.activator+r.container,v=r.container,P=e.top-Math.max(o.top,0),S=a.top+Math.min(a.height,o.top+o.height)-(e.top+e.height),A=P>=v,C=S>=v,B=Math.min(p,f),T=Math.min(g,f),M=Math.min(p+e.height,f),x=Math.min(g+e.height,f),h=l?0:a.top,b={height:B-m,top:u+h-B,positioning:"above"},I={height:T-m,top:d+h,positioning:"below"},F={height:x-m,top:u+h,positioning:"cover"},G={height:M-m,top:u+h-B+e.height+m,positioning:"cover"};return i==="above"?(A||P>=S&&!C)&&(p>f||p>g)?b:I:i==="below"?(C||S>=P&&!A)&&(g>f||g>p)?I:b:i==="cover"?(C||S>=P&&!A)&&(g+e.height>f||g>p)?F:G:A&&C?p>g?b:I:P>v?b:I}function Jr(e,n,r,o,a){const i=r.width-n.width;if(a==="left")return Math.min(i,Math.max(0,e.left-o.horizontal));if(a==="right"){const l=r.width-(e.left+e.width);return Math.min(i,Math.max(0,l-o.horizontal))}return Math.min(i,Math.max(0,e.center.x-n.width/2))}function Kr(e,n){const{center:r}=e;return r.y<n.top||r.y>n.top+n.height}function eo(e,n=In()){const r=Math.max(e.top,0),o=Math.max(e.left,0),a=Math.min(e.top+e.height,n.height),i=Math.min(e.left+e.width,n.width);return new At({top:r,left:o,height:a-r,width:i-o})}function In(){return new At({top:window.scrollY,left:window.scrollX,height:window.innerHeight,width:document.body.clientWidth})}var Et={PositionedOverlay:"Polaris-PositionedOverlay",fixed:"Polaris-PositionedOverlay--fixed",preventInteraction:"Polaris-PositionedOverlay--preventInteraction"};const Zt=Symbol("unique_identifier");function to(e){const n=c.useRef(Zt);return n.current===Zt&&(n.current=e()),n}function no(e){const n=cn(),r=c.useRef(!1);if(n&&!r.current)return r.current=!0,e()}const Nn=c.createContext(void 0);var Ee={Scrollable:"Polaris-Scrollable",hasTopShadow:"Polaris-Scrollable--hasTopShadow",hasBottomShadow:"Polaris-Scrollable--hasBottomShadow",horizontal:"Polaris-Scrollable--horizontal",vertical:"Polaris-Scrollable--vertical",scrollbarWidthThin:"Polaris-Scrollable--scrollbarWidthThin",scrollbarWidthNone:"Polaris-Scrollable--scrollbarWidthNone",scrollbarWidthAuto:"Polaris-Scrollable--scrollbarWidthAuto",scrollbarGutterStable:"Polaris-Scrollable--scrollbarGutterStable","scrollbarGutterStableboth-edges":"Polaris-Scrollable__scrollbarGutterStableboth--edges"};function ro(){const e=c.useRef(null),n=c.useContext(Nn);c.useEffect(()=>{!n||!e.current||n(e.current.offsetTop)},[n]);const r=c.useId();return t.createElement("a",{id:r,ref:e})}const Vt=100,kn=2,wn=c.forwardRef(({children:e,className:n,horizontal:r=!0,vertical:o=!0,shadow:a,hint:i,focusable:l,scrollbarWidth:s="thin",scrollbarGutter:u,onScrolledToBottom:d,...p},g)=>{const[f,m]=c.useState(!1),[v,P]=c.useState(!1),S=to(()=>new Fr),A=c.useRef(null),C=c.useCallback((x,h={})=>{var F;const b=h.behavior||"smooth",I=_n()?"auto":b;(F=A.current)==null||F.scrollTo({top:x,behavior:I})},[]),B=c.useRef();c.useImperativeHandle(g||B,()=>({scrollTo:C}));const T=c.useCallback(()=>{const x=A.current;x&&requestAnimationFrame(()=>{const{scrollTop:h,clientHeight:b,scrollHeight:I}=x,F=I>b,G=h>0,L=h+b>=I-kn;m(G),P(!L),F&&L&&d&&d()})},[d]);no(()=>{T(),i&&requestAnimationFrame(()=>oo(A.current))}),c.useEffect(()=>{var b;const x=A.current;if(!x)return;const h=Mr(T,50,{trailing:!0});return(b=S.current)==null||b.setContainer(x),x.addEventListener("scroll",T),globalThis.addEventListener("resize",h),()=>{x.removeEventListener("scroll",T),globalThis.removeEventListener("resize",h)}},[S,T]);const M=_(n,Ee.Scrollable,o&&Ee.vertical,r&&Ee.horizontal,a&&f&&Ee.hasTopShadow,a&&v&&Ee.hasBottomShadow,s&&Ee[de("scrollbarWidth",s)],u&&Ee[de("scrollbarGutter",u.replace(" ",""))]);return t.createElement(Nn.Provider,{value:C},t.createElement(yr.Provider,{value:S.current},t.createElement("div",Object.assign({className:M},un.props,p,{ref:A,tabIndex:l?0:void 0}),e)))});wn.displayName="Scrollable";function _n(){try{return window.matchMedia("(prefers-reduced-motion: reduce)").matches}catch{return!1}}function oo(e){if(!e||_n())return;const n=e.scrollHeight-e.clientHeight,r=Math.min(Vt,n)-kn,o=()=>{requestAnimationFrame(()=>{e.scrollTop>=r&&(e.removeEventListener("scroll",o),e.scrollTo({top:0,behavior:"smooth"}))})};e.addEventListener("scroll",o),e.scrollTo({top:Vt,behavior:"smooth"})}const ao=e=>{const n=e.closest(un.selector);return n instanceof HTMLElement?n:document},ke=wn;ke.ScrollTo=ro;ke.forNode=ao;const qt={childList:!0,subtree:!0,characterData:!0,attributeFilter:["style"]};class Bn extends c.PureComponent{constructor(n){super(n),this.state={measuring:!0,activatorRect:Oe(this.props.activator),right:void 0,left:void 0,top:0,height:0,width:null,positioning:"below",zIndex:null,outsideScrollableContainer:!1,lockPosition:!1,chevronOffset:0},this.overlay=null,this.scrollableContainers=[],this.overlayDetails=()=>{const{measuring:r,left:o,right:a,positioning:i,height:l,activatorRect:s,chevronOffset:u}=this.state;return{measuring:r,left:o,right:a,desiredHeight:l,positioning:i,activatorRect:s,chevronOffset:u}},this.setOverlay=r=>{this.overlay=r},this.setScrollableContainers=()=>{const r=[];let o=ke.forNode(this.props.activator);if(o)for(r.push(o);o!=null&&o.parentElement;)o=ke.forNode(o.parentElement),r.push(o);this.scrollableContainers=r},this.registerScrollHandlers=()=>{this.scrollableContainers.forEach(r=>{r.addEventListener("scroll",this.handleMeasurement)})},this.unregisterScrollHandlers=()=>{this.scrollableContainers.forEach(r=>{r.removeEventListener("scroll",this.handleMeasurement)})},this.handleMeasurement=()=>{const{lockPosition:r,top:o}=this.state;this.observer.disconnect(),this.setState(({left:a,top:i,right:l})=>({left:a,right:l,top:i,height:0,positioning:"below",measuring:!0}),()=>{if(this.overlay==null||this.firstScrollableContainer==null)return;const{activator:a,preferredPosition:i="below",preferredAlignment:l="center",onScrollOut:s,fullWidth:u,fixed:d,preferInputActivator:p=!0}=this.props,g=p&&a.querySelector("input")||a,f=Oe(g),m=Oe(this.overlay),v=so(this.firstScrollableContainer)?document.body:this.firstScrollableContainer,P=Oe(v),S=u||i==="cover"?new At({...m,width:f.width}):m;v===document.body&&(P.height=document.body.scrollHeight);let A=0;const C=v.querySelector(`${xr.selector}`);C&&(A=C.clientHeight);const B=this.overlay.firstElementChild&&this.overlay.firstChild instanceof HTMLElement?io(this.overlay.firstElementChild):{activator:0,container:0,horizontal:0},T=In(),M=lo(a),x=M==null?M:M+1,h=Yr(f,S,B,P,T,i,d,A),b=Jr(f,S,T,B,l),I=f.center.x-b+B.horizontal*2;this.setState({measuring:!1,activatorRect:Oe(a),left:l!=="right"?b:void 0,right:l==="right"?b:void 0,top:r?o:h.top,lockPosition:!!d,height:h.height||0,width:u||i==="cover"?S.width:null,positioning:h.positioning,outsideScrollableContainer:s!=null&&Kr(f,eo(P)),zIndex:x,chevronOffset:I},()=>{this.overlay&&(this.observer.observe(this.overlay,qt),this.observer.observe(a,qt))})})},this.observer=new MutationObserver(this.handleMeasurement)}componentDidMount(){this.setScrollableContainers(),this.scrollableContainers.length&&!this.props.fixed&&this.registerScrollHandlers(),this.handleMeasurement()}componentWillUnmount(){this.observer.disconnect(),this.scrollableContainers.length&&!this.props.fixed&&this.unregisterScrollHandlers()}componentDidUpdate(){const{outsideScrollableContainer:n,top:r}=this.state,{onScrollOut:o,active:a}=this.props;a&&o!=null&&r!==0&&n&&o()}render(){const{left:n,right:r,top:o,zIndex:a,width:i}=this.state,{render:l,fixed:s,preventInteraction:u,classNames:d,zIndexOverride:p}=this.props,g={top:o==null||isNaN(o)?void 0:o,left:n==null||isNaN(n)?void 0:n,right:r==null||isNaN(r)?void 0:r,width:i==null||isNaN(i)?void 0:i,zIndex:p||a||void 0},f=_(Et.PositionedOverlay,s&&Et.fixed,u&&Et.preventInteraction,d);return t.createElement("div",{className:f,style:g,ref:this.setOverlay},t.createElement(at,{event:"resize",handler:this.handleMeasurement}),l(this.overlayDetails()))}get firstScrollableContainer(){return this.scrollableContainers[0]??null}forceUpdatePosition(){requestAnimationFrame(this.handleMeasurement)}}function io(e){const n=window.getComputedStyle(e);return{activator:parseFloat(n.marginTop||"0"),container:parseFloat(n.marginBottom||"0"),horizontal:parseFloat(n.marginLeft||"0")}}function lo(e){const n=e.closest(dn.selector)||document.body,r=n===document.body?"auto":parseInt(window.getComputedStyle(n).zIndex||"0",10);return r==="auto"||isNaN(r)?null:r}function so(e){return e===document}const co=t.createElement(t.Fragment,null,t.createElement("path",{d:"M18.829 8.171 11.862.921A3 3 0 0 0 7.619.838L0 8.171h1.442l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557h1.387Z",fill:"var(--p-color-tooltip-tail-up-border-experimental)"}),t.createElement("path",{d:"M17.442 10.171h-16v-2l6.87-6.612a2 2 0 0 1 2.83.055l6.3 6.557v2Z",fill:"var(--p-color-bg-surface)"})),uo=t.createElement(t.Fragment,null,t.createElement("path",{d:"m0 2 6.967 7.25a3 3 0 0 0 4.243.083L18.829 2h-1.442l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2H0Z",fill:"var(--p-color-tooltip-tail-down-border-experimental)"}),t.createElement("path",{d:"M1.387 0h16v2l-6.87 6.612a2 2 0 0 1-2.83-.055L1.387 2V0Z",fill:"var(--p-color-bg-surface)"}));function po({active:e,activator:n,preferredPosition:r="above",preventInteraction:o,id:a,children:i,accessibilityLabel:l,width:s,padding:u,borderRadius:d,zIndexOverride:p,instant:g}){const f=pe();return e?t.createElement(Bn,{active:e,activator:n,preferredPosition:r,preventInteraction:o,render:v,zIndexOverride:p}):null;function v(P){const{measuring:S,desiredHeight:A,positioning:C,chevronOffset:B}=P,T=_(ve.TooltipOverlay,S&&ve.measuring,!S&&ve.measured,g&&ve.instant,C==="above"&&ve.positionedAbove),M=_(ve.Content,s&&ve[s]),x=S?void 0:{minHeight:A},h={"--pc-tooltip-chevron-x-pos":`${B}px`,"--pc-tooltip-border-radius":d?`var(--p-border-radius-${d})`:void 0,"--pc-tooltip-padding":u&&u==="default"?"var(--p-space-100) var(--p-space-200)":`var(--p-space-${u})`};return t.createElement("div",Object.assign({style:h,className:T},dn.props),t.createElement("svg",{className:ve.Tail,width:"19",height:"11",fill:"none"},C==="above"?uo:co),t.createElement("div",{id:a,role:"tooltip",className:M,style:{...x,...h},"aria-label":l?f.translate("Polaris.TooltipOverlay.accessibilityLabel",{label:l}):void 0},i))}}const mo=150;function Ue({children:e,content:n,dismissOnMouseOut:r,active:o,hoverDelay:a,preferredPosition:i="above",activatorWrapper:l="span",accessibilityLabel:s,width:u="default",padding:d="default",borderRadius:p,zIndexOverride:g,hasUnderline:f,persistOnClick:m,onOpen:v,onClose:P}){const S=p||"200",A=l,{value:C,setTrue:B,setFalse:T}=Ge(!!o),{value:M,toggle:x}=Ge(!!o&&!!m),[h,b]=c.useState(null),{presenceList:I,addPresence:F,removePresence:G}=qr(),L=c.useId(),R=c.useRef(null),k=c.useRef(!1),[y,$]=c.useState(!o),O=c.useRef(null),Q=c.useRef(null),te=c.useCallback(()=>{o!==!1&&B()},[o,B]);c.useEffect(()=>{const ne=(R.current?fr(R.current):null)||R.current;ne&&(ne.tabIndex=0,ne.setAttribute("aria-describedby",L),ne.setAttribute("data-polaris-tooltip-activator","true"))},[L,e]),c.useEffect(()=>()=>{O.current&&clearTimeout(O.current),Q.current&&clearTimeout(Q.current)},[]);const ae=c.useCallback(()=>{$(!I.tooltip&&!C),v==null||v(),F("tooltip")},[F,I.tooltip,v,C]),U=c.useCallback(()=>{P==null||P(),$(!1),Q.current=setTimeout(()=>{G("tooltip")},mo)},[G,P]),X=c.useCallback(ie=>{ie.key==="Escape"&&(U==null||U(),T(),m&&x())},[T,U,m,x]);c.useEffect(()=>{o===!1&&C&&(U(),T())},[o,C,U,T]);const le=h?t.createElement(Tn,{idPrefix:"tooltip"},t.createElement(po,{id:L,preferredPosition:i,activator:h,active:C,accessibilityLabel:s,onClose:fo,preventInteraction:r,width:u,padding:d,borderRadius:S,zIndexOverride:g,instant:!y},t.createElement(W,{as:"span",variant:"bodyMd"},n))):null,Y=_(l==="div"&&jt.TooltipContainer,f&&jt.HasUnderline);return t.createElement(A,{onFocus:()=>{ae(),te()},onBlur:()=>{U(),T(),m&&x()},onMouseLeave:J,onMouseOver:Ce,onMouseDown:m?x:void 0,ref:me,onKeyUp:X,className:Y},e,le);function me(ie){const ne=R;if(ie==null){ne.current=null,b(null);return}ie.firstElementChild instanceof HTMLElement&&b(ie.firstElementChild),ne.current=ie}function Se(){k.current=!0,a&&!I.tooltip?O.current=setTimeout(()=>{ae(),te()},a):(ae(),te())}function J(){O.current&&(clearTimeout(O.current),O.current=null),k.current=!1,U(),M||T()}function Ce(){!k.current&&Se()}}function fo(){}function Mn({id:e,badge:n,content:r,accessibilityLabel:o,helpText:a,url:i,onAction:l,onMouseEnter:s,icon:u,image:d,prefix:p,suffix:g,disabled:f,external:m,destructive:v,ellipsis:P,truncate:S,active:A,role:C,variant:B="default"}){const T=_(K.Item,f&&K.disabled,v&&K.destructive,A&&K.active,B==="default"&&K.default,B==="indented"&&K.indented,B==="menu"&&K.menu);let M=null;p?M=t.createElement("span",{className:K.Prefix},p):u?M=t.createElement("span",{className:K.Prefix},t.createElement(ge,{source:u})):d&&(M=t.createElement("span",{role:"presentation",className:K.Prefix,style:{backgroundImage:`url(${d}`}}));let x=r||"";S&&r?x=t.createElement(ho,null,r):P&&(x=`${r}…`);const h=a?t.createElement(t.Fragment,null,t.createElement(z,null,x),t.createElement(W,{as:"span",variant:"bodySm",tone:A||f?void 0:"subdued"},a)):t.createElement(W,{as:"span",variant:"bodyMd",fontWeight:A?"semibold":"regular"},x),b=n&&t.createElement("span",{className:K.Suffix},t.createElement(An,{tone:n.tone},n.content)),I=g&&t.createElement(z,null,t.createElement("span",{className:K.Suffix},g)),F=t.createElement("span",{className:K.Text},t.createElement(W,{as:"span",variant:"bodyMd",fontWeight:A?"semibold":"regular"},h)),G=t.createElement(ze,{blockAlign:"center",gap:"150",wrap:!1},M,F,b,I),L=t.createElement(z,{width:"100%"},G),R=A?t.createElement(ke.ScrollTo,null):null,k=i?t.createElement(hr,{id:e,url:f?null:i,className:T,external:m,"aria-label":o,onClick:f?null:l,role:C},L):t.createElement("button",{id:e,type:"button",className:T,disabled:f,"aria-label":o,onClick:l,onMouseUp:ln,role:C,onMouseEnter:s},L);return t.createElement(t.Fragment,null,R,k)}const ho=({children:e})=>{const n=Lr(),r=c.useRef(null),[o,a]=c.useState(!1);return De(()=>{r.current&&a(r.current.scrollWidth>r.current.offsetWidth)},[e]),o?t.createElement(Ue,{zIndexOverride:Number(n.zIndex["z-index-11"]),preferredPosition:"above",hoverDelay:1e3,content:e,dismissOnMouseOut:!0},t.createElement(W,{as:"span",truncate:!0},e)):t.createElement(W,{as:"span",truncate:!0},t.createElement(z,{width:"100%",ref:r},e))};function go({section:e,hasMultipleSections:n,isFirst:r,actionRole:o,onActionAnyItem:a}){const i=p=>()=>{p&&p(),a&&a()},l=e.items.map(({content:p,helpText:g,onAction:f,...m},v)=>{const P=t.createElement(Mn,Object.assign({content:p,helpText:g,role:o,onAction:i(f)},m));return t.createElement(z,{as:"li",key:`${p}-${v}`,role:o==="menuitem"?"presentation":void 0},t.createElement(ze,{wrap:!1},P))});let s=null;e.title&&(s=typeof e.title=="string"?t.createElement(z,{paddingBlockStart:"300",paddingBlockEnd:"100",paddingInlineStart:"300",paddingInlineEnd:"300"},t.createElement(W,{as:"p",variant:"headingSm"},e.title)):t.createElement(z,{padding:"200",paddingInlineEnd:"150"},e.title));let u;switch(o){case"option":u="presentation";break;case"menuitem":u=n?"presentation":"menu";break;default:u=void 0;break}const d=t.createElement(t.Fragment,null,s,t.createElement(z,Object.assign({as:"div",padding:"150"},n&&{paddingBlockStart:"0"},{tabIndex:n?void 0:-1}),t.createElement(Gr,Object.assign({gap:"050",as:"ul"},u&&{role:u}),l)));return n?t.createElement(z,Object.assign({as:"li",role:"presentation",borderColor:"border-secondary"},!r&&{borderBlockStartWidth:"025"},!e.title&&{paddingBlockStart:"150"}),d):d}function je({keyCode:e,handler:n,keyEvent:r="keyup",options:o,useCapture:a}){const i=c.useRef({handler:n,keyCode:e});De(()=>{i.current={handler:n,keyCode:e}},[n,e]);const l=c.useCallback(s=>{const{handler:u,keyCode:d}=i.current;s.keyCode===d&&u(s)},[]);return c.useEffect(()=>(document.addEventListener(r,l,a||o),()=>{document.removeEventListener(r,l,a||o)}),[r,l,a,o]),null}var N={TextField:"Polaris-TextField",ClearButton:"Polaris-TextField__ClearButton",Loading:"Polaris-TextField__Loading",disabled:"Polaris-TextField--disabled",error:"Polaris-TextField--error",readOnly:"Polaris-TextField--readOnly",Input:"Polaris-TextField__Input",Backdrop:"Polaris-TextField__Backdrop",multiline:"Polaris-TextField--multiline",hasValue:"Polaris-TextField--hasValue",focus:"Polaris-TextField--focus",VerticalContent:"Polaris-TextField__VerticalContent",InputAndSuffixWrapper:"Polaris-TextField__InputAndSuffixWrapper",toneMagic:"Polaris-TextField--toneMagic",Prefix:"Polaris-TextField__Prefix",Suffix:"Polaris-TextField__Suffix",AutoSizeWrapper:"Polaris-TextField__AutoSizeWrapper",AutoSizeWrapperWithSuffix:"Polaris-TextField__AutoSizeWrapperWithSuffix",suggestion:"Polaris-TextField--suggestion",borderless:"Polaris-TextField--borderless",slim:"Polaris-TextField--slim","Input-hasClearButton":"Polaris-TextField__Input--hasClearButton","Input-suffixed":"Polaris-TextField__Input--suffixed","Input-alignRight":"Polaris-TextField__Input--alignRight","Input-alignLeft":"Polaris-TextField__Input--alignLeft","Input-alignCenter":"Polaris-TextField__Input--alignCenter","Input-autoSize":"Polaris-TextField__Input--autoSize",PrefixIcon:"Polaris-TextField__PrefixIcon",CharacterCount:"Polaris-TextField__CharacterCount",AlignFieldBottom:"Polaris-TextField__AlignFieldBottom",Spinner:"Polaris-TextField__Spinner",SpinnerIcon:"Polaris-TextField__SpinnerIcon",Resizer:"Polaris-TextField__Resizer",DummyInput:"Polaris-TextField__DummyInput",Segment:"Polaris-TextField__Segment",monospaced:"Polaris-TextField--monospaced"};const vo=t.forwardRef(function({onChange:n,onClick:r,onMouseDown:o,onMouseUp:a,onBlur:i},l){function s(d){return()=>n(d)}function u(d){return p=>{p.button===0&&(o==null||o(d))}}return t.createElement("div",{className:N.Spinner,onClick:r,"aria-hidden":!0,ref:l},t.createElement("div",{role:"button",className:N.Segment,tabIndex:-1,onClick:s(1),onMouseDown:u(s(1)),onMouseUp:a,onBlur:i},t.createElement("div",{className:N.SpinnerIcon},t.createElement(ge,{source:gr}))),t.createElement("div",{role:"button",className:N.Segment,tabIndex:-1,onClick:s(-1),onMouseDown:u(s(-1)),onMouseUp:a,onBlur:i},t.createElement("div",{className:N.SpinnerIcon},t.createElement(ge,{source:vr}))))});var Pe={hidden:"Polaris-Labelled--hidden",LabelWrapper:"Polaris-Labelled__LabelWrapper",disabled:"Polaris-Labelled--disabled",HelpText:"Polaris-Labelled__HelpText",readOnly:"Polaris-Labelled--readOnly",Error:"Polaris-Labelled__Error",Action:"Polaris-Labelled__Action"},Qt={InlineError:"Polaris-InlineError",Icon:"Polaris-InlineError__Icon"};function bo({message:e,fieldID:n}){return e?t.createElement("div",{id:Eo(n),className:Qt.InlineError},t.createElement("div",{className:Qt.Icon},t.createElement(ge,{source:fn})),t.createElement(W,{as:"span",variant:"bodyMd"},e)):null}function Eo(e){return`${e}Error`}var Je={Label:"Polaris-Label",hidden:"Polaris-Label--hidden",Text:"Polaris-Label__Text",RequiredIndicator:"Polaris-Label__RequiredIndicator"};function yn(e){return`${e}Label`}function Po({children:e,id:n,hidden:r,requiredIndicator:o}){const a=_(Je.Label,r&&Je.hidden);return t.createElement("div",{className:a},t.createElement("label",{id:yn(n),htmlFor:n,className:_(Je.Text,o&&Je.RequiredIndicator)},t.createElement(W,{as:"span",variant:"bodyMd"},e)))}function So({id:e,label:n,error:r,action:o,helpText:a,children:i,labelHidden:l,requiredIndicator:s,disabled:u,readOnly:d,...p}){const g=_(l&&Pe.hidden,u&&Pe.disabled,d&&Pe.readOnly),f=o?t.createElement("div",{className:Pe.Action},Sn(o,{variant:"plain"})):null,m=a?t.createElement("div",{className:Pe.HelpText,id:Fn(e),"aria-disabled":u},t.createElement(W,{as:"span",tone:"subdued",variant:"bodyMd",breakWord:!0},a)):null,v=r&&typeof r!="boolean"&&t.createElement("div",{className:Pe.Error},t.createElement(bo,{message:r,fieldID:e})),P=n?t.createElement("div",{className:Pe.LabelWrapper},t.createElement(Po,Object.assign({id:e,requiredIndicator:s},p,{hidden:!1}),n),f):null;return t.createElement("div",{className:g},P,i,v,m)}function Fn(e){return`${e}HelpText`}var Re={Connected:"Polaris-Connected",Item:"Polaris-Connected__Item","Item-primary":"Polaris-Connected__Item--primary","Item-focused":"Polaris-Connected__Item--focused"};function Pt({children:e,position:n}){const{value:r,setTrue:o,setFalse:a}=Ge(!1),i=_(Re.Item,r&&Re["Item-focused"],n==="primary"?Re["Item-primary"]:Re["Item-connection"]);return t.createElement("div",{onBlur:a,onFocus:o,className:i},e)}function Co({children:e,left:n,right:r}){const o=n?t.createElement(Pt,{position:"left"},n):null,a=r?t.createElement(Pt,{position:"right"},r):null;return t.createElement("div",{className:Re.Connected},o,t.createElement(Pt,{position:"primary"},e),a)}function xo({contents:e,currentHeight:n=null,minimumLines:r,onHeightChange:o}){const a=c.useRef(null),i=c.useRef(null),l=c.useRef(),s=c.useRef(n);n!==s.current&&(s.current=n),c.useEffect(()=>()=>{l.current&&cancelAnimationFrame(l.current)},[]);const u=r?t.createElement("div",{ref:i,className:N.DummyInput,dangerouslySetInnerHTML:{__html:Io(r)}}):null,d=c.useCallback(()=>{l.current&&cancelAnimationFrame(l.current),l.current=requestAnimationFrame(()=>{if(!a.current||!i.current)return;const p=Math.max(a.current.offsetHeight,i.current.offsetHeight);p!==s.current&&o(p)})},[o]);return De(()=>{d()}),t.createElement("div",{"aria-hidden":!0,className:N.Resizer},t.createElement(at,{event:"resize",handler:d}),t.createElement("div",{ref:a,className:N.DummyInput,dangerouslySetInnerHTML:{__html:No(e)}}),u)}const Ln={"&":"&amp;","<":"&lt;",">":"&gt;","\n":"<br>","\r":""},Ao=new RegExp(`[${Object.keys(Ln).join()}]`,"g");function To(e){return Ln[e]}function Io(e){let n="";for(let r=0;r<e;r++)n+="<br>";return n}function No(e){return e?`${e.replace(Ao,To)}<br>`:"<br>"}function ko({prefix:e,suffix:n,verticalContent:r,placeholder:o,value:a="",helpText:i,label:l,labelAction:s,labelHidden:u,disabled:d,clearButton:p,readOnly:g,autoFocus:f,focused:m,multiline:v,error:P,connectedRight:S,connectedLeft:A,type:C="text",name:B,id:T,role:M,step:x,largeStep:h,autoComplete:b,max:I,maxLength:F,maxHeight:G,min:L,minLength:R,pattern:k,inputMode:y,spellCheck:$,ariaOwns:O,ariaControls:Q,ariaExpanded:te,ariaActiveDescendant:ae,ariaAutocomplete:U,showCharacterCount:X,align:le,requiredIndicator:Y,monospaced:me,selectTextOnFocus:Se,suggestion:J,variant:Ce="inherit",size:ie="medium",onClearButtonClick:ne,onChange:re,onSpinnerChange:se,onFocus:Ze,onBlur:Ve,tone:It,autoSize:it,loading:zn}){const lt=pe(),[st,Gn]=c.useState(null),[we,ct]=c.useState(!!m),Un=cn(),jn=c.useId(),H=T??jn,Nt=c.useRef(null),fe=c.useRef(null),kt=c.useRef(null),ut=c.useRef(null),dt=c.useRef(null),pt=c.useRef(null),qe=c.useRef(null),mt=c.useRef(),ft=c.useRef(null),xe=c.useCallback(()=>v?kt.current:fe.current,[v]);c.useEffect(()=>{const E=xe();!E||m===void 0||(m?E.focus():E.blur())},[m,r,xe]),c.useEffect(()=>{const E=fe.current;!E||!(C==="text"||C==="tel"||C==="search"||C==="url"||C==="password")||!J||E.setSelectionRange(a.length,J.length)},[we,a,C,J]);const _e=J||a,wt=x??1,_t=I??1/0,Bt=L??-1/0,Zn=_(N.TextField,!!_e&&N.hasValue,d&&N.disabled,g&&N.readOnly,P&&N.error,It&&N[de("tone",It)],v&&N.multiline,we&&!d&&N.focus,Ce!=="inherit"&&N[Ce],ie==="slim"&&N.slim),Vn=C==="currency"?"text":C,Qe=C==="number"||C==="integer",qn=t.isValidElement(e)&&e.type===ge,Qn=e?t.createElement("div",{className:_(N.Prefix,qn&&N.PrefixIcon),id:`${H}-Prefix`,ref:ut},t.createElement(W,{as:"span",variant:"bodyMd"},e)):null,Mt=n?t.createElement("div",{className:N.Suffix,id:`${H}-Suffix`,ref:dt},t.createElement(W,{as:"span",variant:"bodyMd"},n)):null,Xn=zn?t.createElement("div",{className:N.Loading,id:`${H}-Loading`,ref:pt},t.createElement(br,{size:"small"})):null;let yt=null;if(X){const E=_e.length,w=F?lt.translate("Polaris.TextField.characterCountWithMaxLength",{count:E,limit:F}):lt.translate("Polaris.TextField.characterCount",{count:E}),Z=_(N.CharacterCount,v&&N.AlignFieldBottom),V=F?`${E}/${F}`:E;yt=t.createElement("div",{id:`${H}-CharacterCounter`,className:Z,"aria-label":w,"aria-live":we?"polite":"off","aria-atomic":"true",onClick:Xe},t.createElement(W,{as:"span",variant:"bodyMd"},V))}const Yn=p&&_e!==""?t.createElement("button",{type:"button",className:N.ClearButton,onClick:cr,disabled:d},t.createElement(W,{as:"span",visuallyHidden:!0},lt.translate("Polaris.Common.clear")),t.createElement(ge,{source:Pn,tone:"base"})):null,Be=c.useCallback((E,w=wt)=>{if(re==null&&se==null)return;const Z=pr=>(pr.toString().split(".")[1]||[]).length,V=a?parseFloat(a):0;if(isNaN(V))return;const ce=C==="integer"?0:Math.max(Z(V),Z(w)),Fe=Math.min(Number(_t),Math.max(V+E*w,Number(Bt)));se!=null?se(String(Fe.toFixed(ce)),H):re!=null&&re(String(Fe.toFixed(ce)),H)},[H,_t,Bt,re,se,wt,C,a]),ht=c.useCallback(()=>{clearTimeout(mt.current)},[]),Jn=c.useCallback(E=>{let V=200;const ce=()=>{V>50&&(V-=10),E(0),mt.current=window.setTimeout(ce,V)};mt.current=window.setTimeout(ce,V),document.addEventListener("mouseup",ht,{once:!0})},[ht]),Kn=Qe&&x!==0&&!d&&!g?t.createElement(vo,{onClick:Xe,onChange:Be,onMouseDown:Jn,onMouseUp:ht,ref:ft,onBlur:Ht}):null,er=v&&st?{height:st,maxHeight:G}:null,tr=c.useCallback(E=>{Gn(E)},[]),nr=v&&Un?t.createElement(xo,{contents:_e||o,currentHeight:st,minimumLines:typeof v=="number"?v:1,onHeightChange:tr}):null,Me=[];P&&Me.push(`${H}Error`),i&&Me.push(Fn(H)),X&&Me.push(`${H}-CharacterCounter`);const ye=[];e&&ye.push(`${H}-Prefix`),n&&ye.push(`${H}-Suffix`),r&&ye.push(`${H}-VerticalContent`),ye.unshift(yn(H));const rr=_(N.Input,le&&N[de("Input-align",le)],n&&N["Input-suffixed"],p&&N["Input-hasClearButton"],me&&N.monospaced,J&&N.suggestion,it&&N["Input-autoSize"]),Ft=E=>{if(ct(!0),Se&&!J){const w=xe();w==null||w.select()}Ze&&Ze(E)};mn("wheel",or,fe);function or(E){document.activeElement===E.target&&Qe&&E.stopPropagation()}const Lt=c.createElement(v?"textarea":"input",{name:B,id:H,disabled:d,readOnly:g,role:M,autoFocus:f,value:_e,placeholder:o,style:er,autoComplete:b,className:rr,ref:v?kt:fe,min:L,max:I,step:x,minLength:R,maxLength:F,spellCheck:$,pattern:k,inputMode:y,type:Vn,rows:wo(v),size:it?1:void 0,"aria-describedby":Me.length?Me.join(" "):void 0,"aria-labelledby":ye.join(" "),"aria-invalid":!!P,"aria-owns":O,"aria-activedescendant":ae,"aria-autocomplete":U,"aria-controls":Q,"aria-expanded":te,"aria-required":Y,..._o(v),onFocus:Ft,onBlur:Ht,onClick:Xe,onKeyPress:ur,onKeyDown:dr,onChange:J?void 0:Rt,onInput:J?Rt:void 0,"data-1p-ignore":b==="off"||void 0,"data-lpignore":b==="off"||void 0,"data-form-type":b==="off"?"other":void 0}),ar=r?t.createElement("div",{className:N.VerticalContent,id:`${H}-VerticalContent`,ref:qe,onClick:Xe},r,Lt):null,Ot=r?ar:Lt,ir=t.createElement("div",{className:_(N.Backdrop,A&&N["Backdrop-connectedLeft"],S&&N["Backdrop-connectedRight"])}),lr=it?t.createElement("div",{className:N.InputAndSuffixWrapper},t.createElement("div",{className:_(N.AutoSizeWrapper,n&&N.AutoSizeWrapperWithSuffix),"data-auto-size-value":a||o},Ot),Mt):t.createElement(t.Fragment,null,Ot,Mt);return t.createElement(So,{label:l,id:H,error:P,action:s,labelHidden:u,helpText:i,requiredIndicator:Y,disabled:d,readOnly:g},t.createElement(Co,{left:A,right:S},t.createElement("div",{className:Zn,onClick:sr,ref:Nt},Qn,lr,yt,Xn,Yn,Kn,ir,nr)));function Rt(E){re&&re(E.currentTarget.value,H)}function sr(E){var V,ce,Fe;const{target:w}=E,Z=(V=fe==null?void 0:fe.current)==null?void 0:V.getAttribute("role");if(w===fe.current&&Z==="combobox"){(ce=fe.current)==null||ce.focus(),Ft(E);return}Wt(w)||zt(w)||gt(w)||$t(w)||Dt(w)||we||(Fe=xe())==null||Fe.focus()}function Xe(E){var w;!$t(E.target)&&!gt(E.target)&&E.stopPropagation(),!(Wt(E.target)||zt(E.target)||gt(E.target)||Dt(E.target)||we)&&(ct(!0),(w=xe())==null||w.focus())}function cr(){ne&&ne(H)}function ur(E){const{key:w,which:Z}=E,V=/[\d.,eE+-]$/,ce=/[\deE+-]$/;!Qe||Z===oe.Enter||C==="number"&&V.test(w)||C==="integer"&&ce.test(w)||E.preventDefault()}function dr(E){if(!Qe)return;const{key:w,which:Z}=E;C==="integer"&&(w==="ArrowUp"||Z===oe.UpArrow)&&(Be(1),E.preventDefault()),C==="integer"&&(w==="ArrowDown"||Z===oe.DownArrow)&&(Be(-1),E.preventDefault()),(Z===oe.Home||w==="Home")&&L!==void 0&&(se!=null?se(String(L),H):re!=null&&re(String(L),H)),(Z===oe.End||w==="End")&&I!==void 0&&(se!=null?se(String(I),H):re!=null&&re(String(I),H)),(Z===oe.PageUp||w==="PageUp")&&h!==void 0&&Be(1,h),(Z===oe.PageDown||w==="PageDown")&&h!==void 0&&Be(-1,h)}function Ht(E){var w;ct(!1),!((w=Nt.current)!=null&&w.contains(E==null?void 0:E.relatedTarget))&&Ve&&Ve(E)}function gt(E){const w=xe();return E instanceof HTMLElement&&w&&(w.contains(E)||w.contains(document.activeElement))}function Wt(E){return E instanceof Element&&(ut.current&&ut.current.contains(E)||dt.current&&dt.current.contains(E))}function $t(E){return E instanceof Element&&ft.current&&ft.current.contains(E)}function Dt(E){return E instanceof Element&&pt.current&&pt.current.contains(E)}function zt(E){return E instanceof Element&&qe.current&&(qe.current.contains(E)||qe.current.contains(document.activeElement))}}function wo(e){if(e)return typeof e=="number"?e:1}function _o(e){if(e)return e||typeof e=="number"&&e>0?{"aria-multiline":!0}:void 0}const Bo=8;function Tt({items:e,sections:n=[],actionRole:r,allowFiltering:o,onActionAnyItem:a}){const i=pe(),l=c.useContext(Cn);let s=[];const u=c.useRef(null),[d,p]=c.useState("");e?s=[{items:e},...n]:n&&(s=n);const g=s==null?void 0:s.some(h=>h.items.some(b=>typeof b.content=="string")),f=s.length>1,m=f&&r==="menuitem"?"menu":void 0,v=f&&r==="menuitem"?-1:void 0,P=s==null?void 0:s.map(h=>({...h,items:h.items.filter(({content:b})=>typeof b=="string"?b==null?void 0:b.toLowerCase().includes(d.toLowerCase()):b)})),S=P.map((h,b)=>h.items.length>0?t.createElement(go,{key:typeof h.title=="string"?h.title:b,section:h,hasMultipleSections:f,actionRole:r,onActionAnyItem:a,isFirst:b===0}):null),A=h=>{h.preventDefault(),u.current&&h.target&&u.current.contains(h.target)&&Pr(u.current,h.target)},C=h=>{h.preventDefault(),u.current&&h.target&&u.current.contains(h.target)&&Er(u.current,h.target)},B=r==="menuitem"?t.createElement(t.Fragment,null,t.createElement(je,{keyEvent:"keydown",keyCode:oe.DownArrow,handler:C}),t.createElement(je,{keyEvent:"keydown",keyCode:oe.UpArrow,handler:A})):null,T=c.useMemo(()=>(P==null?void 0:P.reduce((b,I)=>b+I.items.length,0))||0,[P]),x=((s==null?void 0:s.reduce((h,b)=>h+b.items.length,0))||0)>=Bo;return t.createElement(t.Fragment,null,(o||l)&&x&&g&&t.createElement(z,{padding:"200",paddingBlockEnd:T>0?"0":"200"},t.createElement(ko,{clearButton:!0,labelHidden:!0,label:i.translate("Polaris.ActionList.SearchField.placeholder"),placeholder:i.translate("Polaris.ActionList.SearchField.placeholder"),autoComplete:"off",value:d,onChange:h=>p(h),prefix:t.createElement(ge,{source:En}),onClearButtonClick:()=>p("")})),t.createElement(z,{as:f?"ul":"div",ref:u,role:m,tabIndex:v},B,S))}Tt.Item=Mn;var Xt={ActionMenu:"Polaris-ActionMenu"},Mo={RollupActivator:"Polaris-ActionMenu-RollupActions__RollupActivator"};function yo(e,{id:n,active:r=!1,ariaHaspopup:o,activatorDisabled:a=!1}){a||(e.tabIndex=e.tabIndex||0),e.setAttribute("aria-controls",n),e.setAttribute("aria-owns",n),e.setAttribute("aria-expanded",String(r)),e.setAttribute("data-state",r?"open":"closed"),o!=null&&e.setAttribute("aria-haspopup",String(o))}function On(e,n,r){return e==null?null:Rn(e,n)?e:t.createElement(n,r,e)}const Fo=(e,n)=>e===n;function Rn(e,n){var l;if(e==null||!c.isValidElement(e)||typeof e.type=="string")return!1;const{type:r}=e,a=((l=e.props)==null?void 0:l.__type__)||r;return(Array.isArray(n)?n:[n]).some(s=>typeof a!="string"&&Fo(s,a))}function Lo(e,n=()=>!0){return c.Children.toArray(e).filter(r=>c.isValidElement(r)&&n(r))}function Oo({condition:e,wrapper:n,children:r}){return e?n(r):r}function St({condition:e,children:n}){return e?n:null}var D={Popover:"Polaris-Popover",PopoverOverlay:"Polaris-Popover__PopoverOverlay","PopoverOverlay-noAnimation":"Polaris-Popover__PopoverOverlay--noAnimation","PopoverOverlay-entering":"Polaris-Popover__PopoverOverlay--entering","PopoverOverlay-open":"Polaris-Popover__PopoverOverlay--open",measuring:"Polaris-Popover--measuring","PopoverOverlay-exiting":"Polaris-Popover__PopoverOverlay--exiting",fullWidth:"Polaris-Popover--fullWidth",Content:"Polaris-Popover__Content",positionedAbove:"Polaris-Popover--positionedAbove",positionedCover:"Polaris-Popover--positionedCover",ContentContainer:"Polaris-Popover__ContentContainer","Content-fullHeight":"Polaris-Popover__Content--fullHeight","Content-fluidContent":"Polaris-Popover__Content--fluidContent",Pane:"Polaris-Popover__Pane","Pane-fixed":"Polaris-Popover__Pane--fixed","Pane-subdued":"Polaris-Popover__Pane--subdued","Pane-captureOverscroll":"Polaris-Popover__Pane--captureOverscroll",Section:"Polaris-Popover__Section",FocusTracker:"Polaris-Popover__FocusTracker","PopoverOverlay-hideOnPrint":"Polaris-Popover__PopoverOverlay--hideOnPrint"};function Hn({children:e}){return t.createElement("div",{className:D.Section},t.createElement(z,{paddingInlineStart:"300",paddingInlineEnd:"300",paddingBlockStart:"200",paddingBlockEnd:"150"},e))}function Ct({captureOverscroll:e=!1,fixed:n,sectioned:r,children:o,height:a,subdued:i,onScrolledToBottom:l}){const s=_(D.Pane,n&&D["Pane-fixed"],i&&D["Pane-subdued"],e&&D["Pane-captureOverscroll"]),u=r?On(o,Hn,{}):o,d=a?{height:a,maxHeight:a,minHeight:a}:void 0;return n?t.createElement("div",{style:d,className:s},u):t.createElement(ke,{shadow:!0,className:s,style:d,onScrolledToBottom:l,scrollbarWidth:"thin"},u)}let he;(function(e){e[e.Click=0]="Click",e[e.EscapeKeypress=1]="EscapeKeypress",e[e.FocusOut=2]="FocusOut",e[e.ScrollOut=3]="ScrollOut"})(he||(he={}));var ee;(function(e){e.Entering="entering",e.Entered="entered",e.Exiting="exiting",e.Exited="exited"})(ee||(ee={}));class Wn extends c.PureComponent{constructor(n){super(n),this.state={transitionStatus:this.props.active?ee.Entering:ee.Exited},this.contentNode=c.createRef(),this.renderPopover=r=>{const{measuring:o,desiredHeight:a,positioning:i}=r,{id:l,children:s,sectioned:u,fullWidth:d,fullHeight:p,fluidContent:g,hideOnPrint:f,autofocusTarget:m,captureOverscroll:v}=this.props,P=i==="cover",S=_(D.Popover,o&&D.measuring,(d||P)&&D.fullWidth,f&&D["PopoverOverlay-hideOnPrint"],i&&D[de("positioned",i)]),A=o?void 0:{height:a},C=_(D.Content,p&&D["Content-fullHeight"],g&&D["Content-fluidContent"]);return t.createElement("div",Object.assign({className:S},Ar.props),t.createElement(at,{event:"click",handler:this.handleClick}),t.createElement(at,{event:"touchstart",handler:this.handleClick}),t.createElement(je,{keyCode:oe.Escape,handler:this.handleEscape}),t.createElement("div",{className:D.FocusTracker,tabIndex:0,onFocus:this.handleFocusFirstItem}),t.createElement("div",{className:D.ContentContainer},t.createElement("div",{id:l,tabIndex:m==="none"?void 0:-1,className:C,style:A,ref:this.contentNode},Ro(s,{captureOverscroll:v,sectioned:u}))),t.createElement("div",{className:D.FocusTracker,tabIndex:0,onFocus:this.handleFocusLastItem}))},this.handleClick=r=>{const o=r.target,{contentNode:a,props:{activator:i,onClose:l,preventCloseOnChildOverlayClick:s}}=this,u=r.composedPath(),d=s?Ho(u,this.context.container):Jt(u,a),p=Yt(i,o);d||p||this.state.transitionStatus!==ee.Entered||l(he.Click)},this.handleScrollOut=()=>{this.props.onClose(he.ScrollOut)},this.handleEscape=r=>{const o=r.target,{contentNode:a,props:{activator:i}}=this,l=r.composedPath(),s=Jt(l,a),u=Yt(i,o);(s||u)&&this.props.onClose(he.EscapeKeypress)},this.handleFocusFirstItem=()=>{this.props.onClose(he.FocusOut)},this.handleFocusLastItem=()=>{this.props.onClose(he.FocusOut)},this.overlayRef=c.createRef()}forceUpdatePosition(){var n;(n=this.overlayRef.current)==null||n.forceUpdatePosition()}changeTransitionStatus(n,r){this.setState({transitionStatus:n},r),this.contentNode.current&&this.contentNode.current.getBoundingClientRect()}componentDidMount(){this.props.active&&(this.focusContent(),this.changeTransitionStatus(ee.Entered))}componentDidUpdate(n){this.props.active&&!n.active&&(this.focusContent(),this.changeTransitionStatus(ee.Entering,()=>{this.clearTransitionTimeout(),this.enteringTimer=window.setTimeout(()=>{this.setState({transitionStatus:ee.Entered})},parseInt(Tr.motion["motion-duration-100"],10))})),!this.props.active&&n.active&&(this.clearTransitionTimeout(),this.setState({transitionStatus:ee.Exited}))}componentWillUnmount(){this.clearTransitionTimeout()}render(){const{active:n,activator:r,fullWidth:o,preferredPosition:a="below",preferredAlignment:i="center",preferInputActivator:l=!0,fixed:s,zIndexOverride:u}=this.props,{transitionStatus:d}=this.state;if(d===ee.Exited&&!n)return null;const p=_(D.PopoverOverlay,d===ee.Entering&&D["PopoverOverlay-entering"],d===ee.Entered&&D["PopoverOverlay-open"],d===ee.Exiting&&D["PopoverOverlay-exiting"],a==="cover"&&D["PopoverOverlay-noAnimation"]);return t.createElement(Bn,{ref:this.overlayRef,fullWidth:o,active:n,activator:r,preferInputActivator:l,preferredPosition:a,preferredAlignment:i,render:this.renderPopover.bind(this),fixed:s,onScrollOut:this.handleScrollOut,classNames:p,zIndexOverride:u})}clearTransitionTimeout(){this.enteringTimer&&window.clearTimeout(this.enteringTimer)}focusContent(){const{autofocusTarget:n="container"}=this.props;n==="none"||this.contentNode==null||requestAnimationFrame(()=>{if(this.contentNode.current==null)return;const r=Sr(this.contentNode.current);r&&n==="first-node"?r.focus({preventScroll:!1}):this.contentNode.current.focus({preventScroll:!1})})}}Wn.contextType=pn;function Ro(e,n){const r=c.Children.toArray(e);return Rn(r[0],Ct)?r:On(r,Ct,n)}function Yt(e,n){if(e===n)return!0;let r=n.parentNode;for(;r!=null;){if(r===e)return!0;r=r.parentNode}return!1}function Jt(e,n){return n.current!=null&&e.includes(n.current)}function Ho(e,n){return e.some(r=>r instanceof Node&&(n==null?void 0:n.contains(r)))}const Wo=c.forwardRef(function({activatorWrapper:n="div",children:r,onClose:o,activator:a,preventFocusOnClose:i,active:l,fixed:s,ariaHaspopup:u,preferInputActivator:d=!0,zIndexOverride:p,...g},f){const[m,v]=c.useState(),P=c.useRef(null),S=c.useRef(null),A=n,C=c.useId();function B(){var h;(h=P.current)==null||h.forceUpdatePosition()}c.useImperativeHandle(f,()=>({forceUpdatePosition:B}));const T=c.useCallback(()=>{if(S.current==null)return;const b=Le(S.current)||S.current,I="disabled"in b&&!!b.disabled;yo(b,{id:C,active:l,ariaHaspopup:u,activatorDisabled:I})},[C,l,u]),M=h=>{if(o(h),!(S.current==null||i)){if(h===he.FocusOut&&m){const b=Le(m)||Le(S.current)||S.current;Gt(b,Kt)||b.focus()}else if(h===he.EscapeKeypress&&m){const b=Le(m)||Le(S.current)||S.current;b?b.focus():Gt(b,Kt)}}};c.useEffect(()=>{(!m&&S.current||m&&S.current&&!S.current.contains(m))&&v(S.current.firstElementChild),T()},[m,T]),c.useEffect(()=>{m&&S.current&&v(S.current.firstElementChild),T()},[m,T]);const x=m?t.createElement(Tn,{idPrefix:"popover"},t.createElement(Wn,Object.assign({ref:P,id:C,activator:m,preferInputActivator:d,onClose:M,active:l,fixed:s,zIndexOverride:p},g),r)):null;return t.createElement(A,{ref:S},c.Children.only(a),x)});function Kt(e){let n=e.parentElement;for(;n;){if(n.matches(Ir.selector))return!1;n=n.parentElement}return!0}const $n=Object.assign(Wo,{Pane:Ct,Section:Hn});function $o({accessibilityLabel:e,items:n=[],sections:r=[]}){const o=pe(),{value:a,toggle:i}=Ge(!1);if(n.length===0&&r.length===0)return null;const l=t.createElement("div",{className:Mo.RollupActivator},t.createElement(Ne,{icon:bn,accessibilityLabel:e||o.translate("Polaris.ActionMenu.RollupActions.rollupButton"),onClick:i}));return t.createElement($n,{active:a,activator:l,preferredAlignment:"right",onClose:i,hideOnPrint:!0},t.createElement(Tt,{items:n,sections:r,onActionAnyItem:i}))}var rt={ActionsLayoutOuter:"Polaris-ActionMenu-Actions__ActionsLayoutOuter",ActionsLayout:"Polaris-ActionMenu-Actions__ActionsLayout","ActionsLayout--measuring":"Polaris-ActionMenu-Actions--actionsLayoutMeasuring",ActionsLayoutMeasurer:"Polaris-ActionMenu-Actions__ActionsLayoutMeasurer"};function en(e=[],n=[],r,o,a){const i=o.reduce((f,m)=>f+m,0),l=e.map((f,m)=>m),s=n.map((f,m)=>m),u=[],d=[],p=[],g=[];if(a>i)u.push(...l),p.push(...s);else{let f=0;l.forEach(m=>{const v=o[m];if(f+v>=a-r){d.push(m);return}u.push(m),f+=v}),s.forEach(m=>{const v=o[m+e.length];if(f+v>=a-r){g.push(m);return}p.push(m),f+=v})}return{visibleActions:u,hiddenActions:d,visibleGroups:p,hiddenGroups:g}}var Do={Details:"Polaris-ActionMenu-MenuGroup__Details"},tn={SecondaryAction:"Polaris-ActionMenu-SecondaryAction",critical:"Polaris-ActionMenu-SecondaryAction--critical"};function We({children:e,tone:n,helpText:r,onAction:o,destructive:a,...i}){const l=t.createElement(Ne,Object.assign({onClick:o,tone:a?"critical":void 0},i),e),s=r?t.createElement(Ue,{preferredPosition:"below",content:r},l):l;return t.createElement("div",{className:_(tn.SecondaryAction,n==="critical"&&tn.critical)},s)}function nn({accessibilityLabel:e,active:n,actions:r,details:o,title:a,icon:i,disabled:l,onClick:s,onClose:u,onOpen:d,sections:p}){const g=c.useCallback(()=>{u(a)},[u,a]),f=c.useCallback(()=>{d(a)},[d,a]),m=c.useCallback(()=>{s?s(f):f()},[s,f]),v=t.createElement(We,{disclosure:!0,disabled:l,icon:i,accessibilityLabel:e,onClick:m},a);return t.createElement($n,{active:!!n,activator:v,preferredAlignment:"left",onClose:g,hideOnPrint:!0},t.createElement(Tt,{items:r,sections:p,onActionAnyItem:g}),o&&t.createElement("div",{className:Do.Details},o))}const zo=8;function Go({actions:e=[],groups:n=[],handleMeasurement:r}){const o=pe(),a=c.useRef(null),i={title:o.translate("Polaris.ActionMenu.Actions.moreActions")},l=t.createElement(We,{disclosure:!0},i.title),s=c.useCallback(()=>{if(!a.current)return;const p=a.current.offsetWidth,g=a.current.children,m=Array.from(g).map(P=>Math.ceil(P.getBoundingClientRect().width)+zo),v=m.pop()||0;r({containerWidth:p,disclosureWidth:v,hiddenActionsWidths:m})},[r]);c.useEffect(()=>{s()},[s,e,n]);const u=e.map(p=>{const{content:g,onAction:f,...m}=p;return t.createElement(We,Object.assign({key:g,onClick:f},m),g)}),d=n.map(p=>{const{title:g,icon:f}=p;return t.createElement(We,{key:g,disclosure:!0,icon:f},g)});return mn("resize",s),t.createElement("div",{className:rt.ActionsLayoutMeasurer,ref:a},u,d,l)}function Uo({actions:e,groups:n,onActionRollup:r}){const o=pe(),a=c.useRef(null),[i,l]=c.useState(void 0),[s,u]=c.useReducer((k,y)=>({...k,...y}),{disclosureWidth:0,containerWidth:1/0,actionsWidths:[],visibleActions:[],hiddenActions:[],visibleGroups:[],hiddenGroups:[],hasMeasured:!1}),{visibleActions:d,hiddenActions:p,visibleGroups:g,hiddenGroups:f,containerWidth:m,disclosureWidth:v,actionsWidths:P,hasMeasured:S}=s,A={title:o.translate("Polaris.ActionMenu.Actions.moreActions"),actions:[]},C=c.useCallback(k=>l(i?void 0:k),[i]),B=c.useCallback(()=>l(void 0),[]);c.useEffect(()=>{if(m===0)return;const{visibleActions:k,visibleGroups:y,hiddenActions:$,hiddenGroups:O}=en(e,n,v,P,m);u({visibleActions:k,visibleGroups:y,hiddenActions:$,hiddenGroups:O,hasMeasured:m!==1/0})},[m,v,e,n,P,u]);const T=c.useMemo(()=>e??[],[e]),M=c.useMemo(()=>n??[],[n]),x=T.filter((k,y)=>!!d.includes(y)).map(k=>{const{content:y,onAction:$,...O}=k;return t.createElement(We,Object.assign({key:y,onClick:$},O),y)}),b=(f.length>0||p.length>0?[...M,A]:[...M]).filter((k,y)=>{const $=M.length===0,O=g.includes(y),Q=k===A;return $?p.length>0:Q?!0:O}),I=p.map(k=>T[k]).filter(k=>k!=null),F=f.map(k=>M[k]).filter(k=>k!=null),G=b.map(k=>{const{title:y,actions:$,...O}=k,Q=k===A,te=[...I,...F],[ae,U]=te.reduce(([X,le],Y)=>(jo(Y)?le.push({title:Y.title,items:Y.actions.map(me=>({...me,disabled:Y.disabled||me.disabled}))}):X.push(Y),[X,le]),[[],[]]);return Q?t.createElement(nn,Object.assign({key:y,title:y,active:y===i,actions:[...ae,...$],sections:U},O,{onOpen:C,onClose:B})):t.createElement(nn,Object.assign({key:y,title:y,active:y===i,actions:$},O,{onOpen:C,onClose:B}))}),L=c.useCallback(k=>{const{hiddenActionsWidths:y,containerWidth:$,disclosureWidth:O}=k,{visibleActions:Q,hiddenActions:te,visibleGroups:ae,hiddenGroups:U}=en(T,M,O,y,$);if(r){const X=te.length>0||U.length>0;a.current!==X&&(r(X),a.current=X)}u({visibleActions:Q,hiddenActions:te,visibleGroups:ae,hiddenGroups:U,actionsWidths:y,containerWidth:$,disclosureWidth:O,hasMeasured:!0})},[T,M,r]),R=t.createElement(Go,{actions:e,groups:n,handleMeasurement:L});return t.createElement("div",{className:rt.ActionsLayoutOuter},R,t.createElement("div",{className:_(rt.ActionsLayout,!S&&rt["ActionsLayout--measuring"])},x,G))}function jo(e){return"title"in e}function Zo({actions:e=[],groups:n=[],rollup:r,rollupActionsLabel:o,onActionRollup:a}){if(e.length===0&&n.length===0)return null;const i=_(Xt.ActionMenu,r&&Xt.rollup),l=n.map(s=>qo(s));return t.createElement("div",{className:i},r?t.createElement($o,{accessibilityLabel:o,items:e,sections:l}):t.createElement(Uo,{actions:e,groups:n,onActionRollup:a}))}function Vo(e=[]){return e.length===0?!1:e.some(n=>n.actions.length>0)}function qo({title:e,actions:n,disabled:r}){return{title:e,items:n.map(o=>({...o,disabled:r||o.disabled}))}}var be={ButtonGroup:"Polaris-ButtonGroup",Item:"Polaris-ButtonGroup__Item","Item-plain":"Polaris-ButtonGroup__Item--plain",variantSegmented:"Polaris-ButtonGroup--variantSegmented","Item-focused":"Polaris-ButtonGroup__Item--focused",fullWidth:"Polaris-ButtonGroup--fullWidth",extraTight:"Polaris-ButtonGroup--extraTight",tight:"Polaris-ButtonGroup--tight",loose:"Polaris-ButtonGroup--loose",noWrap:"Polaris-ButtonGroup--noWrap"};function Qo({button:e}){const{value:n,setTrue:r,setFalse:o}=Ge(!1),a=_(be.Item,n&&be["Item-focused"],e.props.variant==="plain"&&be["Item-plain"]);return t.createElement("div",{className:a,onFocus:r,onBlur:o},e)}function Xo({children:e,gap:n,variant:r,fullWidth:o,connectedTop:a,noWrap:i}){const l=_(be.ButtonGroup,n&&be[n],r&&be[de("variant",r)],o&&be.fullWidth,i&&be.noWrap),s=Lo(e).map((u,d)=>t.createElement(Qo,{button:u,key:d}));return t.createElement("div",{className:l,"data-buttongroup-variant":r,"data-buttongroup-connected-top":a,"data-buttongroup-full-width":o,"data-buttongroup-no-wrap":i},s)}var Yo={Bleed:"Polaris-Bleed"};const Jo=({marginInline:e,marginBlock:n,marginBlockStart:r,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,children:l})=>{const s=m=>{const v=["marginInlineStart","marginInlineEnd"],P=["marginBlockStart","marginBlockEnd"],S={marginBlockStart:r,marginBlockEnd:o,marginInlineStart:a,marginInlineEnd:i,marginInline:e,marginBlock:n};if(S[m])return S[m];if(v.includes(m)&&e)return S.marginInline;if(P.includes(m)&&n)return S.marginBlock},u=s("marginBlockStart"),d=s("marginBlockEnd"),p=s("marginInlineStart"),g=s("marginInlineEnd"),f={...ue("bleed","margin-block-start","space",u),...ue("bleed","margin-block-end","space",d),...ue("bleed","margin-inline-start","space",p),...ue("bleed","margin-inline-end","space",g)};return t.createElement("div",{className:Yo.Bleed,style:xt(f)},l)};function Ko({backAction:e}){const{content:n}=e;return t.createElement(Ne,{key:n,url:"url"in e?e.url:void 0,onClick:"onAction"in e?e.onAction:void 0,onPointerDown:ln,icon:hn,accessibilityLabel:e.accessibilityLabel??n})}var Ie;(function(e){e.Input="INPUT",e.Textarea="TEXTAREA",e.Select="SELECT",e.ContentEditable="contenteditable"})(Ie||(Ie={}));function ea(){if(document==null||document.activeElement==null)return!1;const{tagName:e}=document.activeElement;return e===Ie.Input||e===Ie.Textarea||e===Ie.Select||document.activeElement.hasAttribute(Ie.ContentEditable)}var Ke={Pagination:"Polaris-Pagination",table:"Polaris-Pagination--table",TablePaginationActions:"Polaris-Pagination__TablePaginationActions"};function ta({hasNext:e,hasPrevious:n,nextURL:r,previousURL:o,onNext:a,onPrevious:i,nextTooltip:l,previousTooltip:s,nextKeys:u,previousKeys:d,accessibilityLabel:p,accessibilityLabels:g,label:f,type:m="page"}){const v=pe(),P=c.createRef(),S=p||v.translate("Polaris.Pagination.pagination"),A=(g==null?void 0:g.previous)||v.translate("Polaris.Pagination.previous"),C=(g==null?void 0:g.next)||v.translate("Polaris.Pagination.next"),B=t.createElement(Ne,{icon:gn,accessibilityLabel:A,url:o,onClick:i,disabled:!n,id:"previousURL"}),T=s&&n?t.createElement(Ue,{activatorWrapper:"span",content:s,preferredPosition:"below"},B):B,M=t.createElement(Ne,{icon:vn,accessibilityLabel:C,url:r,onClick:a,disabled:!e,id:"nextURL"}),x=l&&e?t.createElement(Ue,{activatorWrapper:"span",content:l,preferredPosition:"below"},M):M,h=i||on,b=d&&(o||i)&&n&&d.map(R=>t.createElement(je,{key:R,keyCode:R,handler:et(o?rn("previousURL",P):h)})),I=a||on,F=u&&(r||a)&&e&&u.map(R=>t.createElement(je,{key:R,keyCode:R,handler:et(r?rn("nextURL",P):I)}));if(m==="table"){const R=f?t.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},t.createElement(W,{as:"span",variant:"bodySm",fontWeight:"medium"},f)):null;return t.createElement("nav",{"aria-label":S,ref:P,className:_(Ke.Pagination,Ke.table)},b,F,t.createElement(z,{background:"bg-surface-secondary",paddingBlockStart:"150",paddingBlockEnd:"150",paddingInlineStart:"300",paddingInlineEnd:"200"},t.createElement(ze,{align:"center",blockAlign:"center"},t.createElement("div",{className:Ke.TablePaginationActions,"data-buttongroup-variant":"segmented"},t.createElement("div",null,T),R,t.createElement("div",null,x)))))}const G=e&&n?t.createElement("span",null,f):t.createElement(W,{tone:"subdued",as:"span"},f),L=f?t.createElement(z,{padding:"300",paddingBlockStart:"0",paddingBlockEnd:"0"},t.createElement("div",{"aria-live":"polite"},G)):null;return t.createElement("nav",{"aria-label":S,ref:P,className:Ke.Pagination},b,F,t.createElement(Xo,{variant:"segmented"},T,L,x))}function rn(e,n){return()=>{if(n.current==null)return;const r=n.current.querySelector(`#${e}`);r&&r.click()}}function et(e){return()=>{ea()||e()}}function on(){}function Dn(){const e=c.useContext(Or);if(!e)throw new Error("No mediaQuery was provided. Your application must be wrapped in an <AppProvider> component. See https://polaris.shopify.com/components/app-provider for implementation instructions.");return e}function $e(e){return!c.isValidElement(e)&&e!==void 0}function ot(e){return c.isValidElement(e)&&e!==void 0}var tt={Page:"Polaris-Page",fullWidth:"Polaris-Page--fullWidth",narrowWidth:"Polaris-Page--narrowWidth",Content:"Polaris-Page__Content"},j={TitleWrapper:"Polaris-Page-Header__TitleWrapper",TitleWrapperExpand:"Polaris-Page-Header__TitleWrapperExpand",BreadcrumbWrapper:"Polaris-Page-Header__BreadcrumbWrapper",PaginationWrapper:"Polaris-Page-Header__PaginationWrapper",PrimaryActionWrapper:"Polaris-Page-Header__PrimaryActionWrapper",Row:"Polaris-Page-Header__Row",mobileView:"Polaris-Page-Header--mobileView",RightAlign:"Polaris-Page-Header__RightAlign",noBreadcrumbs:"Polaris-Page-Header--noBreadcrumbs",AdditionalMetaData:"Polaris-Page-Header__AdditionalMetaData",Actions:"Polaris-Page-Header__Actions",longTitle:"Polaris-Page-Header--longTitle",mediumTitle:"Polaris-Page-Header--mediumTitle",isSingleRow:"Polaris-Page-Header--isSingleRow"},Te={Title:"Polaris-Header-Title",TitleWithSubtitle:"Polaris-Header-Title__TitleWithSubtitle",TitleWrapper:"Polaris-Header-Title__TitleWrapper",SubTitle:"Polaris-Header-Title__SubTitle",SubtitleCompact:"Polaris-Header-Title__SubtitleCompact",SubtitleMaxWidth:"Polaris-Header-Title__SubtitleMaxWidth"};function na({title:e,subtitle:n,titleMetadata:r,compactTitle:o,hasSubtitleMaxWidth:a}){const i=_(Te.Title,n&&Te.TitleWithSubtitle),l=e?t.createElement("h1",{className:i},t.createElement(W,{as:"span",variant:"headingLg",fontWeight:"bold"},e)):null,s=r?t.createElement(Jo,{marginBlock:"100"},r):null,u=t.createElement("div",{className:Te.TitleWrapper},l,s),d=n?t.createElement("div",{className:_(Te.SubTitle,o&&Te.SubtitleCompact,a&&Te.SubtitleMaxWidth)},t.createElement(W,{as:"p",variant:"bodySm",tone:"subdued"},n)):null;return t.createElement(t.Fragment,null,u,d)}const ra=20,oa=8,an=34;function aa({title:e,subtitle:n,pageReadyAccessibilityLabel:r,titleMetadata:o,additionalMetadata:a,titleHidden:i=!1,primaryAction:l,pagination:s,filterActions:u,backAction:d,secondaryActions:p=[],actionGroups:g=[],compactTitle:f=!1,onActionRollup:m}){const v=pe(),{isNavigationCollapsed:P}=Dn(),S=!l&&!s&&($e(p)&&!p.length||ot(p))&&!g.length,A=g.length>0||$e(p)&&p.length>0||ot(p),C=d?t.createElement("div",{className:j.BreadcrumbWrapper},t.createElement(z,{maxWidth:"100%",paddingInlineEnd:"100",printHidden:!0},t.createElement(Ko,{backAction:d}))):null,B=s&&!P?t.createElement("div",{className:j.PaginationWrapper},t.createElement(z,{printHidden:!0},t.createElement(ta,Object.assign({},s,{hasPrevious:s.hasPrevious,hasNext:s.hasNext})))):null,T=t.createElement("div",{className:_(j.TitleWrapper,!A&&j.TitleWrapperExpand)},t.createElement(na,{title:e,subtitle:n,titleMetadata:o,compactTitle:f,hasSubtitleMaxWidth:A})),M=r||e,x=M?t.createElement("div",{role:"status"},t.createElement(W,{visuallyHidden:!0,as:"p"},v.translate("Polaris.Page.Header.pageReadyAccessibilityLabel",{title:M}))):void 0,h=l?t.createElement(ia,{primaryAction:l}):null;let b=null;$e(p)&&(p.length>0||Vo(g))?b=t.createElement(Zo,{actions:p,groups:g,rollup:P,rollupActionsLabel:e?v.translate("Polaris.Page.Header.rollupActionsLabel",{title:e}):void 0,onActionRollup:m}):ot(p)&&(b=t.createElement(t.Fragment,null,p));const I=C||B?t.createElement(z,{printHidden:!0,paddingBlockEnd:"100",paddingInlineEnd:b&&P?"1000":void 0},t.createElement(ze,{gap:"400",align:"space-between",blockAlign:"center"},C,B)):null,F=a?t.createElement("div",{className:j.AdditionalMetaData},t.createElement(W,{tone:"subdued",as:"span",variant:"bodySm"},a)):null,G=_(S&&j.isSingleRow,I&&j.hasNavigation,b&&j.hasActionMenu,P&&j.mobileView,!d&&j.noBreadcrumbs,e&&e.length<an&&j.mediumTitle,e&&e.length>an&&j.longTitle),{slot1:L,slot2:R,slot3:k,slot4:y,slot5:$}=sa({actionMenuMarkup:b,additionalMetadataMarkup:F,breadcrumbMarkup:C,isNavigationCollapsed:P,pageTitleMarkup:T,paginationMarkup:B,primaryActionMarkup:h,title:e});return t.createElement(z,{position:"relative",paddingBlockStart:{xs:"400",md:"600"},paddingBlockEnd:{xs:"400",md:"600"},paddingInlineStart:{xs:"400",sm:"0"},paddingInlineEnd:{xs:"400",sm:"0"},visuallyHidden:i},x,t.createElement("div",{className:G},t.createElement(Ur,{filterActions:!!u},t.createElement(St,{condition:[L,R,k,y].some(nt)},t.createElement("div",{className:j.Row},L,R,t.createElement(St,{condition:[k,y].some(nt)},t.createElement("div",{className:j.RightAlign},t.createElement(Oo,{condition:[k,y].every(nt),wrapper:O=>t.createElement("div",{className:j.Actions},O)},k,y))))),t.createElement(St,{condition:[$].some(nt)},t.createElement("div",{className:j.Row},t.createElement(ze,{gap:"400"},$))))))}function ia({primaryAction:e}){const{isNavigationCollapsed:n}=Dn();let r;if($e(e)){const{primary:o,helpText:a}=e,i=o===void 0?!0:o,l=Sn(la(n,e),{variant:i?"primary":void 0});r=a?t.createElement(Ue,{content:a},l):l}else r=e;return t.createElement("div",{className:j.PrimaryActionWrapper},t.createElement(z,{printHidden:!0},r))}function la(e,n){let{content:r,accessibilityLabel:o}=n;const{icon:a}=n;return a==null?{...n,icon:void 0}:(e&&(o=o||r,r=void 0),{...n,content:r,accessibilityLabel:o,icon:a})}function nt(e){return e!=null}function sa({actionMenuMarkup:e,additionalMetadataMarkup:n,breadcrumbMarkup:r,isNavigationCollapsed:o,pageTitleMarkup:a,paginationMarkup:i,primaryActionMarkup:l,title:s}){const u={mobileCompact:{slots:{slot1:null,slot2:a,slot3:e,slot4:l,slot5:n},condition:o&&r==null&&s!=null&&s.length<=oa},mobileDefault:{slots:{slot1:r,slot2:a,slot3:e,slot4:l,slot5:n},condition:o},desktopCompact:{slots:{slot1:r,slot2:a,slot3:e,slot4:l,slot5:n},condition:!o&&i==null&&e==null&&s!=null&&s.length<=ra},desktopDefault:{slots:{slot1:r,slot2:a,slot3:t.createElement(t.Fragment,null,e,l),slot4:i,slot5:n},condition:!o}};return(Object.values(u).find(p=>p.condition)||u.desktopDefault).slots}function ha({children:e,fullWidth:n,narrowWidth:r,...o}){const a=_(tt.Page,n&&tt.fullWidth,r&&tt.narrowWidth),i=o.title!=null&&o.title!==""||o.subtitle!=null&&o.subtitle!==""||o.primaryAction!=null||o.secondaryActions!=null&&($e(o.secondaryActions)&&o.secondaryActions.length>0||ot(o.secondaryActions))||o.actionGroups!=null&&o.actionGroups.length>0||o.backAction!=null,l=_(!i&&tt.Content),s=i?t.createElement(aa,Object.assign({filterActions:!0},o)):null;return t.createElement("div",{className:a},s,t.createElement("div",{className:l},e))}export{Gr as B,ze as I,ha as P,ko as T,z as a,Rn as i,On as w};
