/**
 * Database Migration and Consistency Utilities
 * Handles database migrations, data consistency checks, and cleanup operations
 */

import db from "../db.server";

export interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

/**
 * Check database connection and basic functionality
 */
export async function checkDatabaseConnection(): Promise<MigrationResult> {
  try {
    console.log('🔍 Checking database connection...');
    
    // Try a simple query
    await db.$queryRaw`SELECT 1`;
    
    console.log('✅ Database connection successful');
    return {
      success: true,
      message: 'Database connection is working'
    };
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return {
      success: false,
      message: 'Database connection failed',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Migrate existing session data to include billing information
 */
export async function migrateBillingData(): Promise<MigrationResult> {
  try {
    console.log('🔄 Starting billing data migration...');
    
    // Get all sessions without billing subscriptions
    const sessionsWithoutBilling = await db.session.findMany({
      where: {
        billingSubscriptions: {
          none: {}
        }
      },
      select: {
        shop: true
      }
    });

    console.log(`📊 Found ${sessionsWithoutBilling.length} sessions without billing data`);

    let migratedCount = 0;
    
    for (const session of sessionsWithoutBilling) {
      try {
        // Create a default billing record for existing sessions
        await db.billingEvent.create({
          data: {
            shop: session.shop,
            eventType: 'migration_created',
            eventData: JSON.stringify({
              migratedAt: new Date().toISOString(),
              reason: 'Initial billing data migration'
            })
          }
        });
        
        migratedCount++;
      } catch (error) {
        console.error(`❌ Failed to migrate billing data for shop ${session.shop}:`, error);
      }
    }

    console.log(`✅ Migrated billing data for ${migratedCount} sessions`);
    
    return {
      success: true,
      message: `Successfully migrated billing data for ${migratedCount} sessions`,
      details: { migratedCount, totalSessions: sessionsWithoutBilling.length }
    };
  } catch (error) {
    console.error('❌ Billing data migration failed:', error);
    return {
      success: false,
      message: 'Billing data migration failed',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Clean up orphaned billing records
 */
export async function cleanupOrphanedBillingRecords(): Promise<MigrationResult> {
  try {
    console.log('🧹 Cleaning up orphaned billing records...');
    
    // Get all existing shop domains from sessions
    const existingSessions = await db.session.findMany({
      select: { shop: true }
    });
    const existingShops = existingSessions.map((s: any) => s.shop);

    // Find billing subscriptions without corresponding sessions
    const orphanedSubscriptions = await db.billingSubscription.findMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    // Find billing purchases without corresponding sessions
    const orphanedPurchases = await db.billingPurchase.findMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    // Find billing events without corresponding sessions
    const orphanedEvents = await db.billingEvent.findMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    console.log(`📊 Found ${orphanedSubscriptions.length} orphaned subscriptions, ${orphanedPurchases.length} orphaned purchases, ${orphanedEvents.length} orphaned events`);

    // Delete orphaned records
    const deletedSubscriptions = await db.billingSubscription.deleteMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    const deletedPurchases = await db.billingPurchase.deleteMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    const deletedEvents = await db.billingEvent.deleteMany({
      where: {
        shop: {
          notIn: existingShops
        }
      }
    });

    const totalDeleted = deletedSubscriptions.count + deletedPurchases.count + deletedEvents.count;

    console.log(`✅ Cleaned up ${totalDeleted} orphaned billing records`);
    
    return {
      success: true,
      message: `Successfully cleaned up ${totalDeleted} orphaned billing records`,
      details: {
        deletedSubscriptions: deletedSubscriptions.count,
        deletedPurchases: deletedPurchases.count,
        deletedEvents: deletedEvents.count
      }
    };
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    return {
      success: false,
      message: 'Cleanup failed',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Validate data consistency across billing tables
 */
export async function validateDataConsistency(): Promise<MigrationResult> {
  try {
    console.log('🔍 Validating data consistency...');
    
    const issues: string[] = [];

    // Check for subscriptions with invalid status
    const invalidSubscriptions = await db.billingSubscription.findMany({
      where: {
        status: {
          notIn: ['ACTIVE', 'PENDING', 'CANCELLED', 'EXPIRED']
        }
      }
    });

    if (invalidSubscriptions.length > 0) {
      issues.push(`Found ${invalidSubscriptions.length} subscriptions with invalid status`);
    }

    // Check for purchases with invalid status
    const invalidPurchases = await db.billingPurchase.findMany({
      where: {
        status: {
          notIn: ['PENDING', 'ACCEPTED', 'DECLINED']
        }
      }
    });

    if (invalidPurchases.length > 0) {
      issues.push(`Found ${invalidPurchases.length} purchases with invalid status`);
    }

    // Check for negative amounts
    const negativeAmountPurchases = await db.billingPurchase.findMany({
      where: {
        amount: {
          lt: 0
        }
      }
    });

    if (negativeAmountPurchases.length > 0) {
      issues.push(`Found ${negativeAmountPurchases.length} purchases with negative amounts`);
    }

    // Check for inconsistent product counts
    const inconsistentPurchases = await db.billingPurchase.findMany({
      where: {
        OR: [
          { productCount: { lte: 0 } },
          { productCount: { gt: 1000 } }
        ]
      }
    });

    if (inconsistentPurchases.length > 0) {
      issues.push(`Found ${inconsistentPurchases.length} purchases with invalid product counts`);
    }

    if (issues.length === 0) {
      console.log('✅ Data consistency validation passed');
      return {
        success: true,
        message: 'Data consistency validation passed'
      };
    } else {
      console.log('⚠️ Data consistency issues found:', issues);
      return {
        success: false,
        message: 'Data consistency issues found',
        details: { issues }
      };
    }
  } catch (error) {
    console.error('❌ Data consistency validation failed:', error);
    return {
      success: false,
      message: 'Data consistency validation failed',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Update billing statistics and aggregations
 */
export async function updateBillingStatistics(): Promise<MigrationResult> {
  try {
    console.log('📊 Updating billing statistics...');
    
    // Get all shops with billing activity
    const shopsWithBilling = await db.session.findMany({
      where: {
        OR: [
          { billingSubscriptions: { some: {} } },
          { billingPurchases: { some: {} } }
        ]
      },
      select: {
        shop: true,
        billingSubscriptions: {
          select: {
            status: true,
            priceAmount: true,
            createdAt: true
          }
        },
        billingPurchases: {
          select: {
            status: true,
            amount: true,
            productCount: true,
            createdAt: true
          }
        },
        billingUsage: {
          select: {
            productsOptimized: true,
            optimizationDate: true
          }
        }
      }
    });

    console.log(`📈 Processing statistics for ${shopsWithBilling.length} shops`);

    const statistics = {
      totalShops: shopsWithBilling.length,
      activeSubscriptions: 0,
      totalRevenue: 0,
      totalProductsOptimized: 0,
      averageOptimizationPerShop: 0
    };

    for (const shop of shopsWithBilling) {
      // Count active subscriptions
      const activeSubscriptions = shop.billingSubscriptions.filter(sub => sub.status === 'ACTIVE');
      statistics.activeSubscriptions += activeSubscriptions.length;

      // Calculate revenue
      const subscriptionRevenue = shop.billingSubscriptions
        .filter((sub: any) => sub.status === 'ACTIVE')
        .reduce((sum: number, sub: any) => sum + (sub.priceAmount || 0), 0);

      const purchaseRevenue = shop.billingPurchases
        .filter((purchase: any) => purchase.status === 'ACCEPTED')
        .reduce((sum: number, purchase: any) => sum + purchase.amount, 0);

      statistics.totalRevenue += subscriptionRevenue + purchaseRevenue;

      // Count optimized products
      const optimizedProducts = shop.billingUsage
        .reduce((sum: number, usage: any) => sum + usage.productsOptimized, 0);
      
      statistics.totalProductsOptimized += optimizedProducts;
    }

    statistics.averageOptimizationPerShop = statistics.totalShops > 0 
      ? Math.round(statistics.totalProductsOptimized / statistics.totalShops)
      : 0;

    console.log('📊 Billing statistics:', statistics);

    return {
      success: true,
      message: 'Billing statistics updated successfully',
      details: statistics
    };
  } catch (error) {
    console.error('❌ Statistics update failed:', error);
    return {
      success: false,
      message: 'Statistics update failed',
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Run all database maintenance tasks
 */
export async function runDatabaseMaintenance(): Promise<{ passed: number; failed: number; results: MigrationResult[] }> {
  console.log('🔧 Running comprehensive database maintenance...');
  
  const tasks = [
    { name: 'Database Connection', task: checkDatabaseConnection },
    { name: 'Billing Data Migration', task: migrateBillingData },
    { name: 'Cleanup Orphaned Records', task: cleanupOrphanedBillingRecords },
    { name: 'Data Consistency Validation', task: validateDataConsistency },
    { name: 'Update Statistics', task: updateBillingStatistics }
  ];

  const results: MigrationResult[] = [];
  let passed = 0;
  let failed = 0;

  for (const { name, task } of tasks) {
    console.log(`\n🔧 Running: ${name}`);
    
    try {
      const result = await task();
      results.push({ ...result, message: `${name}: ${result.message}` });
      
      if (result.success) {
        console.log(`✅ ${name}: PASSED - ${result.message}`);
        passed++;
      } else {
        console.log(`❌ ${name}: FAILED - ${result.message}`);
        if (result.error) console.log(`   Error: ${result.error}`);
        failed++;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(`❌ ${name}: ERROR - ${errorMessage}`);
      results.push({
        success: false,
        message: `${name}: Unexpected error`,
        error: errorMessage
      });
      failed++;
    }
  }

  console.log(`\n📊 Maintenance Summary: ${passed} passed, ${failed} failed`);
  
  return { passed, failed, results };
}
