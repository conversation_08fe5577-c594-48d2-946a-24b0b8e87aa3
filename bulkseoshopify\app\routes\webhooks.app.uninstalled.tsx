import type { ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import db from "../db.server";

export const action = async ({ request }: ActionFunctionArgs) => {
  try {
    const { shop, session, topic } = await authenticate.webhook(request);

    console.log(`📨 Received ${topic} webhook for ${shop}`);

    // Webhook requests can trigger multiple times and after an app has already been uninstalled.
    // If this webhook already ran, the session may have been deleted previously.
    if (session) {
      await db.session.deleteMany({ where: { shop } });
      console.log(`✅ Deleted sessions for uninstalled app: ${shop}`);
    } else {
      console.log(`⚠️ No session found for shop ${shop} - app may have been already uninstalled`);
    }

    console.log(`✅ App uninstalled webhook processed successfully for ${shop}`);
    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error('❌ App uninstalled webhook error:', error);
    return new Response("Error processing webhook", { status: 500 });
  }
};
