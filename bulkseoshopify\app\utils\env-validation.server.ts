/**
 * Environment Variable Validation Utility
 * Ensures all required environment variables are present and valid
 */

interface EnvironmentConfig {
  SHOPIFY_API_KEY: string;
  SHOPIFY_API_SECRET: string;
  SHOPIFY_APP_URL: string;
  SCOPES: string;
  DATABASE_URL: string;
  SESSION_SECRET: string;
  GEMINI_API_KEY?: string;
  NODE_ENV: 'development' | 'production' | 'test';
  BILLING_ENABLED: string;
  BILLING_TRIAL_DAYS: string;
  BILLING_CURRENCY: string;
  HOST?: string;
  SHOP_CUSTOM_DOMAIN?: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  config?: EnvironmentConfig;
}

class EnvironmentValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'EnvironmentValidationError';
  }
}

/**
 * Validates all required environment variables
 */
export function validateEnvironment(): EnvironmentConfig {
  const requiredVars = [
    'SHOPIFY_API_KEY',
    'SHOPIFY_API_SECRET', 
    'SHOPIFY_APP_URL',
    'SCOPES',
    'DATABASE_URL',
    'SESSION_SECRET'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new EnvironmentValidationError(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }

  // Validate SHOPIFY_APP_URL format
  const appUrl = process.env.SHOPIFY_APP_URL!;
  if (!appUrl.startsWith('https://')) {
    throw new EnvironmentValidationError(
      'SHOPIFY_APP_URL must be a valid HTTPS URL'
    );
  }

  // Validate URL format
  try {
    new URL(appUrl);
  } catch {
    throw new EnvironmentValidationError(
      'SHOPIFY_APP_URL must be a valid URL'
    );
  }

  // Validate basic scopes are present
  const scopes = process.env.SCOPES!;
  const requiredScopes = [
    'read_products',
    'write_products'
  ];

  const missingScopes = requiredScopes.filter(scope =>
    !scopes.includes(scope)
  );

  if (missingScopes.length > 0) {
    console.warn(
      `⚠️  Missing required scopes: ${missingScopes.join(', ')}`
    );
  }

  // Validate billing configuration
  const billingEnabled = process.env.BILLING_ENABLED || 'true';
  const trialDays = process.env.BILLING_TRIAL_DAYS || '1';
  const currency = process.env.BILLING_CURRENCY || 'USD';

  if (!['true', 'false'].includes(billingEnabled)) {
    throw new EnvironmentValidationError(
      'BILLING_ENABLED must be "true" or "false"'
    );
  }

  if (isNaN(parseInt(trialDays)) || parseInt(trialDays) < 0) {
    throw new EnvironmentValidationError(
      'BILLING_TRIAL_DAYS must be a non-negative number'
    );
  }

  if (!/^[A-Z]{3}$/.test(currency)) {
    throw new EnvironmentValidationError(
      'BILLING_CURRENCY must be a valid 3-letter currency code (e.g., USD)'
    );
  }

  return {
    SHOPIFY_API_KEY: process.env.SHOPIFY_API_KEY!,
    SHOPIFY_API_SECRET: process.env.SHOPIFY_API_SECRET!,
    SHOPIFY_APP_URL: appUrl,
    SCOPES: scopes,
    DATABASE_URL: process.env.DATABASE_URL!,
    SESSION_SECRET: process.env.SESSION_SECRET!,
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    NODE_ENV: (process.env.NODE_ENV as any) || 'development',
    BILLING_ENABLED: billingEnabled,
    BILLING_TRIAL_DAYS: trialDays,
    BILLING_CURRENCY: currency
  };
}

/**
 * Gets validated environment configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  try {
    return validateEnvironment();
  } catch (error) {
    console.error('❌ Environment validation failed:', error);
    throw error;
  }
}

/**
 * Enhanced validation with detailed feedback
 */
export function validateEnvironmentEnhanced(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    const config = validateEnvironment();

    // Additional production-specific validations
    if (config.NODE_ENV === 'production') {
      if (config.SHOPIFY_APP_URL.includes('localhost')) {
        errors.push('SHOPIFY_APP_URL should not use localhost in production');
      }

      if (config.SHOPIFY_APP_URL.includes('ngrok')) {
        warnings.push('Using ngrok URL in production - consider using a permanent domain');
      }

      if (config.SESSION_SECRET.length < 64) {
        warnings.push('SESSION_SECRET should be at least 64 characters in production');
      }

      // Check for weak session secrets
      const weakSecrets = [
        'your-session-secret-here',
        'default-secret',
        'fallback-secret',
        'super-secure-session-secret-key'
      ];

      if (weakSecrets.some(weak => config.SESSION_SECRET.includes(weak))) {
        errors.push('SESSION_SECRET appears to use a default or weak value. Generate a strong random secret.');
      }

      if (config.DATABASE_URL.includes('sqlite') || config.DATABASE_URL.includes('file:')) {
        warnings.push('Consider using PostgreSQL or MySQL for production instead of SQLite');
      }
    }

    // Check for development-specific issues
    if (config.NODE_ENV === 'development') {
      if (!config.HOST && !config.SHOPIFY_APP_URL.includes('localhost') && !config.SHOPIFY_APP_URL.includes('ngrok')) {
        warnings.push('Consider setting HOST for development with tunneling services');
      }
    }

    return {
      isValid: true,
      errors: [],
      warnings,
      config
    };
  } catch (error) {
    if (error instanceof EnvironmentValidationError) {
      errors.push(error.message);
    } else {
      errors.push('Unknown validation error occurred');
    }

    return {
      isValid: false,
      errors,
      warnings,
      config: undefined
    };
  }
}

/**
 * Get environment configuration with enhanced validation
 */
export function getEnvConfig(): EnvironmentConfig & {
  isDevelopment: boolean;
  isProduction: boolean;
  isTest: boolean;
  isBillingEnabled: boolean;
} {
  const result = validateEnvironmentEnhanced();

  if (!result.isValid) {
    console.error('❌ Environment validation failed:');
    result.errors.forEach(error => console.error(`  - ${error}`));
    throw new Error(`Environment validation failed: ${result.errors.join(', ')}`);
  }

  if (result.warnings.length > 0) {
    console.warn('⚠️ Environment validation warnings:');
    result.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }

  const config = result.config!;

  return {
    ...config,
    isDevelopment: config.NODE_ENV === 'development',
    isProduction: config.NODE_ENV === 'production',
    isTest: config.NODE_ENV === 'test',
    isBillingEnabled: config.BILLING_ENABLED === 'true'
  };
}

/**
 * Validates environment on app startup with enhanced feedback
 */
export function validateEnvironmentOnStartup(): void {
  const result = validateEnvironmentEnhanced();

  if (!result.isValid) {
    console.error('\n🚨 CRITICAL: Environment validation failed!');
    console.error('Please fix the following issues before starting the app:\n');
    result.errors.forEach((error, index) => {
      console.error(`${index + 1}. ${error}`);
    });
    console.error('\nRefer to the README.md for setup instructions.\n');
    process.exit(1);
  }

  if (result.warnings.length > 0) {
    console.warn('\n⚠️ Environment validation completed with warnings:');
    result.warnings.forEach((warning, index) => {
      console.warn(`${index + 1}. ${warning}`);
    });
    console.warn('');
  }

  const config = result.config!;
  console.log('✅ Environment validation completed successfully');
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🌐 App URL: ${config.SHOPIFY_APP_URL}`);
  console.log(`💰 Billing: ${config.BILLING_ENABLED === 'true' ? 'Enabled' : 'Disabled'}`);
  console.log(`🆓 Trial Days: ${config.BILLING_TRIAL_DAYS}`);
  console.log(`💱 Currency: ${config.BILLING_CURRENCY}`);
}
