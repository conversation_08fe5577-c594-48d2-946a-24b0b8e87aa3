/**
 * SEO Optimization Service
 * Handles SEO optimization with proper request deduplication, progress tracking, and error handling
 */

import db from "../db.server";
import { createError, logError, type ErrorContext } from "../utils/error-handling.server";
import { applyRateLimit, RATE_LIMITERS } from "../utils/rate-limiting.server";
import { createGraphQLService } from "./graphql.server";
import { GeminiService } from "./gemini.server";
import { getEnvironmentConfig } from "../utils/env-validation.server";

export interface SEOOptimizationRequest {
  id: string;
  shop: string;
  productIds: string[];
  settings: SEOOptimizationSettings;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: {
    currentBatch: number;
    totalBatches: number;
    processedProducts: number;
    totalProducts: number;
    successCount: number;
    errorCount: number;
  };
  results?: SEOOptimizationResult[];
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SEOOptimizationSettings {
  updateProductTitle: boolean;
  updateProductDescription: boolean;
  updateSeoFields: boolean;
  updateHandle: boolean;
  updateImageAlts: boolean;
  autoApply: boolean;
}

export interface SEOOptimizationResult {
  productId: string;
  originalProductTitle: string;
  optimizedProductTitle?: string;
  originalProductDescription: string;
  optimizedProductDescription?: string;
  originalSeoTitle: string;
  optimizedSeoTitle?: string;
  originalSeoDescription: string;
  optimizedSeoDescription?: string;
  originalHandle: string;
  optimizedHandle?: string;
  viralKeyword?: string;
  seoScore?: number;
  error?: string;
}

/**
 * SEO Optimization Service with proper state management
 */
export class SEOOptimizationService {
  private static instances: Map<string, SEOOptimizationService> = new Map();
  private admin: any;
  private shop: string;
  private requestStore: Map<string, SEOOptimizationRequest> = new Map();

  constructor(admin: any, shop: string) {
    this.admin = admin;
    this.shop = shop;
  }

  static getInstance(admin: any, shop: string): SEOOptimizationService {
    // Use shop as key to ensure one instance per shop
    if (!this.instances.has(shop)) {
      this.instances.set(shop, new SEOOptimizationService(admin, shop));
    }
    const instance = this.instances.get(shop)!;
    // Update admin in case it changed
    instance.admin = admin;
    return instance;
  }

  /**
   * Start SEO optimization with proper request deduplication
   */
  async startOptimization(
    productIds: string[],
    settings: SEOOptimizationSettings,
    context?: ErrorContext
  ): Promise<{ requestId: string; message: string }> {
    try {
      // Apply rate limiting
      await applyRateLimit(RATE_LIMITERS.SEO_OPTIMIZATION(this.shop), context);

      // Check for existing active requests
      const existingRequest = await this.getActiveRequest();
      if (existingRequest) {
        throw createError('SEO_OPTIMIZATION_IN_PROGRESS', {
          ...context,
          shop: this.shop,
          metadata: { existingRequestId: existingRequest.id }
        });
      }

      // Create new optimization request
      const requestId = `seo_${this.shop}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      const totalProducts = productIds.length;
      const totalBatches = Math.ceil(totalProducts / 10); // 10 products per batch

      const optimizationRequest: SEOOptimizationRequest = {
        id: requestId,
        shop: this.shop,
        productIds,
        settings,
        status: 'pending',
        progress: {
          currentBatch: 0,
          totalBatches,
          processedProducts: 0,
          totalProducts,
          successCount: 0,
          errorCount: 0
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Store request in database for persistence
      await this.storeOptimizationRequest(optimizationRequest);

      // Start processing asynchronously
      this.processOptimization(requestId).catch(error => {
        console.error(`❌ SEO optimization failed for request ${requestId}:`, error);
        this.updateRequestStatus(requestId, 'failed', error.message);
      });

      console.log(`🚀 Started SEO optimization request: ${requestId}`);
      return {
        requestId,
        message: `Started optimization for ${totalProducts} products`
      };

    } catch (error) {
      console.error('❌ Failed to start SEO optimization:', error);
      await logError(
        error instanceof Error ? error : new Error('SEO optimization start failed'),
        { ...context, shop: this.shop, action: 'start_seo_optimization' }
      );
      throw error;
    }
  }

  /**
   * Get optimization request status and progress
   */
  async getOptimizationStatus(requestId: string): Promise<SEOOptimizationRequest | null> {
    try {
      return await this.getOptimizationRequest(requestId);
    } catch (error) {
      console.error(`❌ Failed to get optimization status for ${requestId}:`, error);
      return null;
    }
  }

  /**
   * Process SEO optimization in batches
   */
  private async processOptimization(requestId: string): Promise<void> {
    try {
      const request = await this.getOptimizationRequest(requestId);
      if (!request) {
        throw new Error(`Optimization request ${requestId} not found`);
      }

      await this.updateRequestStatus(requestId, 'processing');

      const graphqlService = createGraphQLService(this.admin, this.shop);
      const envConfig = getEnvironmentConfig();
      const geminiService = new GeminiService(envConfig.GEMINI_API_KEY || '');
      const results: SEOOptimizationResult[] = [];

      // Process products in batches
      const batchSize = 10;
      for (let i = 0; i < request.productIds.length; i += batchSize) {
        const batchIds = request.productIds.slice(i, i + batchSize);
        const currentBatch = Math.floor(i / batchSize) + 1;

        console.log(`📦 Processing batch ${currentBatch}/${request.progress.totalBatches}`);

        try {
          // Fetch product data
          const products = await this.fetchProductsBatch(batchIds, graphqlService);

          // Optimize each product
          for (const product of products) {
            try {
              const result = await this.optimizeProduct(product, geminiService, request.settings);
              results.push(result);

              // Update progress
              await this.updateProgress(requestId, {
                currentBatch,
                processedProducts: results.length,
                successCount: results.filter(r => !r.error).length,
                errorCount: results.filter(r => r.error).length
              });

            } catch (error) {
              console.error(`❌ Failed to optimize product ${product.id}:`, error);
              results.push({
                productId: product.id,
                originalProductTitle: product.title,
                originalProductDescription: product.description,
                originalSeoTitle: product.seo?.title || '',
                originalSeoDescription: product.seo?.description || '',
                originalHandle: product.handle,
                error: error instanceof Error ? error.message : 'Optimization failed'
              });
            }
          }

          // Apply changes if auto-apply is enabled
          if (request.settings.autoApply) {
            await this.applyOptimizations(results.filter(r => !r.error), graphqlService);
          }

          // Small delay between batches
          if (i + batchSize < request.productIds.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

        } catch (error) {
          console.error(`❌ Failed to process batch ${currentBatch}:`, error);
          // Continue with next batch
        }
      }

      // Complete the optimization
      await this.completeOptimization(requestId, results);

    } catch (error) {
      console.error(`❌ SEO optimization processing failed for ${requestId}:`, error);
      await this.updateRequestStatus(requestId, 'failed', error instanceof Error ? error.message : 'Processing failed');
    }
  }

  /**
   * Fetch products in batch using GraphQL service
   */
  private async fetchProductsBatch(productIds: string[], graphqlService: any): Promise<any[]> {
    const operations = productIds.map((id) => ({
      query: `
        query getProduct {
          product(id: "gid://shopify/Product/${id}") {
            id
            title
            description
            productType
            vendor
            handle
            seo {
              title
              description
            }
            images(first: 5) {
              edges {
                node {
                  id
                  altText
                }
              }
            }
          }
        }
      `
    }));

    const results = await graphqlService.batchQuery(operations, 5);
    return results
      .map((result: any) => result.data?.product)
      .filter((product: any) => product !== null);
  }

  /**
   * Optimize a single product using Gemini
   */
  private async optimizeProduct(
    product: any,
    geminiService: GeminiService,
    settings: SEOOptimizationSettings
  ): Promise<SEOOptimizationResult> {
    const result: SEOOptimizationResult = {
      productId: product.id.replace("gid://shopify/Product/", ""),
      originalProductTitle: product.title,
      originalProductDescription: product.description || '',
      originalSeoTitle: product.seo?.title || '',
      originalSeoDescription: product.seo?.description || '',
      originalHandle: product.handle
    };

    try {
      // Use the comprehensive SEO optimization for a single product
      const optimizations = await geminiService.comprehensiveSeoOptimization([{
        id: product.id,
        title: product.title,
        description: product.description || '',
        type: product.productType || '',
        vendor: product.vendor || ''
      }], settings);

      const optimization = optimizations[0];

      if (settings.updateProductTitle && optimization.optimizedProductTitle) {
        result.optimizedProductTitle = optimization.optimizedProductTitle;
      }

      if (settings.updateProductDescription && optimization.optimizedProductDescription) {
        result.optimizedProductDescription = optimization.optimizedProductDescription;
      }

      if (settings.updateSeoFields) {
        if (optimization.optimizedSeoTitle) {
          result.optimizedSeoTitle = optimization.optimizedSeoTitle;
        }
        if (optimization.optimizedSeoDescription) {
          result.optimizedSeoDescription = optimization.optimizedSeoDescription;
        }
      }

      if (settings.updateHandle && optimization.optimizedHandle) {
        result.optimizedHandle = optimization.optimizedHandle;
      }

      result.viralKeyword = optimization.viralKeyword;
      result.seoScore = optimization.seoScore;

    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Optimization failed';
    }

    return result;
  }

  /**
   * Apply optimizations using GraphQL service
   */
  private async applyOptimizations(results: SEOOptimizationResult[], graphqlService: any): Promise<void> {
    const updates = results.map(result => ({
      id: `gid://shopify/Product/${result.productId}`,
      input: {
        ...(result.optimizedProductTitle && { title: result.optimizedProductTitle }),
        ...(result.optimizedProductDescription && { descriptionHtml: result.optimizedProductDescription }),
        ...(result.optimizedHandle && { handle: result.optimizedHandle }),
        ...((result.optimizedSeoTitle || result.optimizedSeoDescription) && {
          seo: {
            ...(result.optimizedSeoTitle && { title: result.optimizedSeoTitle }),
            ...(result.optimizedSeoDescription && { description: result.optimizedSeoDescription })
          }
        })
      }
    }));

    await graphqlService.updateProductsBatch(updates, 5);
  }

  // Database operations (simplified - would need proper implementation)
  private async storeOptimizationRequest(request: SEOOptimizationRequest): Promise<void> {
    // Store in memory for now (in production, use database)
    console.log(`💾 Storing optimization request: ${request.id}`);
    console.log(`📊 Current store size: ${this.requestStore.size}`);
    this.requestStore.set(request.id, request);
    console.log(`📊 Store size after adding: ${this.requestStore.size}`);
  }

  private async getOptimizationRequest(requestId: string): Promise<SEOOptimizationRequest | null> {
    // Retrieve from memory store
    console.log(`📖 Getting optimization request: ${requestId}`);
    console.log(`📊 Current store size: ${this.requestStore.size}`);
    console.log(`📊 Available request IDs: ${Array.from(this.requestStore.keys()).join(', ')}`);
    const request = this.requestStore.get(requestId);
    console.log(`📊 Found request: ${request ? 'YES' : 'NO'}`);
    return request || null;
  }

  private async getActiveRequest(): Promise<SEOOptimizationRequest | null> {
    // Check for active requests for this shop
    console.log(`🔍 Checking for active requests for shop: ${this.shop}`);
    return null; // Placeholder
  }

  private async updateRequestStatus(requestId: string, status: string, error?: string): Promise<void> {
    console.log(`📝 Updating request ${requestId} status to: ${status}`);
    const request = this.requestStore.get(requestId);
    if (request) {
      request.status = status as any;
      if (error) {
        request.error = error;
      }
      this.requestStore.set(requestId, request);
    }
  }

  private async updateProgress(requestId: string, progress: Partial<SEOOptimizationRequest['progress']>): Promise<void> {
    console.log(`📊 Updating progress for request ${requestId}:`, progress);
    const request = this.requestStore.get(requestId);
    if (request) {
      request.progress = { ...request.progress, ...progress };
      this.requestStore.set(requestId, request);
    }
  }

  private async completeOptimization(requestId: string, _results: SEOOptimizationResult[]): Promise<void> {
    console.log(`✅ Completing optimization request: ${requestId}`);
    await this.updateRequestStatus(requestId, 'completed');
  }
}

/**
 * Create SEO optimization service instance
 */
export function createSEOOptimizationService(admin: any, shop: string): SEOOptimizationService {
  return SEOOptimizationService.getInstance(admin, shop);
}
