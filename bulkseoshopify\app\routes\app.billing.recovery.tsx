import type { LoaderFunctionArgs } from "@remix-run/node";
import { redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { BillingService } from "../services/billing.server";
import { invalidateBillingCache } from "../utils/cache.server";
import db from "../db.server";
import type { Prisma } from "@prisma/client";

/**
 * Billing Recovery Route
 * Handles billing callback recovery when authentication fails during Shopify billing flow
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const url = new URL(request.url);
    const shopParam = url.searchParams.get("shop");
    const dataParam = url.searchParams.get("data");
    
    console.log(`🔄 Billing recovery initiated for shop: ${shopParam}`);
    
    if (!shopParam || !dataParam) {
      console.error("❌ Missing shop or data parameters in billing recovery");
      return redirect("/app/billing?error=invalid_recovery");
    }

    // Authenticate the request
    const { admin, session } = await authenticate.admin(request);
    
    if (!session?.shop || session.shop !== shopParam) {
      console.error(`❌ Shop mismatch in billing recovery. Expected: ${shopParam}, Got: ${session?.shop}`);
      return redirect("/app/billing?error=shop_mismatch");
    }

    // Parse the callback data
    let callbackData;
    try {
      callbackData = JSON.parse(decodeURIComponent(dataParam));
    } catch (parseError) {
      console.error("❌ Failed to parse callback data:", parseError);
      return redirect("/app/billing?error=invalid_data");
    }

    const { charge_id, purchase_id } = callbackData;
    console.log(`🔄 Processing recovered billing callback - charge_id: ${charge_id}, purchase_id: ${purchase_id}`);

    const billingService = new BillingService(admin, session.shop);

    if (charge_id) {
      // Handle subscription confirmation
      console.log(`✅ Processing subscription confirmation for shop ${session.shop}, charge_id: ${charge_id}`);
      
      // Get updated subscription data
      const subscriptionData = await billingService.getCurrentSubscription();
      const allSubscriptions = subscriptionData.data?.currentAppInstallation?.activeSubscriptions || [];
      
      // Find the subscription that matches our charge_id
      const subscription = allSubscriptions.find((sub: any) => 
        sub.id.includes(charge_id) || sub.id === charge_id
      ) || allSubscriptions[0]; // Fallback to first subscription if exact match not found

      if (subscription) {
        console.log(`📋 Found subscription: ${subscription.id}, status: ${subscription.status}`);

        // Use database transaction for consistency
        await db.$transaction(async (tx: Prisma.TransactionClient) => {
          // Store subscription details in database
          await tx.billingSubscription.upsert({
            where: { subscriptionId: subscription.id },
            update: {
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              updatedAt: new Date()
            },
            create: {
              shop: session.shop,
              subscriptionId: subscription.id,
              status: subscription.status,
              planId: determinePlanId(subscription),
              trialDays: subscription.trialDays || 0,
              trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
              currentPeriodEnd: subscription.currentPeriodEnd ? new Date(subscription.currentPeriodEnd) : null,
              priceAmount: getSubscriptionPrice(subscription),
              priceCurrency: 'USD'
            }
          });

          // Log billing event
          await tx.billingEvent.create({
            data: {
              shop: session.shop,
              eventType: 'subscription_confirmed_recovery',
              referenceId: subscription.id,
              eventData: JSON.stringify({
                chargeId: charge_id,
                subscriptionStatus: subscription.status,
                trialDays: subscription.trialDays,
                recoveryTimestamp: new Date().toISOString(),
                originalCallback: callbackData
              })
            }
          });

          // Update session with subscription info
          await tx.session.update({
            where: { shop: session.shop },
            data: {
              subscriptionId: subscription.id,
              subscriptionStatus: subscription.status,
              billingPlanId: determinePlanId(subscription),
              trialEndsAt: subscription.trialDays ? new Date(Date.now() + subscription.trialDays * 24 * 60 * 60 * 1000) : null,
              lastBillingCheck: new Date()
            }
          });

          console.log(`✅ Subscription recovery completed for shop ${session.shop}`);
        });

        // Invalidate billing cache to force refresh
        invalidateBillingCache(session.shop);
        console.log(`🔄 Billing cache invalidated for shop ${session.shop}`);

        // Set subscription update flag for other pages to detect the change
        const { setSubscriptionUpdateFlag } = await import("../utils/cache.server");
        setSubscriptionUpdateFlag(session.shop);
        console.log(`🚩 Subscription update flag set for shop ${session.shop}`);

        // Redirect back to billing page with success
        return redirect("/app/billing?success=subscription_recovered");
      } else {
        console.error(`❌ No subscription found for charge_id: ${charge_id}`);
        return redirect("/app/billing?error=subscription_not_found");
      }
    }

    if (purchase_id) {
      // Handle purchase confirmation (similar logic for one-time purchases)
      console.log(`✅ Processing purchase confirmation recovery for shop ${session.shop}, purchase_id: ${purchase_id}`);
      
      // For now, redirect to billing page - purchase recovery can be implemented if needed
      return redirect("/app/billing?success=purchase_recovered");
    }

    // If we get here, no valid callback data was found
    console.error(`❌ No valid callback data found in recovery`);
    return redirect("/app/billing?error=no_callback_data");

  } catch (error) {
    console.error('❌ Billing recovery error:', error);
    return redirect("/app/billing?error=recovery_failed");
  }
};

// Helper functions (same as in billing.callback.tsx)
function determinePlanId(subscription: any): string {
  if (!subscription.name) return 'unknown';
  
  const name = subscription.name.toLowerCase();
  if (name.includes('monthly')) return 'monthly';
  if (name.includes('annual') || name.includes('yearly')) return 'annual';
  return 'monthly'; // Default fallback
}

function getSubscriptionPrice(subscription: any): number {
  // Extract price from subscription data
  if (subscription.lineItems && subscription.lineItems.length > 0) {
    const lineItem = subscription.lineItems[0];
    if (lineItem.plan && lineItem.plan.pricingDetails) {
      return parseFloat(lineItem.plan.pricingDetails.price?.amount || '0');
    }
  }
  
  // Fallback based on plan type
  const planId = determinePlanId(subscription);
  switch (planId) {
    case 'monthly': return 29.99;
    case 'annual': return 299.99;
    default: return 0;
  }
}
