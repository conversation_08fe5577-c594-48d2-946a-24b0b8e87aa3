import{j as i}from"./jsx-runtime-0DLF9kdB.js";import{P as o}from"./PricingSelection-BaVbg180.js";import{c as r}from"./button-RlsfHhb_.js";import{m as s}from"./proxy-CdGEizrq.js";import{u as n}from"./components-C2v4lmCU.js";import{T as m}from"./TitleBar-DFMSJ8Yc.js";import"./index-BJYSoprK.js";import"./badge-CSt7rrAu.js";import"./index-DI88vBYx.js";import"./useAppBridge-Bj34gXAL.js";import"./index-Ci0627_k.js";function c({children:a,className:t}){return i.jsx("div",{className:r("bg-black min-h-screen py-16 px-6",t),children:i.jsx("div",{className:"max-w-7xl mx-auto",children:i.jsx(s.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{duration:.8,ease:"easeOut"},className:"space-y-12",children:a})})})}function v(){const{plans:a,currentPlan:t,csrfToken:e}=n();return i.jsxs(i.Fragment,{children:[i.jsx(m,{title:"Choose Your Plan"}),i.jsx(c,{children:i.jsx(o,{plans:a,selectedPlan:t==null?void 0:t.id,showPayPerUse:!0,csrfToken:e})})]})}export{v as default};
