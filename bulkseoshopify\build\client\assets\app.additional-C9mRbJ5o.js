import{j as e}from"./jsx-runtime-0DLF9kdB.js";import{a as S,P as T,B as f}from"./Page-Dli0YmGf.js";import{T as k}from"./TitleBar-DFMSJ8Yc.js";import{r as I,R as t}from"./index-BJYSoprK.js";import{c as p,v as g,T as l,U as M}from"./Button-3T1ZHLBP.js";import{C as j}from"./Card-u-CSbGqf.js";import"./use-is-after-initial-mount-MUb1fdtO.js";import"./context-Dt_50QHC.js";import"./index-Ci0627_k.js";const B=I.createContext(!1);var r={Layout:"Polaris-Layout",Section:"Polaris-Layout__Section","Section-fullWidth":"Polaris-Layout__Section--fullWidth","Section-oneHalf":"Polaris-Layout__Section--oneHalf","Section-oneThird":"Polaris-Layout__Section--oneThird",AnnotatedSection:"Polaris-Layout__AnnotatedSection",AnnotationWrapper:"Polaris-Layout__AnnotationWrapper",AnnotationContent:"Polaris-Layout__AnnotationContent",Annotation:"Polaris-Layout__Annotation"},E={TextContainer:"Polaris-TextContainer",spacingTight:"Polaris-TextContainer--spacingTight",spacingLoose:"Polaris-TextContainer--spacingLoose"};function U({spacing:a,children:o}){const n=p(E.TextContainer,a&&E[g("spacing",a)]);return t.createElement("div",{className:n},o)}function w({children:a,title:o,description:n,id:i}){const s=typeof n=="string"?t.createElement(l,{as:"p",variant:"bodyMd"},n):n;return t.createElement("div",{className:r.AnnotatedSection},t.createElement("div",{className:r.AnnotationWrapper},t.createElement("div",{className:r.Annotation},t.createElement(U,{spacing:"tight"},t.createElement(l,{id:i,variant:"headingMd",as:"h2"},o),s&&t.createElement(S,{color:"text-secondary"},s))),t.createElement("div",{className:r.AnnotationContent},a)))}function N({children:a,variant:o}){const n=p(r.Section,r[`Section-${o}`]);return t.createElement("div",{className:n},a)}const c=function({sectioned:o,children:n}){const i=o?t.createElement(N,null,n):n;return t.createElement("div",{className:r.Layout},i)};c.AnnotatedSection=w;c.Section=N;var u={Link:"Polaris-Link",monochrome:"Polaris-Link--monochrome",removeUnderline:"Polaris-Link--removeUnderline"};function P({url:a,children:o,onClick:n,external:i,target:s,id:d,monochrome:_,removeUnderline:b,accessibilityLabel:v,dataPrimaryLink:L}){return t.createElement(B.Consumer,null,A=>{const C=_||A,y=p(u.Link,C&&u.monochrome,b&&u.removeUnderline);return a?t.createElement(M,{onClick:n,className:y,url:a,external:i,target:s,id:d,"aria-label":v,"data-primary-link":L},o):t.createElement("button",{type:"button",onClick:n,className:y,id:d,"aria-label":v,"data-primary-link":L},o)})}var m={List:"Polaris-List",typeNumber:"Polaris-List--typeNumber",Item:"Polaris-List__Item",spacingLoose:"Polaris-List--spacingLoose"};function W({children:a}){return t.createElement("li",{className:m.Item},a)}const x=function({children:o,gap:n="loose",type:i="bullet"}){const s=p(m.List,n&&m[g("spacing",n)],i&&m[g("type",i)]),d=i==="bullet"?"ul":"ol";return t.createElement(d,{className:s},o)};x.Item=W;function K(){return e.jsxs(T,{children:[e.jsx(k,{title:"Additional page"}),e.jsxs(c,{children:[e.jsx(c.Section,{children:e.jsx(j,{children:e.jsxs(f,{gap:"300",children:[e.jsxs(l,{as:"p",variant:"bodyMd",children:["The app template comes with an additional page which demonstrates how to create multiple pages within app navigation using"," ",e.jsx(P,{url:"https://shopify.dev/docs/apps/tools/app-bridge",target:"_blank",removeUnderline:!0,children:"App Bridge"}),"."]}),e.jsxs(l,{as:"p",variant:"bodyMd",children:["To create your own page and have it show up in the app navigation, add a page inside ",e.jsx(h,{children:"app/routes"}),", and a link to it in the ",e.jsx(h,{children:"<NavMenu>"})," component found in ",e.jsx(h,{children:"app/routes/app.jsx"}),"."]})]})})}),e.jsx(c.Section,{variant:"oneThird",children:e.jsx(j,{children:e.jsxs(f,{gap:"200",children:[e.jsx(l,{as:"h2",variant:"headingMd",children:"Resources"}),e.jsx(x,{children:e.jsx(x.Item,{children:e.jsx(P,{url:"https://shopify.dev/docs/apps/design-guidelines/navigation#app-nav",target:"_blank",removeUnderline:!0,children:"App nav best practices"})})})]})})})]})]})}function h({children:a}){return e.jsx(S,{as:"span",padding:"025",paddingInlineStart:"100",paddingInlineEnd:"100",background:"bg-surface-active",borderWidth:"025",borderColor:"border",borderRadius:"100",children:e.jsx("code",{children:a})})}export{K as default};
