/**
 * Build Configuration Service
 * Manages build-time configuration, optimization, and deployment settings
 */

import { getEnvironmentConfig } from "./env-validation.server";

export interface BuildConfig {
  environment: 'development' | 'production' | 'test';
  version: string;
  buildTime: string;
  gitCommit?: string;
  gitBranch?: string;
  optimization: {
    minify: boolean;
    sourceMaps: boolean;
    bundleAnalysis: boolean;
    treeshaking: boolean;
    codesplitting: boolean;
  };
  features: {
    enableDevTools: boolean;
    enableDebugLogs: boolean;
    enablePerformanceMonitoring: boolean;
    enableErrorReporting: boolean;
    enableAnalytics: boolean;
  };
  security: {
    enableCSP: boolean;
    enableHSTS: boolean;
    enableCORS: boolean;
    allowedOrigins: string[];
  };
  performance: {
    enableCaching: boolean;
    cacheMaxAge: number;
    enableCompression: boolean;
    enableServiceWorker: boolean;
  };
  monitoring: {
    enableHealthChecks: boolean;
    enableMetrics: boolean;
    enableAlerts: boolean;
    logLevel: string;
  };
}

/**
 * Build configuration service
 */
export class BuildConfigService {
  private static instance: BuildConfigService;
  private config: BuildConfig;

  constructor() {
    this.config = this.generateBuildConfig();
  }

  static getInstance(): BuildConfigService {
    if (!BuildConfigService.instance) {
      BuildConfigService.instance = new BuildConfigService();
    }
    return BuildConfigService.instance;
  }

  /**
   * Get build configuration
   */
  getConfig(): BuildConfig {
    return { ...this.config };
  }

  /**
   * Get specific configuration section
   */
  getOptimizationConfig(): BuildConfig['optimization'] {
    return { ...this.config.optimization };
  }

  getFeaturesConfig(): BuildConfig['features'] {
    return { ...this.config.features };
  }

  getSecurityConfig(): BuildConfig['security'] {
    return { ...this.config.security };
  }

  getPerformanceConfig(): BuildConfig['performance'] {
    return { ...this.config.performance };
  }

  getMonitoringConfig(): BuildConfig['monitoring'] {
    return { ...this.config.monitoring };
  }

  /**
   * Check if feature is enabled
   */
  isFeatureEnabled(feature: keyof BuildConfig['features']): boolean {
    return this.config.features[feature];
  }

  /**
   * Check if in development mode
   */
  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }

  /**
   * Check if in production mode
   */
  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  /**
   * Check if in test mode
   */
  isTest(): boolean {
    return this.config.environment === 'test';
  }

  /**
   * Get build information
   */
  getBuildInfo(): {
    version: string;
    buildTime: string;
    gitCommit?: string;
    gitBranch?: string;
    environment: string;
  } {
    return {
      version: this.config.version,
      buildTime: this.config.buildTime,
      gitCommit: this.config.gitCommit,
      gitBranch: this.config.gitBranch,
      environment: this.config.environment
    };
  }

  /**
   * Generate Content Security Policy
   */
  generateCSP(): string {
    if (!this.config.security.enableCSP) {
      return '';
    }

    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.shopify.com https://js.stripe.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.shopify.com",
      "font-src 'self' https://fonts.gstatic.com https://cdn.shopify.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.shopify.com https://generativelanguage.googleapis.com",
      "frame-src 'self' https://js.stripe.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ];

    if (this.isDevelopment()) {
      // Allow localhost and development servers
      directives.push("connect-src 'self' ws://localhost:* http://localhost:* https://localhost:*");
    }

    return directives.join('; ');
  }

  /**
   * Generate security headers
   */
  generateSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {};

    if (this.config.security.enableCSP) {
      headers['Content-Security-Policy'] = this.generateCSP();
    }

    if (this.config.security.enableHSTS && this.isProduction()) {
      headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains';
    }

    headers['X-Content-Type-Options'] = 'nosniff';
    headers['X-Frame-Options'] = 'DENY';
    headers['X-XSS-Protection'] = '1; mode=block';
    headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';

    if (this.config.security.enableCORS) {
      headers['Access-Control-Allow-Origin'] = this.config.security.allowedOrigins.join(', ');
      headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
      headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With';
    }

    return headers;
  }

  /**
   * Generate cache headers
   */
  generateCacheHeaders(resourceType: 'static' | 'api' | 'page' = 'page'): Record<string, string> {
    if (!this.config.performance.enableCaching) {
      return {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      };
    }

    const headers: Record<string, string> = {};

    switch (resourceType) {
      case 'static':
        // Long cache for static assets
        headers['Cache-Control'] = `public, max-age=${30 * 24 * 60 * 60}, immutable`; // 30 days
        break;
      case 'api':
        // Short cache for API responses
        headers['Cache-Control'] = `private, max-age=${5 * 60}`; // 5 minutes
        break;
      case 'page':
        // Medium cache for pages
        headers['Cache-Control'] = `public, max-age=${this.config.performance.cacheMaxAge}`;
        break;
    }

    if (this.config.performance.enableCompression) {
      headers['Vary'] = 'Accept-Encoding';
    }

    return headers;
  }

  /**
   * Get webpack/build optimization settings
   */
  getBuildOptimizations(): {
    minify: boolean;
    sourceMaps: boolean;
    treeshaking: boolean;
    codesplitting: boolean;
    bundleAnalysis: boolean;
  } {
    return { ...this.config.optimization };
  }

  /**
   * Private methods
   */
  private generateBuildConfig(): BuildConfig {
    const envConfig = getEnvironmentConfig();
    const environment = envConfig.NODE_ENV;
    const isProduction = environment === 'production';
    const isDevelopment = environment === 'development';

    return {
      environment,
      version: this.getVersion(),
      buildTime: new Date().toISOString(),
      gitCommit: this.getGitCommit(),
      gitBranch: this.getGitBranch(),
      optimization: {
        minify: isProduction,
        sourceMaps: !isProduction,
        bundleAnalysis: process.env.ANALYZE_BUNDLE === 'true',
        treeshaking: isProduction,
        codesplitting: isProduction
      },
      features: {
        enableDevTools: isDevelopment,
        enableDebugLogs: isDevelopment || process.env.ENABLE_DEBUG_LOGS === 'true',
        enablePerformanceMonitoring: isProduction || process.env.ENABLE_PERFORMANCE_MONITORING === 'true',
        enableErrorReporting: isProduction || process.env.ENABLE_ERROR_REPORTING === 'true',
        enableAnalytics: isProduction && process.env.ENABLE_ANALYTICS !== 'false'
      },
      security: {
        enableCSP: isProduction || process.env.ENABLE_CSP === 'true',
        enableHSTS: isProduction,
        enableCORS: process.env.ENABLE_CORS === 'true',
        allowedOrigins: this.getAllowedOrigins()
      },
      performance: {
        enableCaching: isProduction || process.env.ENABLE_CACHING === 'true',
        cacheMaxAge: parseInt(process.env.CACHE_MAX_AGE || '3600'), // 1 hour default
        enableCompression: isProduction || process.env.ENABLE_COMPRESSION === 'true',
        enableServiceWorker: isProduction && process.env.ENABLE_SERVICE_WORKER !== 'false'
      },
      monitoring: {
        enableHealthChecks: isProduction || process.env.ENABLE_HEALTH_CHECKS === 'true',
        enableMetrics: isProduction || process.env.ENABLE_METRICS === 'true',
        enableAlerts: isProduction || process.env.ENABLE_ALERTS === 'true',
        logLevel: process.env.LOG_LEVEL || (isDevelopment ? 'debug' : 'info')
      }
    };
  }

  private getVersion(): string {
    try {
      // Try to read from package.json
      const packageJson = require('../../package.json');
      return packageJson.version || '1.0.0';
    } catch {
      return process.env.APP_VERSION || '1.0.0';
    }
  }

  private getGitCommit(): string | undefined {
    return process.env.GIT_COMMIT || process.env.VERCEL_GIT_COMMIT_SHA;
  }

  private getGitBranch(): string | undefined {
    return process.env.GIT_BRANCH || process.env.VERCEL_GIT_COMMIT_REF;
  }

  private getAllowedOrigins(): string[] {
    const envConfig = getEnvironmentConfig();
    const origins = [envConfig.SHOPIFY_APP_URL];
    
    if (process.env.ALLOWED_ORIGINS) {
      origins.push(...process.env.ALLOWED_ORIGINS.split(',').map(o => o.trim()));
    }

    return origins;
  }
}

/**
 * Middleware to add security headers
 */
export function addSecurityHeaders(response: Response): Response {
  const buildConfig = BuildConfigService.getInstance();
  const headers = buildConfig.generateSecurityHeaders();

  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

/**
 * Middleware to add cache headers
 */
export function addCacheHeaders(
  response: Response,
  resourceType: 'static' | 'api' | 'page' = 'page'
): Response {
  const buildConfig = BuildConfigService.getInstance();
  const headers = buildConfig.generateCacheHeaders(resourceType);

  Object.entries(headers).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

/**
 * Get build information for client
 */
export function getBuildInfoForClient(): {
  version: string;
  environment: string;
  buildTime: string;
  features: {
    enableDevTools: boolean;
    enableDebugLogs: boolean;
  };
} {
  const buildConfig = BuildConfigService.getInstance();
  const config = buildConfig.getConfig();

  return {
    version: config.version,
    environment: config.environment,
    buildTime: config.buildTime,
    features: {
      enableDevTools: config.features.enableDevTools,
      enableDebugLogs: config.features.enableDebugLogs
    }
  };
}

// Export singleton instance
export const buildConfigService = BuildConfigService.getInstance();

// Log build configuration on startup
console.log('🔧 Build Configuration:');
console.log(`  Environment: ${buildConfigService.getConfig().environment}`);
console.log(`  Version: ${buildConfigService.getConfig().version}`);
console.log(`  Build Time: ${buildConfigService.getConfig().buildTime}`);
if (buildConfigService.getConfig().gitCommit) {
  console.log(`  Git Commit: ${buildConfigService.getConfig().gitCommit}`);
}
if (buildConfigService.getConfig().gitBranch) {
  console.log(`  Git Branch: ${buildConfigService.getConfig().gitBranch}`);
}
