/**
 * BULLETPROOF AUTHENTICATION SYSTEM
 * Handles ALL authentication scenarios across the entire app
 * Fixes session persistence issues once and for all
 */

import { LoaderFunctionArgs, ActionFunctionArgs, redirect } from "@remix-run/node";
import { authenticate } from "../shopify.server";

export interface BulletproofAuth {
  admin: any;
  session: {
    id: string;
    shop: string;
    accessToken: string;
    expires?: Date;
    userId?: string;
    email?: string;
    isOnline: boolean;
    scope?: string;
  };
}

/**
 * Simple, reliable authentication - just use Shopify's authenticate.admin directly
 */
export async function bulletproofAuthenticate(request: Request): Promise<BulletproofAuth> {
  // Just use Shopify's authentication - it handles everything correctly
  return await authenticate.admin(request);
}

/**
 * Bulletproof loader wrapper - USE THIS FOR ALL LOADERS
 */
export function withBulletproofAuth<T>(
  loaderFn: (args: LoaderFunctionArgs & { auth: BulletproofAuth }) => Promise<T>
) {
  return async (args: LoaderFunctionArgs): Promise<T> => {
    const auth = await bulletproofAuthenticate(args.request);
    return await loaderFn({ ...args, auth });
  };
}

/**
 * Bulletproof action wrapper - USE THIS FOR ALL ACTIONS
 */
export function withBulletproofAction<T>(
  actionFn: (args: ActionFunctionArgs & { auth: BulletproofAuth }) => Promise<T>
) {
  return async (args: ActionFunctionArgs): Promise<T> => {
    const auth = await bulletproofAuthenticate(args.request);
    return await actionFn({ ...args, auth });
  };
}

/**
 * Simple auth wrapper for basic routes that just need authentication
 */
export function requireAuth(
  handlerFn: (args: LoaderFunctionArgs | ActionFunctionArgs) => Promise<any>
) {
  return async (args: LoaderFunctionArgs | ActionFunctionArgs) => {
    await bulletproofAuthenticate(args.request);
    return await handlerFn(args);
  };
}
