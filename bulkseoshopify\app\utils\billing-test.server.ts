/**
 * Billing System Test Utility
 * Tests all billing flows to ensure they work correctly
 */

import { BillingService } from "../services/billing.server";

export interface BillingTestResult {
  success: boolean;
  message: string;
  details?: any;
  error?: string;
}

export class BillingTester {
  private admin: any;
  private shop: string;
  private billingService: BillingService;

  constructor(admin: any, shop: string) {
    this.admin = admin;
    this.shop = shop;
    this.billingService = new BillingService(admin, shop);
  }

  /**
   * Test environment validation
   */
  async testEnvironmentValidation(): Promise<BillingTestResult> {
    try {
      console.log('🧪 Testing environment validation...');
      
      // This will throw if environment is invalid
      new BillingService(this.admin, this.shop);
      
      return {
        success: true,
        message: 'Environment validation passed'
      };
    } catch (error) {
      return {
        success: false,
        message: 'Environment validation failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test billing plans retrieval
   */
  async testBillingPlans(): Promise<BillingTestResult> {
    try {
      console.log('🧪 Testing billing plans retrieval...');
      
      const plans = this.billingService.getAllBillingPlans();
      
      if (!Array.isArray(plans) || plans.length === 0) {
        return {
          success: false,
          message: 'No billing plans found'
        };
      }

      // Validate plan structure
      for (const plan of plans) {
        if (!plan.id || !plan.name || typeof plan.price !== 'number') {
          return {
            success: false,
            message: `Invalid plan structure: ${plan.id}`,
            details: plan
          };
        }
      }
      
      return {
        success: true,
        message: `Found ${plans.length} valid billing plans`,
        details: plans.map(p => ({ id: p.id, name: p.name, price: p.price }))
      };
    } catch (error) {
      return {
        success: false,
        message: 'Billing plans test failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test current subscription retrieval
   */
  async testCurrentSubscription(): Promise<BillingTestResult> {
    try {
      console.log('🧪 Testing current subscription retrieval...');
      
      const subscriptionData = await this.billingService.getCurrentSubscription();
      
      if (!subscriptionData || !subscriptionData.data) {
        return {
          success: false,
          message: 'No subscription data returned'
        };
      }

      const activeSubscriptions = subscriptionData.data.currentAppInstallation?.activeSubscriptions || [];
      
      return {
        success: true,
        message: `Found ${activeSubscriptions.length} active subscriptions`,
        details: {
          subscriptionCount: activeSubscriptions.length,
          subscriptions: activeSubscriptions.map((sub: any) => ({
            id: sub.id,
            status: sub.status,
            trialDays: sub.trialDays
          }))
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Current subscription test failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test billing status check
   */
  async testBillingStatus(): Promise<BillingTestResult> {
    try {
      console.log('🧪 Testing billing status check...');
      
      const billingStatus = await this.billingService.hasActiveBilling();
      
      if (typeof billingStatus.hasAccess !== 'boolean') {
        return {
          success: false,
          message: 'Invalid billing status response'
        };
      }
      
      return {
        success: true,
        message: `Billing status check completed`,
        details: {
          hasAccess: billingStatus.hasAccess,
          planId: billingStatus.plan?.id,
          subscription: billingStatus.subscription
        }
      };
    } catch (error) {
      return {
        success: false,
        message: 'Billing status test failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test pay-per-use cost calculation
   */
  async testPayPerUseCost(): Promise<BillingTestResult> {
    try {
      console.log('🧪 Testing pay-per-use cost calculation...');
      
      const testCases = [
        { products: 1, expected: 0.10 },
        { products: 10, expected: 1.00 },
        { products: 100, expected: 10.00 }
      ];

      for (const testCase of testCases) {
        const cost = this.billingService.calculatePayPerUseCost(testCase.products);
        if (Math.abs(cost - testCase.expected) > 0.001) {
          return {
            success: false,
            message: `Cost calculation failed for ${testCase.products} products`,
            details: { expected: testCase.expected, actual: cost }
          };
        }
      }

      // Test error cases
      try {
        this.billingService.calculatePayPerUseCost(0);
        return {
          success: false,
          message: 'Should have thrown error for 0 products'
        };
      } catch {
        // Expected
      }

      try {
        this.billingService.calculatePayPerUseCost(1001);
        return {
          success: false,
          message: 'Should have thrown error for >1000 products'
        };
      } catch {
        // Expected
      }
      
      return {
        success: true,
        message: 'Pay-per-use cost calculation tests passed',
        details: testCases
      };
    } catch (error) {
      return {
        success: false,
        message: 'Pay-per-use cost test failed',
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Run all billing tests
   */
  async runAllTests(): Promise<{ passed: number; failed: number; results: BillingTestResult[] }> {
    console.log(`🧪 Running comprehensive billing tests for shop: ${this.shop}`);
    
    const tests = [
      { name: 'Environment Validation', test: () => this.testEnvironmentValidation() },
      { name: 'Billing Plans', test: () => this.testBillingPlans() },
      { name: 'Current Subscription', test: () => this.testCurrentSubscription() },
      { name: 'Billing Status', test: () => this.testBillingStatus() },
      { name: 'Pay-Per-Use Cost', test: () => this.testPayPerUseCost() }
    ];

    const results: BillingTestResult[] = [];
    let passed = 0;
    let failed = 0;

    for (const { name, test } of tests) {
      console.log(`\n🔬 Running test: ${name}`);
      
      try {
        const result = await test();
        results.push({ ...result, message: `${name}: ${result.message}` });
        
        if (result.success) {
          console.log(`✅ ${name}: PASSED - ${result.message}`);
          passed++;
        } else {
          console.log(`❌ ${name}: FAILED - ${result.message}`);
          if (result.error) console.log(`   Error: ${result.error}`);
          failed++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.log(`❌ ${name}: ERROR - ${errorMessage}`);
        results.push({
          success: false,
          message: `${name}: Unexpected error`,
          error: errorMessage
        });
        failed++;
      }
    }

    console.log(`\n📊 Test Summary: ${passed} passed, ${failed} failed`);
    
    return { passed, failed, results };
  }
}
